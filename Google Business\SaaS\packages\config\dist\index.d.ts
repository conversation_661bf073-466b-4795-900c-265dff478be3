export declare const config: {
    readonly app: {
        readonly env: any;
        readonly port: any;
        readonly apiBaseUrl: any;
        readonly frontendUrl: any;
        readonly enableSwagger: any;
        readonly enableCors: any;
        readonly corsOrigin: any;
    };
    readonly database: {
        readonly url: any;
        readonly maxConnections: 20;
        readonly connectionTimeout: 60000;
    };
    readonly redis: {
        readonly url: any;
        readonly ttl: 3600;
        readonly maxRetries: 3;
    };
    readonly ai: {
        readonly gemini: {
            readonly apiKey: any;
            readonly model: "gemini-pro";
            readonly maxTokens: 4096;
        };
        readonly openai: {
            readonly apiKey: any;
            readonly model: "gpt-4";
            readonly maxTokens: 4096;
        };
        readonly perplexity: {
            readonly apiKey: any;
            readonly model: "sonar-medium-online";
        };
    };
    readonly google: {
        readonly mapsApiKey: any;
        readonly staticMapsApiKey: any;
    };
    readonly email: {
        readonly host: any;
        readonly port: any;
        readonly user: any;
        readonly pass: any;
        readonly from: any;
    };
    readonly security: {
        readonly jwtSecret: any;
        readonly encryptionKey: any;
        readonly rateLimitWindowMs: any;
        readonly rateLimitMaxRequests: any;
    };
    readonly storage: {
        readonly uploadDir: any;
        readonly reportsDir: any;
        readonly maxFileSizeMB: any;
    };
    readonly scraping: {
        readonly delayMin: any;
        readonly delayMax: any;
        readonly timeout: any;
        readonly maxConcurrent: any;
        readonly useProxy: any;
    };
    readonly monitoring: {
        readonly logLevel: any;
        readonly sentryDsn: any;
        readonly enableMetrics: any;
    };
    readonly n8n: {
        readonly webhookUrl: any;
        readonly apiKey: any;
    };
};
export type Config = typeof config;
//# sourceMappingURL=index.d.ts.map