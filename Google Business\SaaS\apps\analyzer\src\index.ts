import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { config } from 'dotenv';
import winston from 'winston';
import { AIAnalyzer } from './engines/AIAnalyzer';
import { ScoreCalculator } from './scoring/ScoreCalculator';
import { InsightGenerator } from './insights/InsightGenerator';
import { RecommendationEngine } from './recommendations/RecommendationEngine';

// Load environment variables
config();

// Initialize logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Initialize services
const aiAnalyzer = new AIAnalyzer();
const scoreCalculator = new ScoreCalculator();
const insightGenerator = new InsightGenerator();
const recommendationEngine = new RecommendationEngine();

const app = express();
const PORT = process.env.ANALYZER_PORT || 3002;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      service: 'GMB Analyzer',
      environment: process.env.NODE_ENV || 'development'
    }
  });
});

// AI Analysis endpoint
app.post('/api/analyze/business', async (req, res) => {
  try {
    const { businessData, competitorData } = req.body;

    if (!businessData) {
      return res.status(400).json({
        success: false,
        error: { message: 'Business data is required', code: 'MISSING_DATA' }
      });
    }

    logger.info('Starting AI analysis for business', { 
      businessId: businessData.id,
      businessName: businessData.businessName 
    });

    // Perform AI analysis
    const analysisResult = await aiAnalyzer.analyzeBusinessProfile(businessData, competitorData);
    
    // Calculate scores
    const scoreBreakdown = scoreCalculator.calculateOverallScore({
      reviews: businessData.reviews || [],
      rankings: businessData.rankings || [],
      seoFactors: businessData,
      photos: businessData.photos || [],
      posts: businessData.posts || [],
      citations: businessData.citations || [],
      competitors: competitorData || []
    });
    
    // Generate insights
    const insights = await insightGenerator.generateInsights(analysisResult, scoreBreakdown);
    
    // Generate recommendations
    const recommendations = await recommendationEngine.generateRecommendations(
      analysisResult, 
      scoreBreakdown, 
      insights
    );

    res.json({
      success: true,
      data: {
        analysis: analysisResult,
        scores: scoreBreakdown,
        insights,
        recommendations,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error in AI analysis', { error: error.message, stack: error.stack });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to analyze business data', code: 'ANALYSIS_ERROR' }
    });
  }
});

// SEO Analysis endpoint
app.post('/api/analyze/seo', async (req, res) => {
  try {
    const { businessData } = req.body;

    if (!businessData) {
      return res.status(400).json({
        success: false,
        error: { message: 'Business data is required', code: 'MISSING_DATA' }
      });
    }

    const seoAnalysis = await aiAnalyzer.analyzeSEOFactors(businessData);

    res.json({
      success: true,
      data: seoAnalysis
    });

  } catch (error: any) {
    logger.error('Error in SEO analysis', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to analyze SEO factors', code: 'SEO_ANALYSIS_ERROR' }
    });
  }
});

// Review Sentiment Analysis endpoint
app.post('/api/analyze/sentiment', async (req, res) => {
  try {
    const { reviews } = req.body;

    if (!reviews || !Array.isArray(reviews)) {
      return res.status(400).json({
        success: false,
        error: { message: 'Reviews array is required', code: 'MISSING_DATA' }
      });
    }

    const sentimentAnalysis = await aiAnalyzer.analyzeReviewSentiment(reviews);

    res.json({
      success: true,
      data: sentimentAnalysis
    });

  } catch (error: any) {
    logger.error('Error in sentiment analysis', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to analyze review sentiment', code: 'SENTIMENT_ANALYSIS_ERROR' }
    });
  }
});

// Score calculation endpoint
app.post('/api/calculate/score', async (req, res) => {
  try {
    const { analysisData } = req.body;

    if (!analysisData) {
      return res.status(400).json({
        success: false,
        error: { message: 'Analysis data is required', code: 'MISSING_DATA' }
      });
    }

    const scoreBreakdown = scoreCalculator.calculateOverallScore(analysisData);

    res.json({
      success: true,
      data: scoreBreakdown
    });

  } catch (error: any) {
    logger.error('Error in score calculation', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to calculate scores', code: 'SCORE_CALCULATION_ERROR' }
    });
  }
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'GMB Analyzer API',
      version: '1.0.0',
      endpoints: {
        health: '/health',
        analyze: '/api/analyze/business',
        seo: '/api/analyze/seo',
        sentiment: '/api/analyze/sentiment',
        score: '/api/calculate/score'
      },
      description: 'AI-powered analysis engine for Google Business Profile audits'
    }
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', { error: err.message, stack: err.stack });
  res.status(500).json({
    success: false,
    error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  logger.info(`GMB Analyzer service started on port ${PORT}`);
  logger.info('Available endpoints:');
  logger.info('  GET  /health - Health check');
  logger.info('  POST /api/analyze/business - Full business analysis');
  logger.info('  POST /api/analyze/seo - SEO factor analysis');
  logger.info('  POST /api/analyze/sentiment - Review sentiment analysis');
  logger.info('  POST /api/calculate/score - Score calculation');
  logger.info('  GET  /api/docs - API documentation');
});

export default app;
