"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataValidationService = void 0;
const joi_1 = __importDefault(require("joi"));
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
class DataValidationService {
    gmbDataSchema;
    constructor() {
        this.initializeSchemas();
    }
    initializeSchemas() {
        this.gmbDataSchema = joi_1.default.object({
            businessName: joi_1.default.string().min(1).max(255).required(),
            address: joi_1.default.string().min(5).max(500).required(),
            phone: joi_1.default.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).optional(),
            website: joi_1.default.string().uri().optional(),
            rating: joi_1.default.number().min(0).max(5).precision(1).optional(),
            reviewCount: joi_1.default.number().integer().min(0).optional(),
            totalPhotos: joi_1.default.number().integer().min(0).optional(),
            totalPosts: joi_1.default.number().integer().min(0).optional(),
            hours: joi_1.default.object().optional(),
            coordinates: joi_1.default.object({
                latitude: joi_1.default.number().min(-90).max(90).required(),
                longitude: joi_1.default.number().min(-180).max(180).required(),
            }).optional(),
            recentReviews: joi_1.default.array().optional(),
            photos: joi_1.default.array().items(joi_1.default.string().uri()).optional(),
            posts: joi_1.default.array().optional(),
            description: joi_1.default.string().max(2000).optional(),
            attributes: joi_1.default.object().optional(),
            placeId: joi_1.default.string().optional(),
            googleUrl: joi_1.default.string().uri().optional(),
            rawData: joi_1.default.object().optional(),
            scrapedAt: joi_1.default.date().required(),
        });
    }
    async validateAndClean(data) {
        logger.debug('Starting data validation and cleaning', { businessName: data.businessName });
        const result = {
            isValid: false,
            errors: [],
            warnings: [],
            cleanedData: {},
            confidence: 0,
        };
        try {
            const { error, value, warning } = this.gmbDataSchema.validate(data, {
                abortEarly: false,
                stripUnknown: true,
                convert: true,
            });
            if (error) {
                result.errors = error.details.map(detail => detail.message);
            }
            if (warning) {
                result.warnings = warning.details.map(detail => detail.message);
            }
            result.cleanedData = await this.cleanData(value || data);
            await this.performCustomValidation(result);
            result.confidence = this.calculateConfidence(result.cleanedData, result.errors, result.warnings);
            result.isValid = result.errors.length === 0 && result.confidence >= 60;
            logger.info('Data validation completed', {
                businessName: data.businessName,
                isValid: result.isValid,
                confidence: result.confidence,
                errors: result.errors.length,
                warnings: result.warnings.length,
            });
            return result;
        }
        catch (error) {
            logger.error('Error during data validation', { error: error.message });
            result.errors.push(`Validation error: ${error.message}`);
            return result;
        }
    }
    async cleanData(data) {
        const cleaned = { ...data };
        if (cleaned.businessName) {
            cleaned.businessName = this.cleanBusinessName(cleaned.businessName);
        }
        if (cleaned.address) {
            cleaned.address = this.cleanAddress(cleaned.address);
        }
        if (cleaned.phone) {
            cleaned.phone = this.cleanPhoneNumber(cleaned.phone);
        }
        if (cleaned.website) {
            cleaned.website = this.cleanWebsiteUrl(cleaned.website);
        }
        if (cleaned.rating !== undefined) {
            cleaned.rating = this.cleanRating(cleaned.rating);
        }
        if (cleaned.reviewCount !== undefined) {
            cleaned.reviewCount = this.cleanReviewCount(cleaned.reviewCount);
        }
        if (cleaned.coordinates) {
            cleaned.coordinates = this.cleanCoordinates(cleaned.coordinates);
        }
        return cleaned;
    }
    cleanBusinessName(name) {
        return name
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s&.-]/g, '')
            .substring(0, 255);
    }
    cleanAddress(address) {
        return address
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/,\s*,/g, ',')
            .substring(0, 500);
    }
    cleanPhoneNumber(phone) {
        let cleaned = phone.replace(/[^\d+]/g, '');
        if (!cleaned.startsWith('+') && cleaned.length >= 10) {
            cleaned = '+1' + cleaned;
        }
        return cleaned;
    }
    cleanWebsiteUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.toString();
        }
        catch {
            if (!url.startsWith('http')) {
                url = 'https://' + url;
            }
            try {
                const urlObj = new URL(url);
                return urlObj.toString();
            }
            catch {
                return url;
            }
        }
    }
    cleanRating(rating) {
        const cleaned = Math.round(rating * 10) / 10;
        return Math.max(0, Math.min(5, cleaned));
    }
    cleanReviewCount(count) {
        return Math.max(0, Math.floor(count));
    }
    cleanCoordinates(coords) {
        if (!coords || typeof coords.latitude !== 'number' || typeof coords.longitude !== 'number') {
            return undefined;
        }
        const lat = Math.max(-90, Math.min(90, coords.latitude));
        const lng = Math.max(-180, Math.min(180, coords.longitude));
        return { latitude: lat, longitude: lng };
    }
    async performCustomValidation(result) {
        const data = result.cleanedData;
        if (data.businessName && data.businessName.length < 2) {
            result.warnings.push('Business name is very short');
        }
        if (data.rating && data.reviewCount !== undefined) {
            if (data.rating > 0 && data.reviewCount === 0) {
                result.warnings.push('Business has rating but no reviews');
            }
            if (data.rating === 0 && data.reviewCount > 0) {
                result.warnings.push('Business has reviews but no rating');
            }
        }
        if (data.address && !this.isValidAddressFormat(data.address)) {
            result.warnings.push('Address format may be incomplete');
        }
        if (data.phone && !this.isValidPhoneFormat(data.phone)) {
            result.warnings.push('Phone number format may be invalid');
        }
        if (data.website && !this.isValidWebsiteFormat(data.website)) {
            result.warnings.push('Website URL format may be invalid');
        }
        if (data.scrapedAt) {
            const hoursSinceScraped = (Date.now() - data.scrapedAt.getTime()) / (1000 * 60 * 60);
            if (hoursSinceScraped > 24) {
                result.warnings.push('Data is more than 24 hours old');
            }
        }
    }
    isValidAddressFormat(address) {
        return /\d/.test(address) && /[a-zA-Z]/.test(address) && address.length > 10;
    }
    isValidPhoneFormat(phone) {
        const digits = phone.replace(/\D/g, '');
        return digits.length >= 10 && digits.length <= 15;
    }
    isValidWebsiteFormat(website) {
        try {
            const url = new URL(website);
            return ['http:', 'https:'].includes(url.protocol);
        }
        catch {
            return false;
        }
    }
    calculateConfidence(data, errors, warnings) {
        let confidence = 100;
        confidence -= errors.length * 20;
        confidence -= warnings.length * 5;
        const completeness = this.calculateCompleteness(data);
        confidence = confidence * (completeness / 100);
        return Math.max(0, Math.min(100, Math.round(confidence)));
    }
    calculateCompleteness(data) {
        const requiredFields = ['businessName', 'address'];
        const optionalFields = ['phone', 'website', 'rating', 'reviewCount', 'coordinates'];
        let score = 0;
        let totalFields = requiredFields.length + optionalFields.length;
        for (const field of requiredFields) {
            if (data[field]) {
                score += 2;
            }
        }
        for (const field of optionalFields) {
            if (data[field]) {
                score += 1;
            }
        }
        return Math.round((score / (requiredFields.length * 2 + optionalFields.length)) * 100);
    }
    calculateDataQuality(data, validationResult) {
        const completeness = this.calculateCompleteness(data);
        const accuracy = validationResult.confidence;
        const consistency = this.calculateConsistency(data);
        const freshness = this.calculateFreshness(data.scrapedAt);
        const overall = Math.round((completeness + accuracy + consistency + freshness) / 4);
        return {
            completeness,
            accuracy,
            consistency,
            freshness,
            overall,
        };
    }
    calculateConsistency(data) {
        let score = 100;
        if (data.rating && data.reviewCount !== undefined) {
            if ((data.rating > 0 && data.reviewCount === 0) || (data.rating === 0 && data.reviewCount > 0)) {
                score -= 20;
            }
        }
        if (data.businessName && data.address) {
            const nameParts = data.businessName.toLowerCase().split(' ');
            const addressLower = data.address.toLowerCase();
            const nameInAddress = nameParts.some(part => part.length > 3 && addressLower.includes(part));
            if (nameInAddress) {
                score -= 10;
            }
        }
        return Math.max(0, score);
    }
    calculateFreshness(scrapedAt) {
        if (!scrapedAt)
            return 0;
        const hoursSinceScraped = (Date.now() - scrapedAt.getTime()) / (1000 * 60 * 60);
        if (hoursSinceScraped <= 1)
            return 100;
        if (hoursSinceScraped <= 6)
            return 90;
        if (hoursSinceScraped <= 24)
            return 80;
        if (hoursSinceScraped <= 72)
            return 60;
        if (hoursSinceScraped <= 168)
            return 40;
        return 20;
    }
}
exports.DataValidationService = DataValidationService;
//# sourceMappingURL=DataValidationService.js.map