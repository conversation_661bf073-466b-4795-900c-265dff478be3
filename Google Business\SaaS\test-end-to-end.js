const http = require('http');
const fs = require('fs');
const path = require('path');

// Comprehensive End-to-End Testing Suite
console.log('🚀 Phase 4 Testing & Verification - End-to-End Workflow Testing\n');

// Test scenarios with different business types
const testScenarios = [
  {
    name: "High-Performing Restaurant",
    businessData: {
      businessName: "Bella Vista Italian Bistro",
      address: "789 Gourmet Lane, San Francisco, CA 94102",
      phone: "******-555-0199",
      website: "https://bellavistabistro.com",
      reviews: Array.from({length: 150}, (_, i) => ({
        rating: Math.random() > 0.2 ? (Math.random() > 0.5 ? 5 : 4) : 3,
        text: `Great experience! ${i + 1}`,
        author: `Customer ${i + 1}`,
        date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })),
      photos: Array.from({length: 35}, (_, i) => ({
        type: ['exterior', 'interior', 'food', 'menu', 'team'][i % 5],
        url: `https://example.com/photo${i + 1}.jpg`,
        date: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })),
      posts: Array.from({length: 20}, (_, i) => ({
        content: `Weekly special announcement ${i + 1}`,
        date: new Date(Date.now() - i * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        type: 'update'
      })),
      rankings: [
        { keyword: "italian restaurant san francisco", position: 2, searchVolume: 2200 },
        { keyword: "best pasta sf", position: 1, searchVolume: 1800 },
        { keyword: "romantic dinner sf", position: 3, searchVolume: 1500 }
      ],
      seoFactors: {
        businessNameOptimized: true,
        descriptionComplete: true,
        categoriesSet: true,
        hoursComplete: true,
        websiteLinked: true,
        phoneVerified: true
      }
    },
    competitorData: [
      { businessName: "Tony's Pizza", rating: 4.1, reviewCount: 89, photoCount: 18, postCount: 5 },
      { businessName: "Mama Mia's", rating: 4.3, reviewCount: 120, photoCount: 25, postCount: 8 }
    ],
    expectedGrade: ['A', 'B'],
    expectedScore: [75, 100]
  },
  {
    name: "Struggling Service Business",
    businessData: {
      businessName: "Quick Fix Plumbing",
      address: "456 Industrial Blvd, Phoenix, AZ 85001",
      phone: "******-555-0177",
      website: "https://quickfixplumbing.com",
      reviews: Array.from({length: 12}, (_, i) => ({
        rating: Math.random() > 0.6 ? 2 : (Math.random() > 0.5 ? 3 : 4),
        text: `Service was okay ${i + 1}`,
        author: `Customer ${i + 1}`,
        date: new Date(Date.now() - Math.random() * 730 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })),
      photos: Array.from({length: 5}, (_, i) => ({
        type: 'exterior',
        url: `https://example.com/plumbing${i + 1}.jpg`,
        date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })),
      posts: [],
      rankings: [
        { keyword: "plumber phoenix", position: 15, searchVolume: 3200 },
        { keyword: "emergency plumbing az", position: 22, searchVolume: 1100 }
      ],
      seoFactors: {
        businessNameOptimized: false,
        descriptionComplete: false,
        categoriesSet: true,
        hoursComplete: false,
        websiteLinked: true,
        phoneVerified: true
      }
    },
    competitorData: [
      { businessName: "Phoenix Plumbing Pro", rating: 4.7, reviewCount: 245, photoCount: 30, postCount: 15 },
      { businessName: "AZ Drain Masters", rating: 4.5, reviewCount: 189, photoCount: 22, postCount: 12 }
    ],
    expectedGrade: ['D', 'F'],
    expectedScore: [0, 50]
  },
  {
    name: "Healthcare Practice",
    businessData: {
      businessName: "Downtown Dental Care",
      address: "123 Medical Plaza, Denver, CO 80202",
      phone: "+1-************",
      website: "https://downtowndental.com",
      reviews: Array.from({length: 85}, (_, i) => ({
        rating: Math.random() > 0.15 ? (Math.random() > 0.3 ? 5 : 4) : 3,
        text: `Professional dental care ${i + 1}`,
        author: `Patient ${i + 1}`,
        date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })),
      photos: Array.from({length: 18}, (_, i) => ({
        type: ['exterior', 'interior', 'team', 'equipment'][i % 4],
        url: `https://example.com/dental${i + 1}.jpg`,
        date: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })),
      posts: Array.from({length: 8}, (_, i) => ({
        content: `Dental health tip ${i + 1}`,
        date: new Date(Date.now() - i * 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        type: 'health_tip'
      })),
      rankings: [
        { keyword: "dentist denver", position: 5, searchVolume: 2800 },
        { keyword: "dental care downtown", position: 3, searchVolume: 1200 }
      ],
      seoFactors: {
        businessNameOptimized: true,
        descriptionComplete: true,
        categoriesSet: true,
        hoursComplete: true,
        websiteLinked: true,
        phoneVerified: true
      }
    },
    competitorData: [
      { businessName: "Mile High Dentistry", rating: 4.4, reviewCount: 156, photoCount: 28, postCount: 12 },
      { businessName: "Denver Smile Center", rating: 4.6, reviewCount: 203, photoCount: 35, postCount: 18 }
    ],
    expectedGrade: ['B', 'A'],
    expectedScore: [65, 85]
  }
];

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const responseData = res.headers['content-type']?.includes('application/json') 
            ? JSON.parse(body) 
            : body;
          resolve({ status: res.statusCode, data: responseData, headers: res.headers });
        } catch (error) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testEndToEndWorkflow() {
  const results = {
    scenarios: [],
    systemHealth: {},
    performance: {},
    errors: []
  };

  try {
    // 1. System Health Check
    console.log('1. 🏥 System Health Verification...');
    const healthChecks = await Promise.all([
      makeRequest({ hostname: 'localhost', port: 3001, path: '/health', method: 'GET' }).catch(e => ({ error: e.message, service: 'data-collector' })),
      makeRequest({ hostname: 'localhost', port: 3002, path: '/health', method: 'GET' }).catch(e => ({ error: e.message, service: 'analyzer' })),
      makeRequest({ hostname: 'localhost', port: 3003, path: '/health', method: 'GET' }).catch(e => ({ error: e.message, service: 'report-generator' }))
    ]);

    healthChecks.forEach((check, index) => {
      const services = ['Data Collector', 'AI Analyzer', 'Report Generator'];
      if (check.error) {
        console.log(`   ❌ ${services[index]}: ${check.error}`);
        results.systemHealth[services[index]] = 'unavailable';
      } else {
        console.log(`   ✅ ${services[index]}: ${check.status === 200 ? 'healthy' : 'issues'}`);
        results.systemHealth[services[index]] = check.status === 200 ? 'healthy' : 'issues';
      }
    });

    // 2. End-to-End Workflow Testing
    console.log('\n2. 🔄 End-to-End Workflow Testing...\n');

    for (let i = 0; i < testScenarios.length; i++) {
      const scenario = testScenarios[i];
      console.log(`   📊 Testing Scenario ${i + 1}: ${scenario.name}`);
      
      const scenarioResult = {
        name: scenario.name,
        success: false,
        performance: {},
        analysis: {},
        report: {},
        delivery: {},
        errors: []
      };

      try {
        // Step 1: Integrated Analysis & Report Generation
        const startTime = Date.now();
        console.log(`      🔍 Generating integrated analysis and report...`);
        
        const reportResponse = await makeRequest({
          hostname: 'localhost',
          port: 3003,
          path: '/api/generate/integrated-report',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, {
          businessData: scenario.businessData,
          competitorData: scenario.competitorData,
          options: { template: 'default', chartTypes: ['score', 'breakdown', 'competitive'] }
        });

        const reportTime = Date.now() - startTime;
        scenarioResult.performance.reportGeneration = reportTime;

        if (reportResponse.status === 200 && reportResponse.data.success) {
          const analysis = reportResponse.data.data.analysis;
          const report = reportResponse.data.data.report;
          
          scenarioResult.analysis = {
            overallScore: analysis.scores.overall,
            grade: analysis.scores.grade,
            insightsCount: analysis.insights.length,
            recommendationsCount: analysis.recommendations.length,
            overallHealth: analysis.overallHealth
          };

          scenarioResult.report = {
            reportId: report.reportId,
            format: report.format,
            size: report.size
          };

          // Validate score expectations
          const scoreInRange = analysis.scores.overall >= scenario.expectedScore[0] && 
                              analysis.scores.overall <= scenario.expectedScore[1];
          const gradeMatches = scenario.expectedGrade.includes(analysis.scores.grade);

          console.log(`         📈 Score: ${analysis.scores.overall}/100 (${analysis.scores.grade}) - ${scoreInRange ? '✅' : '⚠️'}`);
          console.log(`         💡 Insights: ${analysis.insights.length}, Recommendations: ${analysis.recommendations.length}`);
          console.log(`         ⏱️  Generation Time: ${reportTime}ms`);

          // Step 2: Report Portal Access
          console.log(`      🌐 Testing report portal access...`);
          const portalStartTime = Date.now();
          
          const portalResponse = await makeRequest({
            hostname: 'localhost',
            port: 3003,
            path: `/api/portal/${report.reportId}`,
            method: 'GET'
          });

          const portalTime = Date.now() - portalStartTime;
          scenarioResult.performance.portalAccess = portalTime;

          if (portalResponse.status === 200) {
            const hasBusinessName = typeof portalResponse.data === 'string' && 
                                   portalResponse.data.includes(scenario.businessData.businessName);
            console.log(`         🎨 Portal Access: ${portalTime}ms - ${hasBusinessName ? '✅' : '❌'} Business data present`);
          }

          // Step 3: Email Delivery Test
          console.log(`      📧 Testing email delivery...`);
          const emailStartTime = Date.now();
          
          const emailResponse = await makeRequest({
            hostname: 'localhost',
            port: 3003,
            path: '/api/deliver/email',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          }, {
            reportId: report.reportId,
            recipient: `test@${scenario.businessData.businessName.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`,
            subject: `GMB Audit Report - ${scenario.businessData.businessName}`,
            message: 'Your comprehensive audit report is attached.'
          });

          const emailTime = Date.now() - emailStartTime;
          scenarioResult.performance.emailDelivery = emailTime;

          if (emailResponse.status === 200 && emailResponse.data.success) {
            scenarioResult.delivery = {
              deliveryId: emailResponse.data.data.deliveryId,
              status: emailResponse.data.data.status,
              channel: 'email'
            };
            console.log(`         📬 Email Delivery: ${emailTime}ms - ✅ ${emailResponse.data.data.status}`);
          }

          scenarioResult.success = true;
          console.log(`      ✅ Scenario completed successfully\n`);

        } else {
          scenarioResult.errors.push(`Report generation failed: ${reportResponse.status} - ${JSON.stringify(reportResponse.data)}`);
          console.log(`      ❌ Report generation failed: ${reportResponse.status}\n`);
        }

      } catch (error) {
        scenarioResult.errors.push(error.message);
        console.log(`      ❌ Scenario failed: ${error.message}\n`);
      }

      results.scenarios.push(scenarioResult);
    }

    // 3. Performance Summary
    console.log('3. 📊 Performance Summary...');
    const successfulScenarios = results.scenarios.filter(s => s.success);
    
    if (successfulScenarios.length > 0) {
      const avgReportTime = successfulScenarios.reduce((sum, s) => sum + s.performance.reportGeneration, 0) / successfulScenarios.length;
      const avgPortalTime = successfulScenarios.reduce((sum, s) => sum + (s.performance.portalAccess || 0), 0) / successfulScenarios.length;
      const avgEmailTime = successfulScenarios.reduce((sum, s) => sum + (s.performance.emailDelivery || 0), 0) / successfulScenarios.length;

      results.performance = {
        averageReportGeneration: Math.round(avgReportTime),
        averagePortalAccess: Math.round(avgPortalTime),
        averageEmailDelivery: Math.round(avgEmailTime),
        successRate: (successfulScenarios.length / results.scenarios.length * 100).toFixed(1)
      };

      console.log(`   📈 Average Report Generation: ${results.performance.averageReportGeneration}ms`);
      console.log(`   🌐 Average Portal Access: ${results.performance.averagePortalAccess}ms`);
      console.log(`   📧 Average Email Delivery: ${results.performance.averageEmailDelivery}ms`);
      console.log(`   ✅ Success Rate: ${results.performance.successRate}%`);
    }

    // 4. Save Results
    const resultsPath = path.join(__dirname, 'end-to-end-test-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed results saved to: ${resultsPath}`);

    // 5. Final Assessment
    console.log('\n🎯 End-to-End Testing Assessment:');
    const healthyServices = Object.values(results.systemHealth).filter(status => status === 'healthy').length;
    const totalServices = Object.keys(results.systemHealth).length;
    
    console.log(`   🏥 System Health: ${healthyServices}/${totalServices} services healthy`);
    console.log(`   🔄 Workflow Success: ${successfulScenarios.length}/${results.scenarios.length} scenarios passed`);
    console.log(`   ⚡ Performance: ${results.performance.successRate}% success rate`);
    
    if (healthyServices === totalServices && successfulScenarios.length === results.scenarios.length) {
      console.log('\n   ✅ END-TO-END TESTING: PASSED - System is production ready!');
    } else {
      console.log('\n   ⚠️  END-TO-END TESTING: ISSUES DETECTED - Review required');
    }

  } catch (error) {
    console.error('❌ End-to-end testing failed:', error.message);
    results.errors.push(error.message);
  }

  return results;
}

// Run the comprehensive end-to-end test
testEndToEndWorkflow().then(results => {
  console.log('\n🏁 End-to-End Testing Complete!');
}).catch(error => {
  console.error('💥 Testing suite crashed:', error.message);
});
