{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["*"], "@gmb-audit/config": ["../../packages/config/src"], "@gmb-audit/database": ["../../packages/database/src"], "@gmb-audit/shared": ["../../packages/shared/src"], "@gmb-audit/types": ["../../packages/types/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}