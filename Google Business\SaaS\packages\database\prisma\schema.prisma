// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  businesses Business[]
  auditReports AuditReport[]

  @@map("users")
}

enum UserRole {
  ADMIN
  USER
  API_USER
}

// Core business entity
model Business {
  id          String  @id @default(cuid())
  name        String
  address     String
  city        String?
  state       String?
  country     String  @default("US")
  postalCode  String?
  phone       String?
  website     String?
  email       String?
  category    String?
  subcategory String?
  
  // Geographic coordinates
  latitude    Decimal? @db.Decimal(10, 8)
  longitude   Decimal? @db.Decimal(11, 8)
  
  // Google Business Profile data
  placeId     String?  @unique
  googleUrl   String?
  
  // Metadata
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String
  
  // Relations
  user         User           @relation(fields: [createdBy], references: [id])
  gmbData      GMBData[]
  auditReports AuditReport[]
  competitors  Competitor[]

  @@map("businesses")
  @@index([latitude, longitude])
  @@index([category])
  @@index([createdBy])
}

// Google My Business scraped data
model GMBData {
  id           String   @id @default(cuid())
  businessId   String
  
  // Basic information
  rating       Decimal? @db.Decimal(2, 1)
  reviewCount  Int?
  totalPhotos  Int?
  totalPosts   Int?
  
  // Business hours
  hours        Json?
  
  // Contact information
  phone        String?
  website      String?
  
  // Address and location
  address      String?
  coordinates  Json?
  
  // Reviews data
  recentReviews Json?
  reviewSummary Json?
  
  // Photos and media
  photos       Json?
  
  // Posts and updates
  posts        Json?
  
  // SEO factors
  description  String?
  attributes   Json?
  
  // Raw scraped data
  rawData      Json?
  
  // Metadata
  scrapedAt    DateTime @default(now())
  dataVersion  String   @default("1.0")
  
  // Relations
  business     Business @relation(fields: [businessId], references: [id], onDelete: Cascade)

  @@map("gmb_data")
  @@index([businessId])
  @@index([scrapedAt])
}

// Competitor analysis
model Competitor {
  id           String  @id @default(cuid())
  businessId   String
  
  // Competitor information
  name         String
  address      String?
  rating       Decimal? @db.Decimal(2, 1)
  reviewCount  Int?
  distance     Decimal? @db.Decimal(8, 2) // Distance in miles/km
  
  // Ranking information
  localRanking Int?
  mapRanking   Int?
  
  // Geographic data
  latitude     Decimal? @db.Decimal(10, 8)
  longitude    Decimal? @db.Decimal(11, 8)
  
  // Additional data
  website      String?
  phone        String?
  category     String?
  
  // Analysis data
  analysisData Json?
  
  // Metadata
  discoveredAt DateTime @default(now())
  lastUpdated  DateTime @updatedAt
  
  // Relations
  business     Business @relation(fields: [businessId], references: [id], onDelete: Cascade)

  @@map("competitors")
  @@index([businessId])
  @@index([localRanking])
}

// Audit reports
model AuditReport {
  id             String      @id @default(cuid())
  businessId     String
  userId         String
  
  // Report metadata
  title          String
  description    String?
  reportType     ReportType  @default(COMPREHENSIVE)
  status         ReportStatus @default(PENDING)
  
  // Scoring
  overallScore   Int?
  seoScore       Int?
  visibilityScore Int?
  reviewScore    Int?
  photoScore     Int?
  postScore      Int?
  napScore       Int?
  
  // Report data
  analysisData   Json?
  recommendations Json?
  visualizations Json?
  
  // File paths
  pdfPath        String?
  htmlPath       String?
  
  // Processing metadata
  processingStarted DateTime?
  processingCompleted DateTime?
  processingDuration Int? // in seconds
  
  // Delivery information
  deliveryMethod DeliveryMethod?
  deliveredAt    DateTime?
  deliveryStatus DeliveryStatus @default(PENDING)
  
  // Metadata
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  // Relations
  business       Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  user           User     @relation(fields: [userId], references: [id])

  @@map("audit_reports")
  @@index([businessId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

enum ReportType {
  BASIC
  COMPREHENSIVE
  COMPETITOR_ANALYSIS
  CUSTOM
}

enum ReportStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum DeliveryMethod {
  EMAIL
  WHATSAPP
  DOWNLOAD
  API
}

enum DeliveryStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
}

// System monitoring and analytics
model SystemMetrics {
  id          String   @id @default(cuid())
  metricType  String
  metricName  String
  value       Decimal  @db.Decimal(15, 4)
  unit        String?
  tags        Json?
  timestamp   DateTime @default(now())

  @@map("system_metrics")
  @@index([metricType, metricName])
  @@index([timestamp])
}

// API usage tracking
model ApiUsage {
  id          String   @id @default(cuid())
  userId      String?
  endpoint    String
  method      String
  statusCode  Int
  responseTime Int // in milliseconds
  userAgent   String?
  ipAddress   String?
  timestamp   DateTime @default(now())

  @@map("api_usage")
  @@index([userId])
  @@index([endpoint])
  @@index([timestamp])
}
