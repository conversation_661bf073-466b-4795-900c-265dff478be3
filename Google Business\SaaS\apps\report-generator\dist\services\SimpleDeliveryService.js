"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleDeliveryService = void 0;
const uuid_1 = require("uuid");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class SimpleDeliveryService {
    deliveryHistory = new Map();
    emailTemplatesDir;
    constructor() {
        this.emailTemplatesDir = path.join(__dirname, '../templates/email');
    }
    async sendEmailReport(request) {
        const deliveryId = (0, uuid_1.v4)();
        try {
            if (!this.isValidEmail(request.recipient)) {
                throw new Error('Invalid email address format');
            }
            const emailContent = await this.generateEmailContent(request);
            const response = {
                deliveryId,
                type: 'email',
                status: 'sent',
                recipient: request.recipient,
                timestamp: new Date().toISOString(),
                trackingInfo: {
                    subject: request.subject || 'Your GMB Audit Report',
                    contentLength: emailContent.length,
                    attachments: request.attachPDF ? ['report.pdf'] : [],
                    provider: 'smtp_simulation'
                }
            };
            this.deliveryHistory.set(deliveryId, response);
            return response;
        }
        catch (error) {
            const failedResponse = {
                deliveryId,
                type: 'email',
                status: 'failed',
                recipient: request.recipient,
                timestamp: new Date().toISOString(),
                trackingInfo: {
                    error: error instanceof Error ? error.message : String(error)
                }
            };
            this.deliveryHistory.set(deliveryId, failedResponse);
            throw new Error(`Email delivery failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async sendWhatsAppReport(request) {
        const deliveryId = (0, uuid_1.v4)();
        try {
            if (!this.isValidPhoneNumber(request.phoneNumber)) {
                throw new Error('Invalid phone number format');
            }
            const message = this.generateWhatsAppMessage(request);
            const response = {
                deliveryId,
                type: 'whatsapp',
                status: 'sent',
                recipient: request.phoneNumber,
                timestamp: new Date().toISOString(),
                trackingInfo: {
                    messageLength: message.length,
                    includeLink: request.includeLink || false,
                    provider: 'twilio_simulation'
                }
            };
            this.deliveryHistory.set(deliveryId, response);
            return response;
        }
        catch (error) {
            const failedResponse = {
                deliveryId,
                type: 'whatsapp',
                status: 'failed',
                recipient: request.phoneNumber,
                timestamp: new Date().toISOString(),
                trackingInfo: {
                    error: error instanceof Error ? error.message : String(error)
                }
            };
            this.deliveryHistory.set(deliveryId, failedResponse);
            throw new Error(`WhatsApp delivery failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async sendMultiChannelReport(emailRequest, whatsappRequest) {
        const results = [];
        if (emailRequest) {
            try {
                const emailResult = await this.sendEmailReport(emailRequest);
                results.push(emailResult);
            }
            catch (error) {
                console.error('Email delivery failed:', error instanceof Error ? error.message : String(error));
            }
        }
        if (whatsappRequest) {
            try {
                const whatsappResult = await this.sendWhatsAppReport(whatsappRequest);
                results.push(whatsappResult);
            }
            catch (error) {
                console.error('WhatsApp delivery failed:', error instanceof Error ? error.message : String(error));
            }
        }
        return results;
    }
    async generateEmailContent(request) {
        try {
            const templatePath = path.join(this.emailTemplatesDir, 'default.html');
            let template;
            if (fs.existsSync(templatePath)) {
                template = fs.readFileSync(templatePath, 'utf-8');
            }
            else {
                template = this.getDefaultEmailTemplate();
            }
            const content = template
                .replace(/{{reportId}}/g, request.reportId)
                .replace(/{{recipient}}/g, request.recipient)
                .replace(/{{subject}}/g, request.subject || 'Your GMB Audit Report')
                .replace(/{{message}}/g, request.message || 'Please find your Google Business Profile audit report attached.')
                .replace(/{{portalLink}}/g, `${process.env.BASE_URL || 'http://localhost:3003'}/api/portal/${request.reportId}`)
                .replace(/{{downloadLink}}/g, `${process.env.BASE_URL || 'http://localhost:3003'}/api/download/${request.reportId}`)
                .replace(/{{timestamp}}/g, new Date().toLocaleDateString());
            return content;
        }
        catch (error) {
            throw new Error(`Failed to generate email content: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    generateWhatsAppMessage(request) {
        const baseUrl = process.env.BASE_URL || 'http://localhost:3003';
        const portalLink = `${baseUrl}/api/portal/${request.reportId}`;
        let message = request.message || '🏢 Your Google Business Profile Audit Report is ready!';
        if (request.includeLink !== false) {
            message += `\n\n📊 View your report: ${portalLink}`;
            message += `\n📥 Download PDF: ${baseUrl}/api/download/${request.reportId}`;
        }
        message += '\n\n✨ Generated by GMB Audit Report Generator';
        return message;
    }
    getDefaultEmailTemplate() {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{subject}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #3b82f6; color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 GMB Audit Report</h1>
            <p>Your Google Business Profile analysis is complete</p>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>{{message}}</p>
            <p>You can access your report in the following ways:</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{portalLink}}" class="button">📊 View Online</a>
                <a href="{{downloadLink}}" class="button">📥 Download PDF</a>
            </div>
            <p><strong>Report ID:</strong> {{reportId}}</p>
            <p><strong>Generated:</strong> {{timestamp}}</p>
        </div>
        <div class="footer">
            <p>Generated by GMB Audit Report Generator</p>
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>`;
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    isValidPhoneNumber(phone) {
        const phoneRegex = /^\+?[1-9]\d{1,14}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
    getDeliveryStatus(deliveryId) {
        return this.deliveryHistory.get(deliveryId);
    }
    getDeliveryStats() {
        const deliveries = Array.from(this.deliveryHistory.values());
        return {
            totalDeliveries: deliveries.length,
            emailDeliveries: deliveries.filter(d => d.type === 'email').length,
            whatsappDeliveries: deliveries.filter(d => d.type === 'whatsapp').length,
            successfulDeliveries: deliveries.filter(d => d.status === 'sent').length,
            failedDeliveries: deliveries.filter(d => d.status === 'failed').length
        };
    }
    getDeliveryHistory(limit = 50) {
        const deliveries = Array.from(this.deliveryHistory.values());
        return deliveries
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
            .slice(0, limit);
    }
    clearHistory() {
        this.deliveryHistory.clear();
    }
}
exports.SimpleDeliveryService = SimpleDeliveryService;
exports.default = SimpleDeliveryService;
//# sourceMappingURL=SimpleDeliveryService.js.map