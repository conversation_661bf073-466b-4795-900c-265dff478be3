import Joi from 'joi';
import { GMBData } from './GMBScrapingService';

// Simple logger for Phase 2
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  cleanedData: Partial<GMBData>;
  confidence: number; // 0-100 confidence score
}

export interface DataQualityMetrics {
  completeness: number; // Percentage of fields populated
  accuracy: number; // Estimated accuracy based on validation rules
  consistency: number; // Internal consistency checks
  freshness: number; // How recent the data is
  overall: number; // Overall quality score
}

export class DataValidationService {
  private gmbDataSchema!: Joi.ObjectSchema;

  constructor() {
    this.initializeSchemas();
  }

  private initializeSchemas(): void {
    this.gmbDataSchema = Joi.object({
      businessName: Joi.string().min(1).max(255).required(),
      address: Joi.string().min(5).max(500).required(),
      phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).optional(),
      website: Joi.string().uri().optional(),
      rating: Joi.number().min(0).max(5).precision(1).optional(),
      reviewCount: Joi.number().integer().min(0).optional(),
      totalPhotos: Joi.number().integer().min(0).optional(),
      totalPosts: Joi.number().integer().min(0).optional(),
      hours: Joi.object().optional(),
      coordinates: Joi.object({
        latitude: Joi.number().min(-90).max(90).required(),
        longitude: Joi.number().min(-180).max(180).required(),
      }).optional(),
      recentReviews: Joi.array().optional(),
      photos: Joi.array().items(Joi.string().uri()).optional(),
      posts: Joi.array().optional(),
      description: Joi.string().max(2000).optional(),
      attributes: Joi.object().optional(),
      placeId: Joi.string().optional(),
      googleUrl: Joi.string().uri().optional(),
      rawData: Joi.object().optional(),
      scrapedAt: Joi.date().required(),
    });
  }

  public async validateAndClean(data: Partial<GMBData>): Promise<ValidationResult> {
    logger.debug('Starting data validation and cleaning', { businessName: data.businessName });

    const result: ValidationResult = {
      isValid: false,
      errors: [],
      warnings: [],
      cleanedData: {},
      confidence: 0,
    };

    try {
      // Schema validation
      const { error, value, warning } = this.gmbDataSchema.validate(data, {
        abortEarly: false,
        stripUnknown: true,
        convert: true,
      });

      if (error) {
        result.errors = error.details.map(detail => detail.message);
      }

      if (warning) {
        result.warnings = warning.details.map(detail => detail.message);
      }

      // Clean and normalize data
      result.cleanedData = await this.cleanData(value || data);

      // Additional validation rules
      await this.performCustomValidation(result);

      // Calculate confidence score
      result.confidence = this.calculateConfidence(result.cleanedData, result.errors, result.warnings);

      // Determine if data is valid
      result.isValid = result.errors.length === 0 && result.confidence >= 60;

      logger.info('Data validation completed', {
        businessName: data.businessName,
        isValid: result.isValid,
        confidence: result.confidence,
        errors: result.errors.length,
        warnings: result.warnings.length,
      });

      return result;

    } catch (error: any) {
      logger.error('Error during data validation', { error: error.message });
      result.errors.push(`Validation error: ${error.message}`);
      return result;
    }
  }

  private async cleanData(data: Partial<GMBData>): Promise<Partial<GMBData>> {
    const cleaned: Partial<GMBData> = { ...data };

    // Clean business name
    if (cleaned.businessName) {
      cleaned.businessName = this.cleanBusinessName(cleaned.businessName);
    }

    // Clean address
    if (cleaned.address) {
      cleaned.address = this.cleanAddress(cleaned.address);
    }

    // Clean phone number
    if (cleaned.phone) {
      cleaned.phone = this.cleanPhoneNumber(cleaned.phone);
    }

    // Clean website URL
    if (cleaned.website) {
      cleaned.website = this.cleanWebsiteUrl(cleaned.website);
    }

    // Validate and clean rating
    if (cleaned.rating !== undefined) {
      cleaned.rating = this.cleanRating(cleaned.rating);
    }

    // Clean review count
    if (cleaned.reviewCount !== undefined) {
      cleaned.reviewCount = this.cleanReviewCount(cleaned.reviewCount);
    }

    // Clean coordinates
    if (cleaned.coordinates) {
      cleaned.coordinates = this.cleanCoordinates(cleaned.coordinates);
    }

    return cleaned;
  }

  private cleanBusinessName(name: string): string {
    return name
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[^\w\s&.-]/g, '') // Remove special characters except &, ., -
      .substring(0, 255); // Limit length
  }

  private cleanAddress(address: string): string {
    return address
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/,\s*,/g, ',') // Remove double commas
      .substring(0, 500);
  }

  private cleanPhoneNumber(phone: string): string {
    // Remove all non-digit characters except +
    let cleaned = phone.replace(/[^\d+]/g, '');
    
    // Ensure it starts with + for international format
    if (!cleaned.startsWith('+') && cleaned.length >= 10) {
      cleaned = '+1' + cleaned; // Assume US number if no country code
    }
    
    return cleaned;
  }

  private cleanWebsiteUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.toString();
    } catch {
      // Try to fix common issues
      if (!url.startsWith('http')) {
        url = 'https://' + url;
      }
      try {
        const urlObj = new URL(url);
        return urlObj.toString();
      } catch {
        return url; // Return as-is if can't be fixed
      }
    }
  }

  private cleanRating(rating: number): number {
    const cleaned = Math.round(rating * 10) / 10; // Round to 1 decimal place
    return Math.max(0, Math.min(5, cleaned)); // Ensure between 0 and 5
  }

  private cleanReviewCount(count: number): number {
    return Math.max(0, Math.floor(count)); // Ensure non-negative integer
  }

  private cleanCoordinates(coords: any): { latitude: number; longitude: number } | undefined {
    if (!coords || typeof coords.latitude !== 'number' || typeof coords.longitude !== 'number') {
      return undefined;
    }

    const lat = Math.max(-90, Math.min(90, coords.latitude));
    const lng = Math.max(-180, Math.min(180, coords.longitude));

    return { latitude: lat, longitude: lng };
  }

  private async performCustomValidation(result: ValidationResult): Promise<void> {
    const data = result.cleanedData;

    // Check business name consistency
    if (data.businessName && data.businessName.length < 2) {
      result.warnings.push('Business name is very short');
    }

    // Check rating vs review count consistency
    if (data.rating && data.reviewCount !== undefined) {
      if (data.rating > 0 && data.reviewCount === 0) {
        result.warnings.push('Business has rating but no reviews');
      }
      if (data.rating === 0 && data.reviewCount > 0) {
        result.warnings.push('Business has reviews but no rating');
      }
    }

    // Check address format
    if (data.address && !this.isValidAddressFormat(data.address)) {
      result.warnings.push('Address format may be incomplete');
    }

    // Check phone number format
    if (data.phone && !this.isValidPhoneFormat(data.phone)) {
      result.warnings.push('Phone number format may be invalid');
    }

    // Check website accessibility (basic check)
    if (data.website && !this.isValidWebsiteFormat(data.website)) {
      result.warnings.push('Website URL format may be invalid');
    }

    // Check data freshness
    if (data.scrapedAt) {
      const hoursSinceScraped = (Date.now() - data.scrapedAt.getTime()) / (1000 * 60 * 60);
      if (hoursSinceScraped > 24) {
        result.warnings.push('Data is more than 24 hours old');
      }
    }
  }

  private isValidAddressFormat(address: string): boolean {
    // Basic address validation - should contain numbers and letters
    return /\d/.test(address) && /[a-zA-Z]/.test(address) && address.length > 10;
  }

  private isValidPhoneFormat(phone: string): boolean {
    // Should be at least 10 digits for US numbers
    const digits = phone.replace(/\D/g, '');
    return digits.length >= 10 && digits.length <= 15;
  }

  private isValidWebsiteFormat(website: string): boolean {
    try {
      const url = new URL(website);
      return ['http:', 'https:'].includes(url.protocol);
    } catch {
      return false;
    }
  }

  private calculateConfidence(data: Partial<GMBData>, errors: string[], warnings: string[]): number {
    let confidence = 100;

    // Reduce confidence for errors
    confidence -= errors.length * 20;

    // Reduce confidence for warnings
    confidence -= warnings.length * 5;

    // Increase confidence for completeness
    const completeness = this.calculateCompleteness(data);
    confidence = confidence * (completeness / 100);

    // Ensure confidence is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(confidence)));
  }

  private calculateCompleteness(data: Partial<GMBData>): number {
    const requiredFields = ['businessName', 'address'];
    const optionalFields = ['phone', 'website', 'rating', 'reviewCount', 'coordinates'];
    
    let score = 0;
    let totalFields = requiredFields.length + optionalFields.length;

    // Required fields are worth more
    for (const field of requiredFields) {
      if (data[field as keyof GMBData]) {
        score += 2;
      }
    }

    // Optional fields
    for (const field of optionalFields) {
      if (data[field as keyof GMBData]) {
        score += 1;
      }
    }

    return Math.round((score / (requiredFields.length * 2 + optionalFields.length)) * 100);
  }

  public calculateDataQuality(data: Partial<GMBData>, validationResult: ValidationResult): DataQualityMetrics {
    const completeness = this.calculateCompleteness(data);
    const accuracy = validationResult.confidence;
    const consistency = this.calculateConsistency(data);
    const freshness = this.calculateFreshness(data.scrapedAt);
    
    const overall = Math.round((completeness + accuracy + consistency + freshness) / 4);

    return {
      completeness,
      accuracy,
      consistency,
      freshness,
      overall,
    };
  }

  private calculateConsistency(data: Partial<GMBData>): number {
    let score = 100;

    // Check rating vs review count consistency
    if (data.rating && data.reviewCount !== undefined) {
      if ((data.rating > 0 && data.reviewCount === 0) || (data.rating === 0 && data.reviewCount > 0)) {
        score -= 20;
      }
    }

    // Check if business name appears in address (might indicate data mixing)
    if (data.businessName && data.address) {
      const nameParts = data.businessName.toLowerCase().split(' ');
      const addressLower = data.address.toLowerCase();
      const nameInAddress = nameParts.some(part => part.length > 3 && addressLower.includes(part));
      if (nameInAddress) {
        score -= 10;
      }
    }

    return Math.max(0, score);
  }

  private calculateFreshness(scrapedAt?: Date): number {
    if (!scrapedAt) return 0;

    const hoursSinceScraped = (Date.now() - scrapedAt.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceScraped <= 1) return 100;
    if (hoursSinceScraped <= 6) return 90;
    if (hoursSinceScraped <= 24) return 80;
    if (hoursSinceScraped <= 72) return 60;
    if (hoursSinceScraped <= 168) return 40; // 1 week
    
    return 20; // Older than 1 week
  }
}
