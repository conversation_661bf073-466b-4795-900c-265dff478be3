{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:30:05.099Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:30:05.107Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:30:05.108Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:30:05.109Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:30:05.109Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:30:05.110Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:30:05.110Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:30:05.111Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:30:05.111Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:30:05.113Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:30:05.113Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:31:23.055Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:31:23.061Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:31:23.062Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:31:23.062Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:31:23.063Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:31:23.063Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:31:23.064Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:31:23.064Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:31:23.065Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:31:23.066Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:31:23.066Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:31:53.916Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:31:53.922Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:31:53.923Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:31:53.923Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:31:53.924Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:31:53.925Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:31:53.926Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:31:53.926Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:31:53.927Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:31:53.928Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:31:53.929Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:32:47.269Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:32:47.278Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:32:47.278Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:32:47.279Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:32:47.279Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:32:47.280Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:32:47.281Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:32:47.282Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:32:47.283Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:32:47.285Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:32:47.286Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:33:30.246Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:33:30.251Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:33:30.252Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:33:30.252Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:33:30.253Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:33:30.253Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:33:30.254Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:33:30.256Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:33:30.257Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:33:30.258Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:33:30.259Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:34:37.033Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:34:37.038Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:34:37.039Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:34:37.039Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:34:37.040Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:34:37.040Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:34:37.041Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:34:37.041Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:34:37.041Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:34:37.042Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:34:37.046Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:39:29.039Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:39:29.047Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:39:29.048Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:39:29.049Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:39:29.049Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:39:29.051Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:39:29.052Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:39:29.052Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:39:29.053Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:39:29.055Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:39:29.056Z"}
{"chartTypes":["score","breakdown","competitive"],"level":"info","message":"Chart generation requested","timestamp":"2025-07-31T05:40:40.044Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:42:37.144Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:42:37.148Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:42:37.149Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:42:37.149Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:42:37.150Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:42:37.151Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:42:37.151Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:42:37.151Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:42:37.153Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:42:37.156Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:42:37.158Z"}
{"businessName":"Joe's Pizza","level":"info","message":"PDF generation requested","template":"default","timestamp":"2025-07-31T05:42:59.598Z"}
{"chartTypes":["score","breakdown","competitive"],"level":"info","message":"Chart generation requested","timestamp":"2025-07-31T05:42:59.747Z"}
{"level":"info","message":"Portal access requested","reportId":"a92cf573-1f90-4d6f-88a4-aefa6f9d0a96","timestamp":"2025-07-31T05:42:59.771Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:49:23.702Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:49:23.707Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:49:23.708Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:49:23.708Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:49:23.709Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:49:23.710Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:49:23.710Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:49:23.711Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:49:23.712Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:49:23.714Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:49:23.715Z"}
{"businessName":"Joe's Pizza","level":"info","message":"PDF generation requested","template":"default","timestamp":"2025-07-31T05:49:45.754Z"}
{"chartTypes":["score","breakdown","competitive"],"level":"info","message":"Chart generation requested","timestamp":"2025-07-31T05:49:45.833Z"}
{"level":"info","message":"Portal access requested","reportId":"08067a00-774f-4319-95f9-d6f38154fe4d","timestamp":"2025-07-31T05:49:45.859Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"08067a00-774f-4319-95f9-d6f38154fe4d","timestamp":"2025-07-31T05:49:45.872Z"}
{"level":"info","message":"WhatsApp delivery requested","phoneNumber":"+1234567890","reportId":"08067a00-774f-4319-95f9-d6f38154fe4d","timestamp":"2025-07-31T05:49:45.883Z"}
{"email":true,"level":"info","message":"Multi-channel delivery requested","reportId":"08067a00-774f-4319-95f9-d6f38154fe4d","timestamp":"2025-07-31T05:49:45.890Z","whatsapp":true}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T05:50:55.926Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T05:50:55.933Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T05:50:55.933Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T05:50:55.934Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T05:50:55.934Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T05:50:55.935Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T05:50:55.935Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T05:50:55.936Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T05:50:55.937Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T05:50:55.938Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T05:50:55.939Z"}
{"businessName":"Joe's Pizza","level":"info","message":"PDF generation requested","template":"default","timestamp":"2025-07-31T05:51:18.364Z"}
{"chartTypes":["score","breakdown","competitive"],"level":"info","message":"Chart generation requested","timestamp":"2025-07-31T05:51:18.423Z"}
{"level":"info","message":"Portal access requested","reportId":"801322c4-0cb1-4073-8c64-3b96e1a4335b","timestamp":"2025-07-31T05:51:18.442Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"801322c4-0cb1-4073-8c64-3b96e1a4335b","timestamp":"2025-07-31T05:51:18.453Z"}
{"level":"info","message":"WhatsApp delivery requested","phoneNumber":"+1234567890","reportId":"801322c4-0cb1-4073-8c64-3b96e1a4335b","timestamp":"2025-07-31T05:51:18.460Z"}
{"email":true,"level":"info","message":"Multi-channel delivery requested","reportId":"801322c4-0cb1-4073-8c64-3b96e1a4335b","timestamp":"2025-07-31T05:51:18.467Z","whatsapp":true}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T06:14:31.261Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T06:14:31.268Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T06:14:31.268Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T06:14:31.268Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T06:14:31.269Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T06:14:31.270Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T06:14:31.270Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T06:14:31.270Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T06:14:31.271Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T06:14:31.272Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T06:14:31.273Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T06:16:38.244Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T06:16:38.370Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T06:16:38.370Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T06:16:38.370Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T06:16:38.371Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T06:16:38.372Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T06:16:38.372Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T06:16:38.373Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T06:16:38.374Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T06:16:38.376Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T06:16:38.377Z"}
{"businessName":"Mario's Italian Restaurant","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:17:02.562Z"}
{"level":"info","message":"Portal access requested","reportId":"c0b44fed-e066-4a38-a859-cc8f74ebd7c9","timestamp":"2025-07-31T06:17:02.825Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"c0b44fed-e066-4a38-a859-cc8f74ebd7c9","timestamp":"2025-07-31T06:17:02.840Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T06:19:41.390Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T06:19:41.395Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T06:19:41.396Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T06:19:41.397Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T06:19:41.397Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T06:19:41.398Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T06:19:41.398Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T06:19:41.399Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T06:19:41.400Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T06:19:41.402Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T06:19:41.403Z"}
{"businessName":"Test Restaurant","hasCompetitors":false,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:20:11.393Z"}
{"level":"info","message":"Portal access requested","reportId":"247f649b-b024-4c2f-b4e3-e41e0306af44","timestamp":"2025-07-31T06:20:11.604Z"}
{"businessName":"Bella Vista Italian Bistro","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:33:34.639Z"}
{"level":"info","message":"Portal access requested","reportId":"c928feb4-85a9-47e3-af25-43e5600dad12","timestamp":"2025-07-31T06:33:34.667Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"c928feb4-85a9-47e3-af25-43e5600dad12","timestamp":"2025-07-31T06:33:34.673Z"}
{"businessName":"Quick Fix Plumbing","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:33:34.680Z"}
{"level":"info","message":"Portal access requested","reportId":"d4edab05-e288-42c3-87b5-9428097fc812","timestamp":"2025-07-31T06:33:34.705Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"d4edab05-e288-42c3-87b5-9428097fc812","timestamp":"2025-07-31T06:33:34.710Z"}
{"businessName":"Downtown Dental Care","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:33:34.717Z"}
{"level":"info","message":"Portal access requested","reportId":"a3e46860-5b9f-428e-8c26-621c14309003","timestamp":"2025-07-31T06:33:34.747Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"a3e46860-5b9f-428e-8c26-621c14309003","timestamp":"2025-07-31T06:33:34.751Z"}
{"businessName":"Performance Test Restaurant 1","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.832Z"}
{"businessName":"Performance Test Restaurant 1","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.861Z"}
{"businessName":"Performance Test Restaurant 2","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.865Z"}
{"businessName":"Performance Test Restaurant 3","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.872Z"}
{"businessName":"Performance Test Restaurant 1","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.898Z"}
{"businessName":"Performance Test Restaurant 2","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.905Z"}
{"businessName":"Performance Test Restaurant 3","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.910Z"}
{"businessName":"Performance Test Restaurant 4","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.915Z"}
{"businessName":"Performance Test Restaurant 5","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:37:02.920Z"}
{"level":"info","message":"Portal access requested","reportId":"non-existent-report-id","timestamp":"2025-07-31T06:41:08.826Z"}
{"level":"info","message":"Portal access requested","reportId":"invalid-format","timestamp":"2025-07-31T06:41:08.830Z"}
{"businessName":"Fallback Test Business","hasCompetitors":false,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:41:08.854Z"}
{"businessName":"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA","hasCompetitors":false,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:41:08.871Z"}
{"businessName":"Test<script>alert('xss')</script>Business","hasCompetitors":false,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:41:08.899Z"}
{"businessName":"Raga Dental Implants and Laser","hasCompetitors":true,"level":"info","message":"Integrated report generation requested","timestamp":"2025-07-31T06:53:40.735Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"d869afa4-9d05-4c62-b76a-a889c85722db","timestamp":"2025-07-31T06:53:40.783Z"}
{"level":"info","message":"Portal access requested","reportId":"d869afa4-9d05-4c62-b76a-a889c85722db","timestamp":"2025-07-31T06:53:49.639Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::3003","port":3003,"stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\apps\\report-generator\\src\\index.ts:689:20)\n    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n    at Module.m._compile (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1689:10\n    at Object.require.extensions.<computed> [as .ts] (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)","syscall":"listen","timestamp":"2025-07-31T07:23:32.014Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T07:26:04.464Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T07:26:04.470Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T07:26:04.470Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T07:26:04.470Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T07:26:04.471Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T07:26:04.472Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T07:26:04.472Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T07:26:04.473Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T07:26:04.473Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T07:26:04.474Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T07:26:04.476Z"}
{"businessName":"Raga Dental Implants and Laser","hasCompetitors":true,"level":"info","message":"World-class report generation requested","template":"healthcare","timestamp":"2025-07-31T07:26:48.380Z"}
{"error":"Failed to generate chart: Unsupported chart type: sentiment-analysis","level":"error","message":"World-class report generation failed","timestamp":"2025-07-31T07:26:48.518Z"}
{"level":"info","message":"GMB Report Generator started on port 3003","timestamp":"2025-07-31T07:27:54.595Z"}
{"level":"info","message":"Available endpoints:","timestamp":"2025-07-31T07:27:54.601Z"}
{"level":"info","message":"  GET  /health - Health check","timestamp":"2025-07-31T07:27:54.602Z"}
{"level":"info","message":"  POST /api/generate/pdf - Generate PDF report","timestamp":"2025-07-31T07:27:54.602Z"}
{"level":"info","message":"  POST /api/generate/charts - Generate visualizations","timestamp":"2025-07-31T07:27:54.603Z"}
{"level":"info","message":"  POST /api/deliver/email - Send report via email","timestamp":"2025-07-31T07:27:54.604Z"}
{"level":"info","message":"  POST /api/deliver/whatsapp - Send report via WhatsApp","timestamp":"2025-07-31T07:27:54.605Z"}
{"level":"info","message":"  GET  /api/download/:reportId - Download report","timestamp":"2025-07-31T07:27:54.606Z"}
{"level":"info","message":"  GET  /api/portal/:reportId - Web portal access","timestamp":"2025-07-31T07:27:54.607Z"}
{"level":"info","message":"  GET  /api/templates - List available templates","timestamp":"2025-07-31T07:27:54.609Z"}
{"level":"info","message":"  GET  /api/docs - API documentation","timestamp":"2025-07-31T07:27:54.612Z"}
{"businessName":"Raga Dental Implants and Laser","hasCompetitors":true,"level":"info","message":"World-class report generation requested","template":"healthcare","timestamp":"2025-07-31T07:28:17.091Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"GMB-mdr2p3c9-vrtxv78th","timestamp":"2025-07-31T07:28:17.259Z"}
{"businessName":"Raga Dental Implants and Laser","hasCompetitors":true,"level":"info","message":"World-class report generation requested","template":"healthcare","timestamp":"2025-07-31T07:47:15.707Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"GMB-mdr3dhuf-sd3hj8902","timestamp":"2025-07-31T07:47:15.770Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::3003","port":3003,"stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\apps\\report-generator\\dist\\index.js:570:20)\n    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n    at Object..js (node:internal/modules/cjs/loader:1689:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","syscall":"listen","timestamp":"2025-07-31T07:49:38.258Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::3003","port":3003,"stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\apps\\report-generator\\dist\\index.js:570:20)\n    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n    at Object..js (node:internal/modules/cjs/loader:1689:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","syscall":"listen","timestamp":"2025-07-31T07:49:56.208Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::3003","port":3003,"stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\apps\\report-generator\\dist\\index.js:570:20)\n    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n    at Object..js (node:internal/modules/cjs/loader:1689:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","syscall":"listen","timestamp":"2025-07-31T07:50:21.662Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::3003","port":3003,"stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\apps\\report-generator\\dist\\index.js:570:20)\n    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n    at Object..js (node:internal/modules/cjs/loader:1689:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","syscall":"listen","timestamp":"2025-07-31T07:50:37.700Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Server error: listen EADDRINUSE: address already in use :::3003","port":3003,"stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\projects\\Augment Code\\workflows\\Google Business\\SaaS\\apps\\report-generator\\dist\\index.js:570:20)\n    at Module._compile (node:internal/modules/cjs/loader:1546:14)\n    at Object..js (node:internal/modules/cjs/loader:1689:10)\n    at Module.load (node:internal/modules/cjs/loader:1318:32)\n    at Function._load (node:internal/modules/cjs/loader:1128:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)","syscall":"listen","timestamp":"2025-07-31T07:50:57.315Z"}
{"businessName":"Raga Dental Implants and Laser","hasCompetitors":true,"level":"info","message":"World-class report generation requested","template":"healthcare","timestamp":"2025-07-31T07:51:09.545Z"}
{"level":"info","message":"Email delivery requested","recipient":"<EMAIL>","reportId":"GMB-mdr3ii9x-we7zm73qo","timestamp":"2025-07-31T07:51:09.614Z"}
