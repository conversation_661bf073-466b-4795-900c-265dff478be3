const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('⚡ Phase 4 Testing & Verification - Performance Testing & Load Testing\n');

// Performance test configuration
const PERFORMANCE_CONFIG = {
  concurrentUsers: [1, 5, 10, 20],
  testDuration: 30000, // 30 seconds
  warmupRequests: 5,
  endpoints: [
    { path: '/health', method: 'GET', service: 'report-generator', port: 3003 },
    { path: '/api/docs', method: 'GET', service: 'report-generator', port: 3003 },
    { path: '/health', method: 'GET', service: 'analyzer', port: 3002 },
    { path: '/api/docs', method: 'GET', service: 'analyzer', port: 3002 },
    { path: '/health', method: 'GET', service: 'data-collector', port: 3001 }
  ],
  integratedReportTest: {
    businessData: {
      businessName: "Performance Test Restaurant",
      address: "123 Speed Lane, Fast City, FC 12345",
      phone: "******-FAST-123",
      website: "https://fastrestaurant.com",
      reviews: Array.from({length: 50}, (_, i) => ({
        rating: Math.floor(Math.random() * 5) + 1,
        text: `Review ${i + 1}`,
        author: `Customer ${i + 1}`,
        date: new Date().toISOString().split('T')[0]
      })),
      photos: Array.from({length: 20}, (_, i) => ({
        type: 'food',
        url: `https://example.com/photo${i + 1}.jpg`,
        date: new Date().toISOString().split('T')[0]
      }))
    },
    competitorData: [
      { businessName: "Competitor 1", rating: 4.2, reviewCount: 100, photoCount: 15, postCount: 5 },
      { businessName: "Competitor 2", rating: 4.5, reviewCount: 80, photoCount: 20, postCount: 8 }
    ]
  }
};

function makeRequest(options, data = null, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const responseData = res.headers['content-type']?.includes('application/json') 
            ? JSON.parse(body) 
            : body;
          resolve({ 
            status: res.statusCode, 
            data: responseData, 
            responseTime,
            size: Buffer.byteLength(body, 'utf8')
          });
        } catch (error) {
          resolve({ 
            status: res.statusCode, 
            data: body, 
            responseTime,
            size: Buffer.byteLength(body, 'utf8')
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({ error: error.message, responseTime: Date.now() - startTime });
    });

    req.setTimeout(timeout, () => {
      req.destroy();
      reject({ error: 'Request timeout', responseTime: timeout });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function performanceTest() {
  const results = {
    timestamp: new Date().toISOString(),
    warmup: {},
    endpointTests: {},
    loadTests: {},
    integratedReportTests: {},
    summary: {}
  };

  try {
    // 1. Warmup Phase
    console.log('1. 🔥 Warmup Phase...');
    const warmupResults = [];
    
    for (let i = 0; i < PERFORMANCE_CONFIG.warmupRequests; i++) {
      try {
        const result = await makeRequest({
          hostname: 'localhost',
          port: 3003,
          path: '/health',
          method: 'GET'
        });
        warmupResults.push(result.responseTime);
        process.stdout.write('.');
      } catch (error) {
        process.stdout.write('x');
      }
    }
    
    results.warmup = {
      requests: PERFORMANCE_CONFIG.warmupRequests,
      averageResponseTime: warmupResults.length > 0 ? 
        Math.round(warmupResults.reduce((a, b) => a + b, 0) / warmupResults.length) : 0,
      successRate: (warmupResults.length / PERFORMANCE_CONFIG.warmupRequests * 100).toFixed(1)
    };
    
    console.log(`\n   ✅ Warmup completed: ${results.warmup.averageResponseTime}ms avg, ${results.warmup.successRate}% success\n`);

    // 2. Endpoint Performance Testing
    console.log('2. 🎯 Endpoint Performance Testing...');
    
    for (const endpoint of PERFORMANCE_CONFIG.endpoints) {
      console.log(`   Testing ${endpoint.service} ${endpoint.method} ${endpoint.path}...`);
      
      const endpointResults = [];
      const testCount = 10;
      
      for (let i = 0; i < testCount; i++) {
        try {
          const result = await makeRequest({
            hostname: 'localhost',
            port: endpoint.port,
            path: endpoint.path,
            method: endpoint.method
          });
          endpointResults.push({
            responseTime: result.responseTime,
            status: result.status,
            size: result.size
          });
        } catch (error) {
          endpointResults.push({
            responseTime: 0,
            status: 0,
            error: error.error
          });
        }
      }
      
      const successful = endpointResults.filter(r => r.status === 200);
      const avgResponseTime = successful.length > 0 ? 
        Math.round(successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length) : 0;
      const avgSize = successful.length > 0 ? 
        Math.round(successful.reduce((sum, r) => sum + r.size, 0) / successful.length) : 0;
      
      results.endpointTests[`${endpoint.service}_${endpoint.method}_${endpoint.path.replace(/\//g, '_')}`] = {
        service: endpoint.service,
        endpoint: `${endpoint.method} ${endpoint.path}`,
        totalRequests: testCount,
        successfulRequests: successful.length,
        successRate: (successful.length / testCount * 100).toFixed(1),
        averageResponseTime: avgResponseTime,
        averageSize: avgSize,
        minResponseTime: successful.length > 0 ? Math.min(...successful.map(r => r.responseTime)) : 0,
        maxResponseTime: successful.length > 0 ? Math.max(...successful.map(r => r.responseTime)) : 0
      };
      
      console.log(`      ✅ ${successful.length}/${testCount} success, ${avgResponseTime}ms avg, ${avgSize} bytes`);
    }

    // 3. Load Testing with Concurrent Users
    console.log('\n3. 🚀 Load Testing with Concurrent Users...');
    
    for (const concurrentUsers of PERFORMANCE_CONFIG.concurrentUsers) {
      console.log(`   Testing with ${concurrentUsers} concurrent users...`);
      
      const loadTestResults = [];
      const promises = [];
      const startTime = Date.now();
      
      // Create concurrent requests
      for (let user = 0; user < concurrentUsers; user++) {
        const userPromise = (async () => {
          const userResults = [];
          const endTime = startTime + PERFORMANCE_CONFIG.testDuration;
          
          while (Date.now() < endTime) {
            try {
              const result = await makeRequest({
                hostname: 'localhost',
                port: 3003,
                path: '/health',
                method: 'GET'
              }, null, 5000);
              
              userResults.push({
                responseTime: result.responseTime,
                status: result.status,
                timestamp: Date.now()
              });
              
              // Small delay to prevent overwhelming
              await new Promise(resolve => setTimeout(resolve, 100));
              
            } catch (error) {
              userResults.push({
                responseTime: 0,
                status: 0,
                error: error.error,
                timestamp: Date.now()
              });
            }
          }
          
          return userResults;
        })();
        
        promises.push(userPromise);
      }
      
      // Wait for all users to complete
      const allUserResults = await Promise.all(promises);
      const flatResults = allUserResults.flat();
      
      const successful = flatResults.filter(r => r.status === 200);
      const totalRequests = flatResults.length;
      const testDurationActual = Date.now() - startTime;
      
      results.loadTests[`${concurrentUsers}_users`] = {
        concurrentUsers,
        testDuration: testDurationActual,
        totalRequests,
        successfulRequests: successful.length,
        failedRequests: totalRequests - successful.length,
        successRate: (successful.length / totalRequests * 100).toFixed(1),
        requestsPerSecond: Math.round(totalRequests / (testDurationActual / 1000)),
        averageResponseTime: successful.length > 0 ? 
          Math.round(successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length) : 0,
        minResponseTime: successful.length > 0 ? Math.min(...successful.map(r => r.responseTime)) : 0,
        maxResponseTime: successful.length > 0 ? Math.max(...successful.map(r => r.responseTime)) : 0
      };
      
      console.log(`      📊 ${totalRequests} requests, ${successful.length} success (${results.loadTests[`${concurrentUsers}_users`].successRate}%)`);
      console.log(`      ⚡ ${results.loadTests[`${concurrentUsers}_users`].requestsPerSecond} req/sec, ${results.loadTests[`${concurrentUsers}_users`].averageResponseTime}ms avg`);
    }

    // 4. Integrated Report Performance Testing
    console.log('\n4. 📊 Integrated Report Performance Testing...');
    
    const reportTests = [1, 3, 5]; // Number of concurrent report generations
    
    for (const concurrent of reportTests) {
      console.log(`   Testing ${concurrent} concurrent report generation(s)...`);
      
      const reportPromises = [];
      const startTime = Date.now();
      
      for (let i = 0; i < concurrent; i++) {
        const reportPromise = makeRequest({
          hostname: 'localhost',
          port: 3003,
          path: '/api/generate/integrated-report',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, {
          businessData: {
            ...PERFORMANCE_CONFIG.integratedReportTest.businessData,
            businessName: `${PERFORMANCE_CONFIG.integratedReportTest.businessData.businessName} ${i + 1}`
          },
          competitorData: PERFORMANCE_CONFIG.integratedReportTest.competitorData,
          options: { template: 'default' }
        }, 30000);
        
        reportPromises.push(reportPromise);
      }
      
      try {
        const reportResults = await Promise.all(reportPromises);
        const totalTime = Date.now() - startTime;
        
        const successful = reportResults.filter(r => r.status === 200 && r.data.success);
        const avgResponseTime = successful.length > 0 ? 
          Math.round(successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length) : 0;
        const avgSize = successful.length > 0 ? 
          Math.round(successful.reduce((sum, r) => sum + r.size, 0) / successful.length) : 0;
        
        results.integratedReportTests[`${concurrent}_concurrent`] = {
          concurrentReports: concurrent,
          totalTime,
          successfulReports: successful.length,
          successRate: (successful.length / concurrent * 100).toFixed(1),
          averageResponseTime: avgResponseTime,
          averageReportSize: avgSize,
          reportsPerMinute: Math.round((successful.length / totalTime) * 60000)
        };
        
        console.log(`      ✅ ${successful.length}/${concurrent} success, ${avgResponseTime}ms avg, ${avgSize} bytes avg`);
        console.log(`      📈 ${results.integratedReportTests[`${concurrent}_concurrent`].reportsPerMinute} reports/min capacity`);
        
      } catch (error) {
        console.log(`      ❌ Report generation test failed: ${error.message}`);
        results.integratedReportTests[`${concurrent}_concurrent`] = {
          concurrentReports: concurrent,
          error: error.message,
          success: false
        };
      }
    }

    // 5. Performance Summary
    console.log('\n5. 📈 Performance Summary...');
    
    const endpointAvgs = Object.values(results.endpointTests);
    const loadTestAvgs = Object.values(results.loadTests);
    const reportTestAvgs = Object.values(results.integratedReportTests).filter(t => !t.error);
    
    results.summary = {
      endpointPerformance: {
        averageResponseTime: endpointAvgs.length > 0 ? 
          Math.round(endpointAvgs.reduce((sum, t) => sum + t.averageResponseTime, 0) / endpointAvgs.length) : 0,
        averageSuccessRate: endpointAvgs.length > 0 ? 
          (endpointAvgs.reduce((sum, t) => sum + parseFloat(t.successRate), 0) / endpointAvgs.length).toFixed(1) : '0.0'
      },
      loadTestPerformance: {
        maxConcurrentUsers: Math.max(...PERFORMANCE_CONFIG.concurrentUsers),
        maxRequestsPerSecond: loadTestAvgs.length > 0 ? Math.max(...loadTestAvgs.map(t => t.requestsPerSecond)) : 0,
        averageSuccessRateUnderLoad: loadTestAvgs.length > 0 ? 
          (loadTestAvgs.reduce((sum, t) => sum + parseFloat(t.successRate), 0) / loadTestAvgs.length).toFixed(1) : '0.0'
      },
      reportGenerationPerformance: {
        maxConcurrentReports: reportTestAvgs.length > 0 ? Math.max(...reportTestAvgs.map(t => t.concurrentReports)) : 0,
        averageReportGenerationTime: reportTestAvgs.length > 0 ? 
          Math.round(reportTestAvgs.reduce((sum, t) => sum + t.averageResponseTime, 0) / reportTestAvgs.length) : 0,
        maxReportsPerMinute: reportTestAvgs.length > 0 ? Math.max(...reportTestAvgs.map(t => t.reportsPerMinute)) : 0
      }
    };
    
    console.log(`   🎯 Endpoint Performance: ${results.summary.endpointPerformance.averageResponseTime}ms avg, ${results.summary.endpointPerformance.averageSuccessRate}% success`);
    console.log(`   🚀 Load Test Performance: ${results.summary.loadTestPerformance.maxRequestsPerSecond} req/sec max, ${results.summary.loadTestPerformance.averageSuccessRateUnderLoad}% success under load`);
    console.log(`   📊 Report Generation: ${results.summary.reportGenerationPerformance.averageReportGenerationTime}ms avg, ${results.summary.reportGenerationPerformance.maxReportsPerMinute} reports/min max`);

    // 6. Save Results
    const resultsPath = path.join(__dirname, 'performance-test-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed performance results saved to: ${resultsPath}`);

    // 7. Performance Assessment
    console.log('\n🎯 Performance Assessment:');
    
    const endpointHealthy = results.summary.endpointPerformance.averageResponseTime < 100 && 
                           parseFloat(results.summary.endpointPerformance.averageSuccessRate) > 95;
    const loadHealthy = results.summary.loadTestPerformance.maxRequestsPerSecond > 50 && 
                       parseFloat(results.summary.loadTestPerformance.averageSuccessRateUnderLoad) > 90;
    const reportHealthy = results.summary.reportGenerationPerformance.averageReportGenerationTime < 5000 && 
                         results.summary.reportGenerationPerformance.maxReportsPerMinute > 10;
    
    console.log(`   🎯 Endpoint Performance: ${endpointHealthy ? '✅ EXCELLENT' : '⚠️  NEEDS OPTIMIZATION'}`);
    console.log(`   🚀 Load Handling: ${loadHealthy ? '✅ EXCELLENT' : '⚠️  NEEDS OPTIMIZATION'}`);
    console.log(`   📊 Report Generation: ${reportHealthy ? '✅ EXCELLENT' : '⚠️  NEEDS OPTIMIZATION'}`);
    
    if (endpointHealthy && loadHealthy && reportHealthy) {
      console.log('\n   ✅ PERFORMANCE TESTING: PASSED - System performance is excellent!');
    } else {
      console.log('\n   ⚠️  PERFORMANCE TESTING: OPTIMIZATION RECOMMENDED');
    }

  } catch (error) {
    console.error('❌ Performance testing failed:', error.message);
    results.error = error.message;
  }

  return results;
}

// Run performance testing
performanceTest().then(results => {
  console.log('\n🏁 Performance Testing Complete!');
}).catch(error => {
  console.error('💥 Performance testing suite crashed:', error.message);
});
