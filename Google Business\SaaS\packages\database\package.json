{"name": "@gmb-audit/database", "version": "1.0.0", "description": "Database layer for GMB audit generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.7.0", "dotenv": "^16.3.1", "joi": "^17.11.0"}, "devDependencies": {"@types/node": "^20.10.0", "prisma": "^5.7.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}