"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GMBScrapingService = void 0;
const playwright_1 = require("playwright");
const user_agents_1 = __importDefault(require("user-agents"));
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
class GMBScrapingService {
    browser = null;
    contexts = [];
    proxyList = [];
    userAgents;
    constructor() {
        this.userAgents = new user_agents_1.default();
        this.loadProxyList();
    }
    loadProxyList() {
        this.proxyList = [];
    }
    async initializeBrowser() {
        if (this.browser) {
            return this.browser;
        }
        logger.info('Initializing browser with anti-detection measures');
        this.browser = await playwright_1.chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-hang-monitor',
                '--disable-prompt-on-repost',
                '--disable-sync',
                '--disable-translate',
                '--metrics-recording-only',
                '--no-default-browser-check',
                '--safebrowsing-disable-auto-update',
                '--enable-automation',
                '--password-store=basic',
                '--use-mock-keychain',
            ],
        });
        return this.browser;
    }
    async createStealthContext() {
        const browser = await this.initializeBrowser();
        const userAgent = this.userAgents.random();
        const proxy = this.proxyList.length > 0
            ? this.proxyList[Math.floor(Math.random() * this.proxyList.length)]
            : undefined;
        const contextOptions = {
            userAgent: userAgent.toString(),
            viewport: {
                width: 1366 + Math.floor(Math.random() * 200),
                height: 768 + Math.floor(Math.random() * 200),
            },
            locale: 'en-US',
            timezoneId: 'America/New_York',
            permissions: ['geolocation'],
            geolocation: {
                latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
                longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
            },
        };
        if (proxy) {
            contextOptions.proxy = { server: proxy };
        }
        const context = await browser.newContext(contextOptions);
        await context.addInitScript(`
      // Override webdriver property
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // Override plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // Override languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });

      // Override permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: window.Notification?.permission }) :
          originalQuery(parameters)
      );

      // Override chrome runtime
      if (!window.chrome) {
        window.chrome = {};
      }
      if (!window.chrome.runtime) {
        window.chrome.runtime = {};
      }
    `);
        this.contexts.push(context);
        return context;
    }
    async randomDelay(min = 1000, max = 3000) {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        await new Promise(resolve => setTimeout(resolve, delay));
    }
    async humanLikeTyping(page, selector, text) {
        await page.click(selector);
        await this.randomDelay(100, 300);
        for (const char of text) {
            await page.keyboard.type(char);
            await this.randomDelay(50, 150);
        }
    }
    async extractBusinessData(page) {
        logger.debug('Extracting business data from page');
        try {
            await page.waitForLoadState('networkidle', { timeout: 10000 });
            const data = {};
            try {
                const nameElement = await page.locator('[data-attrid="title"]').first();
                if (await nameElement.isVisible()) {
                    data.businessName = await nameElement.textContent() || '';
                }
            }
            catch (error) {
                logger.debug('Could not extract business name');
            }
            try {
                const ratingElement = await page.locator('[data-attrid="kc:/collection/knowledge_panels/local_business/rating:star_rating"]').first();
                if (await ratingElement.isVisible()) {
                    const ratingText = await ratingElement.textContent();
                    if (ratingText) {
                        const rating = parseFloat(ratingText.match(/[\d.]+/)?.[0] || '0');
                        data.rating = rating;
                    }
                }
            }
            catch (error) {
                logger.debug('Could not extract rating');
            }
            try {
                const reviewElement = await page.locator('text=/\\d+[,\\d]*\\s+reviews?/i').first();
                if (await reviewElement.isVisible()) {
                    const reviewText = await reviewElement.textContent();
                    if (reviewText) {
                        const reviewCount = parseInt(reviewText.replace(/[^\d]/g, ''));
                        data.reviewCount = reviewCount;
                    }
                }
            }
            catch (error) {
                logger.debug('Could not extract review count');
            }
            try {
                const addressElement = await page.locator('[data-attrid="kc:/collection/knowledge_panels/local_business/address"]').first();
                if (await addressElement.isVisible()) {
                    data.address = await addressElement.textContent() || '';
                }
            }
            catch (error) {
                logger.debug('Could not extract address');
            }
            try {
                const phoneElement = await page.locator('[data-attrid="kc:/collection/knowledge_panels/local_business/phone"]').first();
                if (await phoneElement.isVisible()) {
                    data.phone = await phoneElement.textContent() || '';
                }
            }
            catch (error) {
                logger.debug('Could not extract phone');
            }
            try {
                const websiteElement = await page.locator('[data-attrid="kc:/collection/knowledge_panels/local_business/website"] a').first();
                if (await websiteElement.isVisible()) {
                    data.website = await websiteElement.getAttribute('href') || '';
                }
            }
            catch (error) {
                logger.debug('Could not extract website');
            }
            try {
                const hoursButton = await page.locator('text=/hours/i').first();
                if (await hoursButton.isVisible()) {
                    await hoursButton.click();
                    await this.randomDelay(1000, 2000);
                    const hoursData = {};
                    const dayElements = await page.locator('[data-attrid*="hours"]').all();
                    for (const dayElement of dayElements) {
                        const dayText = await dayElement.textContent();
                        if (dayText) {
                            const [day, hours] = dayText.split(':');
                            if (day && hours) {
                                hoursData[day.trim()] = hours.trim();
                            }
                        }
                    }
                    data.hours = hoursData;
                }
            }
            catch (error) {
                logger.debug('Could not extract hours');
            }
            data.scrapedAt = new Date();
            data.googleUrl = page.url();
            logger.info('Successfully extracted business data', { businessName: data.businessName });
            return data;
        }
        catch (error) {
            logger.error('Error extracting business data', { error: error.message });
            throw error;
        }
    }
    async scrapeBusinessData(request) {
        const startTime = Date.now();
        logger.info('Starting GMB scraping', { businessName: request.businessName });
        let context = null;
        let page = null;
        try {
            context = await this.createStealthContext();
            page = await context.newPage();
            await page.goto('https://www.google.com', { waitUntil: 'networkidle' });
            await this.randomDelay(1000, 2000);
            const searchQuery = `${request.businessName} ${request.address}`;
            await this.humanLikeTyping(page, 'input[name="q"]', searchQuery);
            await this.randomDelay(500, 1000);
            await page.keyboard.press('Enter');
            await page.waitForLoadState('networkidle', { timeout: 15000 });
            await this.randomDelay(2000, 4000);
            const businessData = await this.extractBusinessData(page);
            const result = {
                businessName: request.businessName,
                address: request.address,
                placeId: request.placeId,
                scrapedAt: new Date(),
                ...businessData,
            };
            const duration = Date.now() - startTime;
            logger.info('GMB scraping completed successfully', {
                businessName: request.businessName,
                duration: `${duration}ms`
            });
            return result;
        }
        catch (error) {
            logger.error('GMB scraping failed', {
                businessName: request.businessName,
                error: error.message
            });
            throw error;
        }
        finally {
            if (page) {
                await page.close();
            }
            if (context) {
                await context.close();
                this.contexts = this.contexts.filter(ctx => ctx !== context);
            }
        }
    }
    async close() {
        logger.info('Closing GMB scraping service');
        for (const context of this.contexts) {
            try {
                await context.close();
            }
            catch (error) {
                logger.warn('Error closing context', { error });
            }
        }
        this.contexts = [];
        if (this.browser) {
            try {
                await this.browser.close();
                this.browser = null;
            }
            catch (error) {
                logger.warn('Error closing browser', { error });
            }
        }
    }
}
exports.GMBScrapingService = GMBScrapingService;
//# sourceMappingURL=GMBScrapingService.js.map