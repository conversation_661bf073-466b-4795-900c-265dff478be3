interface BusinessData {
    businessName: string;
    address?: string;
    phone?: string;
    website?: string;
    reviews?: any[];
    photos?: any[];
    category?: string;
    description?: string;
}
export interface VerificationResult {
    isVerified: boolean;
    confidence: number;
    sources: string[];
    issues: string[];
    verifiedData: Partial<BusinessData>;
    methodology: string;
}
export interface DataSource {
    name: string;
    url?: string;
    reliability: number;
    lastChecked: Date;
}
export declare class BusinessDataVerifier {
    private readonly dataSources;
    verifyBusinessData(inputData: BusinessData): Promise<VerificationResult>;
    private validateDataCompleteness;
    private verifyBusinessName;
    private verifyAddress;
    private verifyPhoneNumber;
    private verifyWebsite;
    private buildVerifiedData;
    private getMethodologyExplanation;
    generateReliabilityReport(verification: VerificationResult): string;
}
export {};
//# sourceMappingURL=BusinessDataVerifier.d.ts.map