import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import { createLogger } from 'winston';
// Import services - will be implemented step by step
// import { ReportGenerator } from './services/ReportGenerator';
// import { TemplateManager } from './services/TemplateManager';
// import { VisualizationEngine } from './services/VisualizationEngine';
// import { DeliveryService } from './services/DeliveryService';
import SimpleReportGenerator from './services/SimpleReportGenerator';
import SimpleVisualizationEngine from './services/SimpleVisualizationEngine';
import SimpleDeliveryService from './services/SimpleDeliveryService';
import AnalyzerClient from './services/AnalyzerClient';
import { WorldClassReportGenerator } from './services/WorldClassReportGenerator';
import {
  validateReportRequest,
  validateEmailDelivery,
  validateWhatsAppDelivery,
  validateChartGeneration,
  validateReportId,
  validateIntegratedReport
} from './middleware/validation-simple';
// import { errorHandler } from './middleware/errorHandler';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.REPORT_GENERATOR_PORT || 3003;

// Initialize services
const reportGenerator = new SimpleReportGenerator();
const visualizationEngine = new SimpleVisualizationEngine();
const deliveryService = new SimpleDeliveryService();
const analyzerClient = new AnalyzerClient();
const worldClassReportGenerator = new WorldClassReportGenerator();

// Configure logger
const logger = createLogger({
  level: 'info',
  format: require('winston').format.combine(
    require('winston').format.timestamp(),
    require('winston').format.json()
  ),
  transports: [
    new (require('winston').transports.Console)(),
    new (require('winston').transports.File)({ filename: 'logs/report-generator.log' })
  ]
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize services - placeholder for now
// const reportGenerator = new ReportGenerator();
// const templateManager = new TemplateManager();
// const visualizationEngine = new VisualizationEngine();
// const deliveryService = new DeliveryService();

// Health check endpoint with analyzer service status
app.get('/health', async (req, res) => {
  try {
    const analyzerHealthy = await analyzerClient.healthCheck();

    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        service: 'GMB Report Generator',
        environment: process.env.NODE_ENV || 'development',
        dependencies: {
          analyzerService: analyzerHealthy ? 'healthy' : 'unavailable'
        }
      }
    });
  } catch (error) {
    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        service: 'GMB Report Generator',
        environment: process.env.NODE_ENV || 'development',
        dependencies: {
          analyzerService: 'unavailable'
        }
      }
    });
  }
});

// Generate PDF report endpoint (placeholder)
app.post('/api/generate/pdf', validateReportRequest, async (req, res) => {
  try {
    const { analysisData, businessData, options = {} } = req.body;

    logger.info('PDF generation requested', {
      businessName: businessData?.businessName,
      template: options.template || 'default'
    });

    // Generate report using the service
    const reportData = {
      businessData,
      analysisData,
      options: {
        ...options,
        format: 'pdf'
      }
    };

    const result = await reportGenerator.generatePDFReport(reportData);

    res.json({
      success: true,
      data: {
        ...result,
        filename: `GMB-Audit-Report-${businessData.businessName?.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.pdf`,
        downloadUrl: `/api/download/${result.reportId}`
      }
    });

  } catch (error) {
    logger.error('PDF generation request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process PDF generation request',
        code: 'PDF_GENERATION_ERROR'
      }
    });
  }
});

// Integrated analysis and report generation endpoint
app.post('/api/generate/integrated-report', validateIntegratedReport, async (req, res) => {
  try {
    const { businessData, competitorData, options = {} } = req.body;

    logger.info('Integrated report generation requested', {
      businessName: businessData.businessName,
      hasCompetitors: !!competitorData?.length
    });

    // Step 1: Get analysis from AI Analysis Engine
    const analysisResponse = await analyzerClient.analyzeBusinessProfile(businessData, competitorData);

    if (!analysisResponse.success) {
      throw new Error('Failed to analyze business profile');
    }

    // Step 2: Generate report with real analysis data
    const reportData = {
      businessData,
      analysisData: analysisResponse.data,
      options: { ...options, format: 'pdf' }
    };

    const reportResult = await reportGenerator.generatePDFReport(reportData);

    // Step 3: Generate visualizations with real data
    const chartTypes = options.chartTypes || ['score', 'breakdown', 'competitive'];
    const chartRequests = chartTypes.map((type: string) => ({
      chartType: type === 'score' ? 'score-gauge' :
                 type === 'breakdown' ? 'breakdown-chart' :
                 type === 'competitive' ? 'competitive-comparison' : 'insights-matrix',
      data: analysisResponse.data,
      options: {}
    }));

    const chartResults: any = {};
    for (const request of chartRequests) {
      const chartResult = await visualizationEngine.generateChart(request);
      chartResults[request.chartType.replace('-gauge', '').replace('-chart', '').replace('-comparison', '')] = chartResult;
    }

    res.json({
      success: true,
      data: {
        report: reportResult,
        charts: chartResults,
        analysis: {
          scores: analysisResponse.data.scores,
          insights: analysisResponse.data.insights,
          recommendations: analysisResponse.data.recommendations,
          overallHealth: analysisResponse.data.analysis.overallHealth
        },
        timestamp: new Date().toISOString(),
        source: 'integrated_analysis'
      }
    });

  } catch (error) {
    logger.error('Integrated report generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to generate integrated report',
        code: 'INTEGRATED_REPORT_ERROR',
        details: error instanceof Error ? error.message : String(error)
      }
    });
  }
});

// World-Class Report Generation endpoint
app.post('/api/generate/world-class-report', validateIntegratedReport, async (req, res) => {
  try {
    const { businessData, competitorData, options = {} } = req.body;

    logger.info('World-class report generation requested', {
      businessName: businessData.businessName,
      hasCompetitors: !!competitorData?.length,
      template: options.template || 'default'
    });

    // Step 1: Get analysis from AI Analysis Engine
    const analysisResponse = await analyzerClient.analyzeBusinessProfile(businessData, competitorData);

    if (!analysisResponse.success) {
      throw new Error('Failed to analyze business profile');
    }

    // Step 2: Generate world-class report with enhanced structure
    const worldClassReportData = {
      businessData,
      analysisData: analysisResponse.data,
      options: {
        ...options,
        template: options.template || 'healthcare', // Default to healthcare for dental
        branding: {
          companyName: 'GMB Audit Pro',
          primaryColor: '#2563eb',
          secondaryColor: '#059669',
          ...options.branding
        },
        includeAppendix: options.includeAppendix !== false,
        includeTemplates: options.includeTemplates !== false
      }
    };

    const worldClassResult = await worldClassReportGenerator.generateWorldClassReport(worldClassReportData);

    // Step 3: Generate enhanced visualizations
    const chartTypes = options.chartTypes || ['score', 'breakdown', 'competitive', 'sentiment'];
    const chartRequests = chartTypes.map((type: string) => ({
      chartType: type === 'score' ? 'score-gauge' :
                 type === 'breakdown' ? 'breakdown-chart' :
                 type === 'competitive' ? 'competitive-comparison' :
                 type === 'sentiment' ? 'insights-matrix' : 'insights-matrix',
      data: analysisResponse.data,
      options: { enhanced: true, worldClass: true }
    }));

    const chartResults: any = {};
    for (const request of chartRequests) {
      const chartResult = await visualizationEngine.generateChart(request);
      chartResults[request.chartType.replace('-gauge', '').replace('-chart', '').replace('-comparison', '').replace('-analysis', '')] = chartResult;
    }

    res.json({
      success: true,
      data: {
        report: {
          reportId: worldClassResult.reportId,
          htmlContent: worldClassResult.htmlContent,
          size: worldClassResult.size,
          generationTime: worldClassResult.generationTime,
          format: 'world-class-html',
          template: options.template || 'healthcare',
          features: [
            'Professional cover page',
            'Executive summary',
            'Visual scorecard dashboard',
            'Geographic heatmap',
            'Image quality audit',
            'Review sentiment analysis',
            'Strategic recommendations',
            '30-day engagement plan',
            'Ready-to-use templates',
            'Technical appendix'
          ]
        },
        charts: chartResults,
        analysis: {
          scores: analysisResponse.data.scores,
          insights: analysisResponse.data.insights,
          recommendations: analysisResponse.data.recommendations,
          overallHealth: analysisResponse.data.analysis.overallHealth
        },
        businessImpact: {
          potentialRevenueIncrease: '25-40%',
          patientAcquisitionIncrease: '15-25 new patients/month',
          localSearchImprovement: '3-5 position improvement',
          reviewGenerationIncrease: '10-15 new reviews/month',
          photoEngagementIncrease: '50-75% increase in profile views'
        },
        timestamp: new Date().toISOString(),
        source: 'world_class_analysis',
        version: '2.0'
      }
    });

  } catch (error) {
    logger.error('World-class report generation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to generate world-class report',
        code: 'WORLD_CLASS_REPORT_ERROR',
        details: error instanceof Error ? error.message : String(error)
      }
    });
  }
});

// Generate visualizations endpoint (placeholder)
app.post('/api/generate/charts', validateChartGeneration, async (req, res) => {
  try {
    const { analysisData, chartTypes = ['score', 'breakdown', 'competitive'] } = req.body;

    logger.info('Chart generation requested', { chartTypes });

    // Generate charts using the visualization engine
    const chartRequests = chartTypes.map((type: string) => ({
      chartType: type === 'score' ? 'score-gauge' :
                 type === 'breakdown' ? 'breakdown-chart' :
                 type === 'competitive' ? 'competitive-comparison' : 'insights-matrix',
      data: analysisData,
      options: {}
    }));

    const chartResults = await visualizationEngine.generateMultipleCharts(chartRequests);

    const charts: any = {};
    chartResults.forEach((result, index) => {
      const originalType = chartTypes[index];
      charts[originalType] = {
        id: result.chartId,
        type: result.chartType,
        data: result.data,
        metadata: {
          width: 800,
          height: 600,
          title: `${originalType} Chart`,
          timestamp: result.timestamp
        }
      };
    });

    res.json({
      success: true,
      data: {
        charts,
        timestamp: new Date().toISOString(),
        status: 'generated'
      }
    });

  } catch (error) {
    logger.error('Chart generation request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process chart generation request',
        code: 'CHART_GENERATION_ERROR'
      }
    });
  }
});

// Email delivery endpoint
app.post('/api/deliver/email', validateEmailDelivery, async (req, res) => {
  try {
    const { reportId, recipient, subject, message } = req.body;

    logger.info('Email delivery requested', { reportId, recipient });

    // Send email using delivery service
    const deliveryRequest = {
      reportId,
      recipient,
      subject,
      message,
      attachPDF: true
    };

    const result = await deliveryService.sendEmailReport(deliveryRequest);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Email delivery request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process email delivery request',
        code: 'EMAIL_DELIVERY_ERROR'
      }
    });
  }
});

// WhatsApp delivery endpoint
app.post('/api/deliver/whatsapp', validateWhatsAppDelivery, async (req, res) => {
  try {
    const { reportId, phoneNumber, message } = req.body;

    logger.info('WhatsApp delivery requested', { reportId, phoneNumber });

    // Send WhatsApp message using delivery service
    const deliveryRequest = {
      reportId,
      phoneNumber,
      message,
      includeLink: true
    };

    const result = await deliveryService.sendWhatsAppReport(deliveryRequest);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('WhatsApp delivery request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process WhatsApp delivery request',
        code: 'WHATSAPP_DELIVERY_ERROR'
      }
    });
  }
});

// Download report endpoint (placeholder)
app.get('/api/download/:reportId', validateReportId, async (req, res) => {
  try {
    const { reportId } = req.params;

    logger.info('Report download requested', { reportId });

    // Placeholder response
    res.json({
      success: true,
      data: {
        reportId,
        message: 'Download functionality will be implemented in Phase 4',
        timestamp: new Date().toISOString(),
        status: 'Phase 4 implementation in progress'
      }
    });

  } catch (error) {
    logger.error('Report download request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process download request',
        code: 'DOWNLOAD_ERROR'
      }
    });
  }
});

// Web portal endpoint - serve HTML report
app.get('/api/portal/:reportId', validateReportId, async (req, res) => {
  try {
    const { reportId } = req.params;

    logger.info('Portal access requested', { reportId });

    // Get cached report
    const cachedReport = reportGenerator.getCachedReport(reportId);

    if (!cachedReport) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Report not found',
          code: 'REPORT_NOT_FOUND'
        }
      });
    }

    if (cachedReport.htmlContent) {
      // Serve HTML content directly
      res.setHeader('Content-Type', 'text/html');
      res.send(cachedReport.htmlContent);
    } else {
      // Return JSON response if no HTML content
      res.json({
        success: true,
        data: {
          reportId,
          title: 'GMB Audit Report Portal',
          format: cachedReport.format,
          template: cachedReport.template,
          timestamp: cachedReport.timestamp,
          status: cachedReport.status
        }
      });
    }

  } catch (error) {
    logger.error('Portal access request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process portal access request',
        code: 'PORTAL_ACCESS_ERROR'
      }
    });
  }
});

// Template management endpoints (placeholder)
app.get('/api/templates', async (req, res) => {
  try {
    logger.info('Template listing requested');

    // Placeholder response
    const templates = [
      { id: 'default', name: 'Default Template', description: 'Standard GMB audit report' },
      { id: 'restaurant', name: 'Restaurant Template', description: 'Specialized for restaurants' },
      { id: 'service', name: 'Service Business Template', description: 'For service-based businesses' },
      { id: 'healthcare', name: 'Healthcare Template', description: 'Medical and healthcare businesses' }
    ];

    res.json({
      success: true,
      data: {
        templates,
        status: 'Phase 4 implementation in progress'
      }
    });
  } catch (error) {
    logger.error('Template listing request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process template listing request',
        code: 'TEMPLATE_LIST_ERROR'
      }
    });
  }
});

// Multi-channel delivery endpoint
app.post('/api/deliver/multi', async (req, res) => {
  try {
    const { reportId, email, whatsapp } = req.body;

    logger.info('Multi-channel delivery requested', { reportId, email: !!email, whatsapp: !!whatsapp });

    const results = await deliveryService.sendMultiChannelReport(email, whatsapp);

    res.json({
      success: true,
      data: {
        deliveries: results,
        totalSent: results.filter(r => r.status === 'sent').length,
        totalFailed: results.filter(r => r.status === 'failed').length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Multi-channel delivery failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to process multi-channel delivery',
        code: 'MULTI_DELIVERY_ERROR'
      }
    });
  }
});

// Delivery statistics endpoint (must come before :deliveryId route)
app.get('/api/delivery/stats', async (req, res) => {
  try {
    const stats = deliveryService.getDeliveryStats();
    const history = deliveryService.getDeliveryHistory(10);

    res.json({
      success: true,
      data: {
        statistics: stats,
        recentDeliveries: history,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Delivery stats request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get delivery statistics',
        code: 'DELIVERY_STATS_ERROR'
      }
    });
  }
});

// Delivery status endpoint
app.get('/api/delivery/:deliveryId', async (req, res) => {
  try {
    const { deliveryId } = req.params;
    const delivery = deliveryService.getDeliveryStatus(deliveryId);

    if (!delivery) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Delivery not found',
          code: 'DELIVERY_NOT_FOUND'
        }
      });
    }

    res.json({
      success: true,
      data: delivery
    });

  } catch (error) {
    logger.error('Delivery status request failed', { error: error instanceof Error ? error.message : String(error) });
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get delivery status',
        code: 'DELIVERY_STATUS_ERROR'
      }
    });
  }
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    success: true,
    data: {
      service: 'GMB Report Generator API',
      version: '1.0.0',
      endpoints: {
        health: '/health',
        generatePDF: '/api/generate/pdf',
        generateIntegratedReport: '/api/generate/integrated-report',
        generateWorldClassReport: '/api/generate/world-class-report',
        generateCharts: '/api/generate/charts',
        deliverEmail: '/api/deliver/email',
        deliverWhatsApp: '/api/deliver/whatsapp',
        deliverMulti: '/api/deliver/multi',
        deliveryStatus: '/api/delivery/:deliveryId',
        deliveryStats: '/api/delivery/stats',
        download: '/api/download/:reportId',
        portal: '/api/portal/:reportId',
        templates: '/api/templates'
      },
      description: 'Professional report generation and delivery service for GMB audits'
    }
  });
});

// Error handling middleware (placeholder)
// app.use(errorHandler);

// Start server
const server = app.listen(PORT, () => {
  logger.info(`GMB Report Generator started on port ${PORT}`);
  logger.info('Available endpoints:');
  logger.info('  GET  /health - Health check');
  logger.info('  POST /api/generate/pdf - Generate PDF report');
  logger.info('  POST /api/generate/charts - Generate visualizations');
  logger.info('  POST /api/deliver/email - Send report via email');
  logger.info('  POST /api/deliver/whatsapp - Send report via WhatsApp');
  logger.info('  GET  /api/download/:reportId - Download report');
  logger.info('  GET  /api/portal/:reportId - Web portal access');
  logger.info('  GET  /api/templates - List available templates');
  logger.info('  GET  /api/docs - API documentation');
});

// Handle server errors
server.on('error', (error: any) => {
  logger.error('Server error:', error);
});

// Handle process errors
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection at:', promise, 'reason:', reason);
});

// Keep the process alive
process.on('SIGINT', () => {
  logger.info('Received SIGINT, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

export default app;
