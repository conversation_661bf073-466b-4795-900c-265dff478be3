"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateIntegratedReport = exports.validateReportId = exports.validateWhatsAppDelivery = exports.validateEmailDelivery = exports.validateChartGeneration = exports.validateReportRequest = void 0;
const joi_1 = __importDefault(require("joi"));
const reportRequestSchema = joi_1.default.object({
    analysisData: joi_1.default.object().required(),
    businessData: joi_1.default.object({
        businessName: joi_1.default.string().required(),
        address: joi_1.default.string().optional(),
        phone: joi_1.default.string().optional(),
        website: joi_1.default.string().optional()
    }).required(),
    options: joi_1.default.object().optional()
});
const chartGenerationSchema = joi_1.default.object({
    analysisData: joi_1.default.object().required(),
    chartTypes: joi_1.default.array().items(joi_1.default.string()).optional()
});
const emailDeliverySchema = joi_1.default.object({
    reportId: joi_1.default.string().required(),
    recipient: joi_1.default.string().email().required(),
    subject: joi_1.default.string().optional(),
    message: joi_1.default.string().optional()
});
const whatsappDeliverySchema = joi_1.default.object({
    reportId: joi_1.default.string().required(),
    phoneNumber: joi_1.default.string().required(),
    message: joi_1.default.string().optional()
});
const reportIdSchema = joi_1.default.object({
    reportId: joi_1.default.string().required()
});
const integratedReportSchema = joi_1.default.object({
    businessData: joi_1.default.object({
        businessName: joi_1.default.string().required(),
        address: joi_1.default.string().optional(),
        phone: joi_1.default.string().optional(),
        website: joi_1.default.string().optional(),
        reviews: joi_1.default.array().optional(),
        photos: joi_1.default.array().optional(),
        posts: joi_1.default.array().optional(),
        rankings: joi_1.default.array().optional(),
        seoFactors: joi_1.default.object().optional(),
        citations: joi_1.default.array().optional()
    }).required(),
    competitorData: joi_1.default.array().optional(),
    options: joi_1.default.object().optional()
});
const validateReportRequest = (req, res, next) => {
    const { error } = reportRequestSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid request data',
                details: error.details.map(d => d.message)
            }
        });
    }
    next();
};
exports.validateReportRequest = validateReportRequest;
const validateChartGeneration = (req, res, next) => {
    const { error } = chartGenerationSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid chart generation request',
                details: error.details.map(d => d.message)
            }
        });
    }
    next();
};
exports.validateChartGeneration = validateChartGeneration;
const validateEmailDelivery = (req, res, next) => {
    const { error } = emailDeliverySchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid email delivery request',
                details: error.details.map(d => d.message)
            }
        });
    }
    next();
};
exports.validateEmailDelivery = validateEmailDelivery;
const validateWhatsAppDelivery = (req, res, next) => {
    const { error } = whatsappDeliverySchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid WhatsApp delivery request',
                details: error.details.map(d => d.message)
            }
        });
    }
    next();
};
exports.validateWhatsAppDelivery = validateWhatsAppDelivery;
const validateReportId = (req, res, next) => {
    const { error } = reportIdSchema.validate(req.params);
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid report ID',
                details: error.details.map(d => d.message)
            }
        });
    }
    next();
};
exports.validateReportId = validateReportId;
const validateIntegratedReport = (req, res, next) => {
    const { error } = integratedReportSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid integrated report request',
                details: error.details.map(d => d.message)
            }
        });
    }
    next();
};
exports.validateIntegratedReport = validateIntegratedReport;
//# sourceMappingURL=validation-simple.js.map