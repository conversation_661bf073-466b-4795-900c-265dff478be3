"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationEngine = void 0;
const winston_1 = __importDefault(require("winston"));
const uuid_1 = require("uuid");
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.json(),
    transports: [new winston_1.default.transports.Console()]
});
class RecommendationEngine {
    async generateRecommendations(analysis, scores, insights) {
        try {
            logger.info('Generating actionable recommendations');
            const recommendations = [];
            recommendations.push(...this.generateScoreBasedRecommendations(scores));
            recommendations.push(...this.generateInsightBasedRecommendations(insights));
            recommendations.push(...this.generateSEORecommendations(analysis.seoAnalysis, scores.breakdown.seo));
            recommendations.push(...this.generateReviewRecommendations(analysis.sentimentAnalysis, scores.breakdown.reviews));
            recommendations.push(...this.generatePhotoRecommendations(analysis.photoAnalysis, scores.breakdown.photos));
            const sortedRecommendations = recommendations
                .sort((a, b) => {
                const aPriority = this.getPriorityWeight(a.priority);
                const bPriority = this.getPriorityWeight(b.priority);
                if (aPriority !== bPriority)
                    return bPriority - aPriority;
                return b.expectedImpact.scoreIncrease - a.expectedImpact.scoreIncrease;
            })
                .slice(0, 15);
            logger.info(`Generated ${sortedRecommendations.length} recommendations`);
            return sortedRecommendations;
        }
        catch (error) {
            logger.error('Error generating recommendations', { error: error.message });
            return [];
        }
    }
    generateScoreBasedRecommendations(scores) {
        const recommendations = [];
        const sortedAreas = Object.entries(scores.breakdown)
            .sort(([, a], [, b]) => a - b)
            .slice(0, 3);
        for (const [area, score] of sortedAreas) {
            if (score < 70) {
                recommendations.push(this.createAreaRecommendation(area, score));
            }
        }
        return recommendations;
    }
    generateInsightBasedRecommendations(insights) {
        const recommendations = [];
        const highImpactInsights = insights.filter(insight => insight.impact === 'high' && (insight.type === 'weakness' || insight.type === 'opportunity'));
        for (const insight of highImpactInsights.slice(0, 5)) {
            recommendations.push({
                id: (0, uuid_1.v4)(),
                priority: insight.impact === 'high' ? 'high' : 'medium',
                category: insight.category,
                title: `Address: ${insight.title}`,
                description: insight.description,
                actionItems: insight.recommendations.map(rec => ({
                    task: rec,
                    effort: 'medium',
                    timeline: '1-2 weeks',
                    impact: insight.impact === 'high' ? 85 : 65
                })),
                expectedImpact: {
                    scoreIncrease: insight.impact === 'high' ? 15 : 8,
                    timeframe: '2-4 weeks',
                    confidence: insight.confidence
                },
                resources: this.getResourcesForCategory(insight.category)
            });
        }
        return recommendations;
    }
    generateSEORecommendations(seoAnalysis, seoScore) {
        const recommendations = [];
        if (seoScore < 80) {
            if (seoAnalysis.factors?.description?.score < 70) {
                recommendations.push({
                    id: (0, uuid_1.v4)(),
                    priority: 'high',
                    category: 'seo',
                    title: 'Optimize Business Description',
                    description: 'Improve your business description with relevant keywords and compelling content.',
                    actionItems: [
                        { task: 'Research relevant local keywords', effort: 'medium', timeline: '1 week', impact: 80 },
                        { task: 'Rewrite description with keywords', effort: 'low', timeline: '2 days', impact: 85 },
                        { task: 'Ensure description is 100-750 characters', effort: 'low', timeline: '1 day', impact: 70 }
                    ],
                    expectedImpact: {
                        scoreIncrease: 12,
                        timeframe: '2-3 weeks',
                        confidence: 90
                    },
                    resources: [
                        {
                            type: 'guide',
                            title: 'GMB Description Best Practices',
                            description: 'Complete guide to writing effective business descriptions'
                        },
                        {
                            type: 'tool',
                            title: 'Keyword Research Tool',
                            description: 'Find relevant local keywords for your business'
                        }
                    ]
                });
            }
            if (seoAnalysis.factors?.categories?.score < 80) {
                recommendations.push({
                    id: (0, uuid_1.v4)(),
                    priority: 'medium',
                    category: 'seo',
                    title: 'Add Relevant Business Categories',
                    description: 'Select the most accurate primary and secondary categories for better search visibility.',
                    actionItems: [
                        { task: 'Research competitor categories', effort: 'low', timeline: '1 day', impact: 75 },
                        { task: 'Select optimal primary category', effort: 'low', timeline: '1 day', impact: 85 },
                        { task: 'Add relevant secondary categories', effort: 'low', timeline: '1 day', impact: 70 }
                    ],
                    expectedImpact: {
                        scoreIncrease: 8,
                        timeframe: '1-2 weeks',
                        confidence: 85
                    },
                    resources: [
                        {
                            type: 'guide',
                            title: 'GMB Category Selection Guide',
                            description: 'How to choose the best categories for your business'
                        }
                    ]
                });
            }
        }
        return recommendations;
    }
    generateReviewRecommendations(sentimentAnalysis, reviewScore) {
        const recommendations = [];
        if (reviewScore < 70) {
            recommendations.push({
                id: (0, uuid_1.v4)(),
                priority: 'high',
                category: 'reviews',
                title: 'Implement Review Generation Strategy',
                description: 'Increase your review volume and improve overall rating through systematic review requests.',
                actionItems: [
                    { task: 'Set up automated review request system', effort: 'high', timeline: '1 week', impact: 90 },
                    { task: 'Train staff to request reviews', effort: 'medium', timeline: '3 days', impact: 80 },
                    { task: 'Create review request templates', effort: 'low', timeline: '2 days', impact: 70 },
                    { task: 'Follow up with satisfied customers', effort: 'medium', timeline: 'ongoing', impact: 85 }
                ],
                expectedImpact: {
                    scoreIncrease: 20,
                    timeframe: '4-8 weeks',
                    confidence: 85
                },
                resources: [
                    {
                        type: 'template',
                        title: 'Review Request Email Templates',
                        description: 'Professional templates for requesting customer reviews'
                    },
                    {
                        type: 'tool',
                        title: 'Review Management Platform',
                        description: 'Automate review requests and management'
                    }
                ]
            });
        }
        if (sentimentAnalysis.responseAnalysis?.responseRate < 50) {
            recommendations.push({
                id: (0, uuid_1.v4)(),
                priority: 'medium',
                category: 'reviews',
                title: 'Improve Review Response Rate',
                description: 'Respond to customer reviews to show engagement and improve reputation.',
                actionItems: [
                    { task: 'Respond to all recent negative reviews', effort: 'medium', timeline: '1 week', impact: 85 },
                    { task: 'Thank customers for positive reviews', effort: 'low', timeline: '3 days', impact: 70 },
                    { task: 'Create response templates', effort: 'low', timeline: '2 days', impact: 65 },
                    { task: 'Set up review monitoring alerts', effort: 'medium', timeline: '1 day', impact: 75 }
                ],
                expectedImpact: {
                    scoreIncrease: 10,
                    timeframe: '2-4 weeks',
                    confidence: 80
                },
                resources: [
                    {
                        type: 'template',
                        title: 'Review Response Templates',
                        description: 'Professional templates for responding to reviews'
                    }
                ]
            });
        }
        return recommendations;
    }
    generatePhotoRecommendations(photoAnalysis, photoScore) {
        const recommendations = [];
        if (photoScore < 70) {
            recommendations.push({
                id: (0, uuid_1.v4)(),
                priority: 'medium',
                category: 'photos',
                title: 'Enhance Photo Portfolio',
                description: 'Add high-quality photos to improve visual appeal and search rankings.',
                actionItems: [
                    { task: 'Take professional exterior photos', effort: 'medium', timeline: '1 week', impact: 80 },
                    { task: 'Capture interior and workspace photos', effort: 'medium', timeline: '1 week', impact: 75 },
                    { task: 'Add product/service photos', effort: 'medium', timeline: '1 week', impact: 85 },
                    { task: 'Include team and staff photos', effort: 'low', timeline: '3 days', impact: 70 }
                ],
                expectedImpact: {
                    scoreIncrease: 12,
                    timeframe: '2-3 weeks',
                    confidence: 85
                },
                resources: [
                    {
                        type: 'guide',
                        title: 'GMB Photo Best Practices',
                        description: 'Guidelines for taking effective business photos'
                    }
                ]
            });
        }
        return recommendations;
    }
    createAreaRecommendation(area, score) {
        const areaConfig = {
            reviews: {
                title: 'Boost Review Performance',
                description: 'Improve your review quantity, quality, and response rate.',
                impact: 18
            },
            visibility: {
                title: 'Enhance Local Visibility',
                description: 'Improve your local search rankings and online presence.',
                impact: 15
            },
            seo: {
                title: 'Optimize SEO Factors',
                description: 'Enhance your business information for better search performance.',
                impact: 12
            },
            photos: {
                title: 'Improve Photo Strategy',
                description: 'Add more high-quality photos to showcase your business.',
                impact: 10
            },
            posts: {
                title: 'Increase Google Posts Activity',
                description: 'Regular posting keeps your profile active and engaging.',
                impact: 8
            },
            nap: {
                title: 'Fix NAP Consistency',
                description: 'Ensure your business information is consistent across all platforms.',
                impact: 10
            }
        };
        const config = areaConfig[area] || areaConfig.seo;
        return {
            id: (0, uuid_1.v4)(),
            priority: score < 50 ? 'critical' : score < 70 ? 'high' : 'medium',
            category: area,
            title: config.title,
            description: config.description,
            actionItems: [
                { task: `Analyze current ${area} performance`, effort: 'low', timeline: '1 day', impact: 60 },
                { task: `Implement ${area} improvements`, effort: 'medium', timeline: '1-2 weeks', impact: 80 },
                { task: `Monitor ${area} progress`, effort: 'low', timeline: 'ongoing', impact: 70 }
            ],
            expectedImpact: {
                scoreIncrease: config.impact,
                timeframe: '3-6 weeks',
                confidence: 80
            },
            resources: this.getResourcesForCategory(area)
        };
    }
    getResourcesForCategory(category) {
        const resourceMap = {
            seo: [
                { type: 'guide', title: 'GMB SEO Guide', description: 'Complete SEO optimization guide' },
                { type: 'tool', title: 'SEO Analysis Tool', description: 'Analyze your SEO performance' }
            ],
            reviews: [
                { type: 'template', title: 'Review Templates', description: 'Templates for managing reviews' },
                { type: 'tool', title: 'Review Management', description: 'Automate review processes' }
            ],
            photos: [
                { type: 'guide', title: 'Photo Guidelines', description: 'Best practices for business photos' }
            ],
            competitive: [
                { type: 'tool', title: 'Competitor Analysis', description: 'Track competitor performance' }
            ]
        };
        return resourceMap[category] || [];
    }
    getPriorityWeight(priority) {
        switch (priority) {
            case 'critical': return 4;
            case 'high': return 3;
            case 'medium': return 2;
            case 'low': return 1;
            default: return 1;
        }
    }
}
exports.RecommendationEngine = RecommendationEngine;
//# sourceMappingURL=RecommendationEngine.js.map