import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

// Simplified validation schemas for Phase 4 development
const reportRequestSchema = Joi.object({
  analysisData: Joi.object().required(),
  businessData: Joi.object({
    businessName: Joi.string().required(),
    address: Joi.string().optional(),
    phone: Joi.string().optional(),
    website: Joi.string().optional()
  }).required(),
  options: Joi.object().optional()
});

const chartGenerationSchema = Joi.object({
  analysisData: Joi.object().required(),
  chartTypes: Joi.array().items(Joi.string()).optional()
});

const emailDeliverySchema = Joi.object({
  reportId: Joi.string().required(),
  recipient: Joi.string().email().required(),
  subject: Joi.string().optional(),
  message: Joi.string().optional()
});

const whatsappDeliverySchema = Joi.object({
  reportId: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  message: Joi.string().optional()
});

const reportIdSchema = Joi.object({
  reportId: Joi.string().required()
});

const integratedReportSchema = Joi.object({
  businessData: Joi.object({
    businessName: Joi.string().required(),
    address: Joi.string().optional(),
    phone: Joi.string().optional(),
    website: Joi.string().optional(),
    reviews: Joi.array().optional(),
    photos: Joi.array().optional(),
    posts: Joi.array().optional(),
    rankings: Joi.array().optional(),
    seoFactors: Joi.object().optional(),
    citations: Joi.array().optional()
  }).required(),
  competitorData: Joi.array().optional(),
  options: Joi.object().optional()
});

// Validation middleware functions
export const validateReportRequest = (req: Request, res: Response, next: NextFunction) => {
  const { error } = reportRequestSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid request data',
        details: error.details.map(d => d.message)
      }
    });
  }
  next();
};

export const validateChartGeneration = (req: Request, res: Response, next: NextFunction) => {
  const { error } = chartGenerationSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid chart generation request',
        details: error.details.map(d => d.message)
      }
    });
  }
  next();
};

export const validateEmailDelivery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = emailDeliverySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid email delivery request',
        details: error.details.map(d => d.message)
      }
    });
  }
  next();
};

export const validateWhatsAppDelivery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = whatsappDeliverySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid WhatsApp delivery request',
        details: error.details.map(d => d.message)
      }
    });
  }
  next();
};

export const validateReportId = (req: Request, res: Response, next: NextFunction) => {
  const { error } = reportIdSchema.validate(req.params);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid report ID',
        details: error.details.map(d => d.message)
      }
    });
  }
  next();
};

export const validateIntegratedReport = (req: Request, res: Response, next: NextFunction) => {
  const { error } = integratedReportSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid integrated report request',
        details: error.details.map(d => d.message)
      }
    });
  }
  next();
};
