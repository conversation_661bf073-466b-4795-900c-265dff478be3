{"version": 3, "file": "ScrapingQueue.js", "sourceRoot": "", "sources": ["../../src/queue/ScrapingQueue.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAyB;AACzB,uEAA8F;AAG9F,MAAM,MAAM,GAAG;IACb,IAAI,EAAE,CAAC,OAAe,EAAE,IAAU,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;IAC/G,IAAI,EAAE,CAAC,OAAe,EAAE,IAAU,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;IAChH,KAAK,EAAE,CAAC,OAAe,EAAE,IAAU,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;IACnH,KAAK,EAAE,CAAC,OAAe,EAAE,IAAU,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;CAClH,CAAC;AAoBF,MAAa,aAAa;IAChB,KAAK,CAA+B;IACpC,eAAe,CAAqB;IACpC,aAAa,GAAG,KAAK,CAAC;IAE9B;QAEE,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;YAC3C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,MAAM,CAAC;YAChD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YACpC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC;SAC1C,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,cAAK,CAAC,cAAc,EAAE;YACrC,KAAK,EAAE,WAAW;YAClB,iBAAiB,EAAE;gBACjB,gBAAgB,EAAE,GAAG;gBACrB,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE;oBACP,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,IAAI;iBACZ;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,uCAAkB,EAAE,CAAC;IAClD,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAG3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;YACrD,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBACpC,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;aACpC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACrC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBAClC,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;gBACnC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE;YAC/B,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;aACpC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACpC,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,QAAQ,EAAE,GAAG,QAAQ,GAAG;aACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAGH,WAAW,CAAC,KAAK,IAAI,EAAE;YACrB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,GAA+B;QAC9D,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEpD,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACrC,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,YAAY;YACZ,OAAO,EAAE,GAAG,CAAC,YAAY,GAAG,CAAC;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC;YAEH,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAGvB,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACvB,MAAM,eAAe,GAAoB;gBACvC,YAAY;gBACZ,OAAO;gBACP,OAAO;aACR,CAAC;YAEF,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE9E,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAKvB,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAExB,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACjD,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,YAAY;aACb,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,YAAY;gBACZ,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAAwB;QAClD,MAAM,OAAO,GAAoB;YAC/B,GAAG,OAAO;YACV,KAAK,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACxE,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC;QAEhE,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,EAAE;YAC3D,QAAQ;YACR,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;SACvB,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzC,KAAK,EAAE,GAAG,CAAC,EAAE;YACb,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,WAAW,CAAC,QAAgB;QAClC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YACvB,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAEO,QAAQ;QAEd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IACjD,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,KAAa;QACrC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAE3C,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,OAAO,KAAK,YAAY,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,MAAM,GAAc;YACxB,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YAC5B,MAAM,EAAE,MAAM,GAAG,CAAC,QAAQ,EAAE;YAC5B,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE;YACxB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;SACnC,CAAC;QAEF,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;YACnB,MAAM,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,WAAW,CAAC;QAClC,CAAC;QAED,IAAI,GAAG,CAAC,YAAY,EAAE,CAAC;YACrB,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC;QAClC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,aAAa;QACxB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QAC5C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QAE5C,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,SAAS,CAAC,MAAM;YAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;SACzE,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACxC,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEtC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;CACF;AAjPD,sCAiPC"}