{"version": 3, "file": "SimpleReportGenerator.js", "sourceRoot": "", "sources": ["../../src/services/SimpleReportGenerator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+BAAoC;AACpC,uCAAyB;AACzB,2CAA6B;AAsB7B,MAAa,qBAAqB;IACxB,YAAY,GAAgC,IAAI,GAAG,EAAE,CAAC;IACtD,YAAY,CAAS;IAE7B;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAC3D,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,IAAgB;QACvC,MAAM,QAAQ,GAAG,IAAA,SAAM,GAAE,CAAC;QAE1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,SAAS,CAAC;YACrD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE9D,MAAM,QAAQ,GAAmB;gBAC/B,QAAQ;gBACR,MAAM,EAAE,MAAM;gBACd,QAAQ;gBACR,WAAW;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,WAAW;aACpB,CAAC;YAGF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE1C,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/G,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,IAAgB;QACtC,MAAM,QAAQ,GAAG,IAAA,SAAM,GAAE,CAAC;QAE1B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,QAAQ,GAAmB;gBAC/B,QAAQ;gBACR,MAAM,EAAE,KAAK;gBACb,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,SAAS;gBAC7C,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,4BAA4B;aACrC,CAAC;YAGF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE1C,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,YAAoB,EAAE,IAAgB;QACjE,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,YAAY,OAAO,CAAC,CAAC;YAE1E,IAAI,QAAgB,CAAC;YACrB,IAAI,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBAEN,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACvC,CAAC;YAGD,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE1D,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,YAAY,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5H,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,IAAgB;QAC5D,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;QAE5C,IAAI,QAAQ,GAAG,QAAQ,CAAC;QAGxB,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,EAAE,YAAY,CAAC,YAAY,IAAI,kBAAkB,CAAC,CAAC;YAClG,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,YAAY,CAAC,OAAO,IAAI,sBAAsB,CAAC,CAAC;YACpG,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,YAAY,CAAC,KAAK,IAAI,oBAAoB,CAAC,CAAC;YAC9F,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,YAAY,CAAC,OAAO,IAAI,sBAAsB,CAAC,CAAC;QACtG,CAAC;QAGD,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,EAAE,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC;YAC/F,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC;YACrE,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAGhF,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACrD,MAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBAC/C,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,QAAQ,SAAS,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC;gBACjG,CAAC,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACtE,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAC7D,CAAC;YAGD,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;gBACjC,MAAM,mBAAmB,GAAG,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC3F,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,oBAAoB,CAAC,QAAe;QAC1C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,+BAA+B,CAAC;QACzC,CAAC;QAED,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACtD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;gBAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAC/B,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA2B,CAAC,CAAC;QAEhC,IAAI,IAAI,GAAG,kCAAkC,CAAC;QAE9C,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,IAAI,gCAAgC,CAAC;YACzC,IAAI,IAAI,4BAA4B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC;YACzF,IAAI,IAAI,2BAA2B,CAAC;YAEpC,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,EAAE;gBAC5C,IAAI,IAAI,2BAA2B,CAAC;gBACpC,IAAI,IAAI,WAAW,OAAO,CAAC,KAAK,IAAI,SAAS,aAAa,CAAC;gBAC3D,IAAI,IAAI,GAAG,OAAO,CAAC,WAAW,IAAI,0BAA0B,EAAE,CAAC;gBAC/D,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,IAAI,IAAI,qCAAqC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,SAAS,CAAC;gBAC1F,CAAC;gBACD,IAAI,IAAI,OAAO,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,aAAa,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,QAAQ,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,2BAA2B,CAAC,eAAsB;QACxD,IAAI,CAAC,eAAe,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,OAAO,sCAAsC,CAAC;QAChD,CAAC;QAED,IAAI,IAAI,GAAG,yCAAyC,CAAC;QACrD,IAAI,IAAI,kCAAkC,CAAC;QAE3C,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACrC,IAAI,IAAI,kCAAkC,CAAC;YAC3C,IAAI,IAAI,qCAAqC,CAAC;YAC9C,IAAI,IAAI,OAAO,GAAG,CAAC,KAAK,IAAI,kBAAkB,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC;YACjE,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACjB,IAAI,IAAI,yCAAyC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,SAAS,CAAC;YAC1F,CAAC;YACD,IAAI,IAAI,QAAQ,CAAC;YACjB,IAAI,IAAI,yCAAyC,GAAG,CAAC,WAAW,IAAI,0BAA0B,MAAM,CAAC;YAErG,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,IAAI,IAAI,2BAA2B,CAAC;gBACpC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;oBAEtC,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;wBACvC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,WAAW;4BACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACxC,IAAI,IAAI,OAAO,UAAU,OAAO,CAAC;gBACnC,CAAC,CAAC,CAAC;gBACH,IAAI,IAAI,OAAO,CAAC;YAClB,CAAC;YAED,IAAI,GAAG,CAAC,cAAc,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;gBAC1C,IAAI,IAAI,mCAAmC,CAAC;gBAC5C,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM,UAAU,GAAG,OAAO,GAAG,CAAC,cAAc,KAAK,QAAQ,CAAC,CAAC;wBAC1C,IAAI,GAAG,CAAC,cAAc,CAAC,aAAa,IAAI,CAAC,cAAc,GAAG,CAAC,cAAc,CAAC,SAAS,IAAI,cAAc,EAAE,CAAC,CAAC;wBACzG,GAAG,CAAC,cAAc,CAAC;oBACpC,IAAI,IAAI,mCAAmC,UAAU,SAAS,CAAC;gBACjE,CAAC;gBACD,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;oBACpB,IAAI,IAAI,mCAAmC,GAAG,CAAC,WAAW,SAAS,CAAC;gBACtE,CAAC;gBACD,IAAI,IAAI,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,IAAI,OAAO,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,aAAa,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,kBAAkB;QACxB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA8DH,CAAC;IACP,CAAC;IAKD,eAAe,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAKD,UAAU;QACR,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;CACF;AAzTD,sDAyTC;AAED,kBAAe,qBAAqB,CAAC"}