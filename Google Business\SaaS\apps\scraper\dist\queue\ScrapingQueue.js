"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrapingQueue = void 0;
const bull_1 = __importDefault(require("bull"));
const GMBScrapingService_1 = require("../services/GMBScrapingService");
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
class ScrapingQueue {
    queue;
    scrapingService;
    isInitialized = false;
    constructor() {
        const redisConfig = {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            password: process.env.REDIS_PASSWORD,
            db: parseInt(process.env.REDIS_DB || '0'),
        };
        this.queue = new bull_1.default('gmb-scraping', {
            redis: redisConfig,
            defaultJobOptions: {
                removeOnComplete: 100,
                removeOnFail: 50,
                attempts: 3,
                backoff: {
                    type: 'exponential',
                    delay: 5000,
                },
            },
        });
        this.scrapingService = new GMBScrapingService_1.GMBScrapingService();
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        logger.info('Initializing scraping queue');
        this.queue.process('scrape-business', 1, async (job) => {
            return this.processScrapingJob(job);
        });
        this.queue.on('completed', (job, result) => {
            logger.info('Scraping job completed', {
                jobId: job.id,
                businessName: job.data.businessName
            });
        });
        this.queue.on('failed', (job, error) => {
            logger.error('Scraping job failed', {
                jobId: job.id,
                businessName: job.data.businessName,
                error: error.message
            });
        });
        this.queue.on('stalled', (job) => {
            logger.warn('Scraping job stalled', {
                jobId: job.id,
                businessName: job.data.businessName
            });
        });
        this.queue.on('progress', (job, progress) => {
            logger.debug('Scraping job progress', {
                jobId: job.id,
                progress: `${progress}%`
            });
        });
        setInterval(async () => {
            try {
                await this.queue.clean(24 * 60 * 60 * 1000, 'completed');
                await this.queue.clean(7 * 24 * 60 * 60 * 1000, 'failed');
            }
            catch (error) {
                logger.error('Error cleaning queue', { error });
            }
        }, 60 * 60 * 1000);
        this.isInitialized = true;
        logger.info('Scraping queue initialized successfully');
    }
    async processScrapingJob(job) {
        const { businessName, address, placeId } = job.data;
        logger.info('Processing scraping job', {
            jobId: job.id,
            businessName,
            attempt: job.attemptsMade + 1
        });
        try {
            await job.progress(10);
            await job.progress(30);
            const scrapingRequest = {
                businessName,
                address,
                placeId,
            };
            await job.progress(50);
            const result = await this.scrapingService.scrapeBusinessData(scrapingRequest);
            await job.progress(80);
            await job.progress(100);
            logger.info('Scraping job processed successfully', {
                jobId: job.id,
                businessName
            });
            return result;
        }
        catch (error) {
            logger.error('Error processing scraping job', {
                jobId: job.id,
                businessName,
                error: error.message
            });
            throw error;
        }
    }
    async addScrapingJob(request) {
        const jobData = {
            ...request,
            jobId: `scrape-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date(),
        };
        const priority = this.getPriority(request.priority || 'normal');
        const job = await this.queue.add('scrape-business', jobData, {
            priority,
            delay: this.getDelay(),
        });
        logger.info('Scraping job added to queue', {
            jobId: job.id,
            businessName: request.businessName,
            priority: request.priority
        });
        return job;
    }
    getPriority(priority) {
        switch (priority) {
            case 'high': return 10;
            case 'normal': return 5;
            case 'low': return 1;
            default: return 5;
        }
    }
    getDelay() {
        return Math.floor(Math.random() * 9000) + 1000;
    }
    async getJobStatus(jobId) {
        const job = await this.queue.getJob(jobId);
        if (!job) {
            throw new Error(`Job ${jobId} not found`);
        }
        const status = {
            id: job.id?.toString() || '',
            status: await job.getState(),
            progress: job.progress(),
            data: job.data,
            createdAt: new Date(job.timestamp),
        };
        if (job.processedOn) {
            status.processedAt = new Date(job.processedOn);
        }
        if (job.finishedOn) {
            status.completedAt = new Date(job.finishedOn);
        }
        if (job.returnvalue) {
            status.result = job.returnvalue;
        }
        if (job.failedReason) {
            status.error = job.failedReason;
        }
        return status;
    }
    async getQueueStats() {
        const waiting = await this.queue.getWaiting();
        const active = await this.queue.getActive();
        const completed = await this.queue.getCompleted();
        const failed = await this.queue.getFailed();
        return {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            total: waiting.length + active.length + completed.length + failed.length,
        };
    }
    async pauseQueue() {
        await this.queue.pause();
        logger.info('Scraping queue paused');
    }
    async resumeQueue() {
        await this.queue.resume();
        logger.info('Scraping queue resumed');
    }
    async close() {
        logger.info('Closing scraping queue');
        try {
            await this.queue.close();
            await this.scrapingService.close();
            logger.info('Scraping queue closed successfully');
        }
        catch (error) {
            logger.error('Error closing scraping queue', { error });
        }
    }
}
exports.ScrapingQueue = ScrapingQueue;
//# sourceMappingURL=ScrapingQueue.js.map