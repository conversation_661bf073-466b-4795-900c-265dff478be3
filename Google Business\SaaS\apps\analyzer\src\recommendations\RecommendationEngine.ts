import { AnalysisResult, ScoreBreakdown, Insight, Recommendation } from '../types/analysis';
import winston from 'winston';
import { v4 as uuidv4 } from 'uuid';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [new winston.transports.Console()]
});

export class RecommendationEngine {
  async generateRecommendations(
    analysis: AnalysisResult, 
    scores: ScoreBreakdown, 
    insights: Insight[]
  ): Promise<Recommendation[]> {
    try {
      logger.info('Generating actionable recommendations');

      const recommendations: Recommendation[] = [];

      // Generate recommendations based on score weaknesses
      recommendations.push(...this.generateScoreBasedRecommendations(scores));

      // Generate recommendations from insights
      recommendations.push(...this.generateInsightBasedRecommendations(insights));

      // Generate specific category recommendations
      recommendations.push(...this.generateSEORecommendations(analysis.seoAnalysis, scores.breakdown.seo));
      recommendations.push(...this.generateReviewRecommendations(analysis.sentimentAnalysis, scores.breakdown.reviews));
      recommendations.push(...this.generatePhotoRecommendations(analysis.photoAnalysis, scores.breakdown.photos));

      // Sort by priority and expected impact
      const sortedRecommendations = recommendations
        .sort((a, b) => {
          const aPriority = this.getPriorityWeight(a.priority);
          const bPriority = this.getPriorityWeight(b.priority);
          if (aPriority !== bPriority) return bPriority - aPriority;
          return b.expectedImpact.scoreIncrease - a.expectedImpact.scoreIncrease;
        })
        .slice(0, 15); // Limit to top 15 recommendations

      logger.info(`Generated ${sortedRecommendations.length} recommendations`);
      return sortedRecommendations;

    } catch (error: any) {
      logger.error('Error generating recommendations', { error: error.message });
      return [];
    }
  }

  private generateScoreBasedRecommendations(scores: ScoreBreakdown): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Find the lowest scoring areas
    const sortedAreas = Object.entries(scores.breakdown)
      .sort(([,a], [,b]) => a - b)
      .slice(0, 3);

    for (const [area, score] of sortedAreas) {
      if (score < 70) {
        recommendations.push(this.createAreaRecommendation(area, score));
      }
    }

    return recommendations;
  }

  private generateInsightBasedRecommendations(insights: Insight[]): Recommendation[] {
    const recommendations: Recommendation[] = [];

    // Convert high-impact insights to recommendations
    const highImpactInsights = insights.filter(insight => 
      insight.impact === 'high' && (insight.type === 'weakness' || insight.type === 'opportunity')
    );

    for (const insight of highImpactInsights.slice(0, 5)) {
      recommendations.push({
        id: uuidv4(),
        priority: insight.impact === 'high' ? 'high' : 'medium',
        category: insight.category,
        title: `Address: ${insight.title}`,
        description: insight.description,
        actionItems: insight.recommendations.map(rec => ({
          task: rec,
          effort: 'medium',
          timeline: '1-2 weeks',
          impact: insight.impact === 'high' ? 85 : 65
        })),
        expectedImpact: {
          scoreIncrease: insight.impact === 'high' ? 15 : 8,
          timeframe: '2-4 weeks',
          confidence: insight.confidence
        },
        resources: this.getResourcesForCategory(insight.category)
      });
    }

    return recommendations;
  }

  private generateSEORecommendations(seoAnalysis: any, seoScore: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (seoScore < 80) {
      // Business description optimization
      if (seoAnalysis.factors?.description?.score < 70) {
        recommendations.push({
          id: uuidv4(),
          priority: 'high',
          category: 'seo',
          title: 'Optimize Business Description',
          description: 'Improve your business description with relevant keywords and compelling content.',
          actionItems: [
            { task: 'Research relevant local keywords', effort: 'medium', timeline: '1 week', impact: 80 },
            { task: 'Rewrite description with keywords', effort: 'low', timeline: '2 days', impact: 85 },
            { task: 'Ensure description is 100-750 characters', effort: 'low', timeline: '1 day', impact: 70 }
          ],
          expectedImpact: {
            scoreIncrease: 12,
            timeframe: '2-3 weeks',
            confidence: 90
          },
          resources: [
            {
              type: 'guide',
              title: 'GMB Description Best Practices',
              description: 'Complete guide to writing effective business descriptions'
            },
            {
              type: 'tool',
              title: 'Keyword Research Tool',
              description: 'Find relevant local keywords for your business'
            }
          ]
        });
      }

      // Business categories optimization
      if (seoAnalysis.factors?.categories?.score < 80) {
        recommendations.push({
          id: uuidv4(),
          priority: 'medium',
          category: 'seo',
          title: 'Add Relevant Business Categories',
          description: 'Select the most accurate primary and secondary categories for better search visibility.',
          actionItems: [
            { task: 'Research competitor categories', effort: 'low', timeline: '1 day', impact: 75 },
            { task: 'Select optimal primary category', effort: 'low', timeline: '1 day', impact: 85 },
            { task: 'Add relevant secondary categories', effort: 'low', timeline: '1 day', impact: 70 }
          ],
          expectedImpact: {
            scoreIncrease: 8,
            timeframe: '1-2 weeks',
            confidence: 85
          },
          resources: [
            {
              type: 'guide',
              title: 'GMB Category Selection Guide',
              description: 'How to choose the best categories for your business'
            }
          ]
        });
      }
    }

    return recommendations;
  }

  private generateReviewRecommendations(sentimentAnalysis: any, reviewScore: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (reviewScore < 70) {
      // Review generation strategy
      recommendations.push({
        id: uuidv4(),
        priority: 'high',
        category: 'reviews',
        title: 'Implement Review Generation Strategy',
        description: 'Increase your review volume and improve overall rating through systematic review requests.',
        actionItems: [
          { task: 'Set up automated review request system', effort: 'high', timeline: '1 week', impact: 90 },
          { task: 'Train staff to request reviews', effort: 'medium', timeline: '3 days', impact: 80 },
          { task: 'Create review request templates', effort: 'low', timeline: '2 days', impact: 70 },
          { task: 'Follow up with satisfied customers', effort: 'medium', timeline: 'ongoing', impact: 85 }
        ],
        expectedImpact: {
          scoreIncrease: 20,
          timeframe: '4-8 weeks',
          confidence: 85
        },
        resources: [
          {
            type: 'template',
            title: 'Review Request Email Templates',
            description: 'Professional templates for requesting customer reviews'
          },
          {
            type: 'tool',
            title: 'Review Management Platform',
            description: 'Automate review requests and management'
          }
        ]
      });
    }

    // Review response strategy
    if (sentimentAnalysis.responseAnalysis?.responseRate < 50) {
      recommendations.push({
        id: uuidv4(),
        priority: 'medium',
        category: 'reviews',
        title: 'Improve Review Response Rate',
        description: 'Respond to customer reviews to show engagement and improve reputation.',
        actionItems: [
          { task: 'Respond to all recent negative reviews', effort: 'medium', timeline: '1 week', impact: 85 },
          { task: 'Thank customers for positive reviews', effort: 'low', timeline: '3 days', impact: 70 },
          { task: 'Create response templates', effort: 'low', timeline: '2 days', impact: 65 },
          { task: 'Set up review monitoring alerts', effort: 'medium', timeline: '1 day', impact: 75 }
        ],
        expectedImpact: {
          scoreIncrease: 10,
          timeframe: '2-4 weeks',
          confidence: 80
        },
        resources: [
          {
            type: 'template',
            title: 'Review Response Templates',
            description: 'Professional templates for responding to reviews'
          }
        ]
      });
    }

    return recommendations;
  }

  private generatePhotoRecommendations(photoAnalysis: any, photoScore: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (photoScore < 70) {
      recommendations.push({
        id: uuidv4(),
        priority: 'medium',
        category: 'photos',
        title: 'Enhance Photo Portfolio',
        description: 'Add high-quality photos to improve visual appeal and search rankings.',
        actionItems: [
          { task: 'Take professional exterior photos', effort: 'medium', timeline: '1 week', impact: 80 },
          { task: 'Capture interior and workspace photos', effort: 'medium', timeline: '1 week', impact: 75 },
          { task: 'Add product/service photos', effort: 'medium', timeline: '1 week', impact: 85 },
          { task: 'Include team and staff photos', effort: 'low', timeline: '3 days', impact: 70 }
        ],
        expectedImpact: {
          scoreIncrease: 12,
          timeframe: '2-3 weeks',
          confidence: 85
        },
        resources: [
          {
            type: 'guide',
            title: 'GMB Photo Best Practices',
            description: 'Guidelines for taking effective business photos'
          }
        ]
      });
    }

    return recommendations;
  }

  private createAreaRecommendation(area: string, score: number): Recommendation {
    const areaConfig = {
      reviews: {
        title: 'Boost Review Performance',
        description: 'Improve your review quantity, quality, and response rate.',
        impact: 18
      },
      visibility: {
        title: 'Enhance Local Visibility',
        description: 'Improve your local search rankings and online presence.',
        impact: 15
      },
      seo: {
        title: 'Optimize SEO Factors',
        description: 'Enhance your business information for better search performance.',
        impact: 12
      },
      photos: {
        title: 'Improve Photo Strategy',
        description: 'Add more high-quality photos to showcase your business.',
        impact: 10
      },
      posts: {
        title: 'Increase Google Posts Activity',
        description: 'Regular posting keeps your profile active and engaging.',
        impact: 8
      },
      nap: {
        title: 'Fix NAP Consistency',
        description: 'Ensure your business information is consistent across all platforms.',
        impact: 10
      }
    };

    const config = areaConfig[area as keyof typeof areaConfig] || areaConfig.seo;

    return {
      id: uuidv4(),
      priority: score < 50 ? 'critical' : score < 70 ? 'high' : 'medium',
      category: area as any,
      title: config.title,
      description: config.description,
      actionItems: [
        { task: `Analyze current ${area} performance`, effort: 'low', timeline: '1 day', impact: 60 },
        { task: `Implement ${area} improvements`, effort: 'medium', timeline: '1-2 weeks', impact: 80 },
        { task: `Monitor ${area} progress`, effort: 'low', timeline: 'ongoing', impact: 70 }
      ],
      expectedImpact: {
        scoreIncrease: config.impact,
        timeframe: '3-6 weeks',
        confidence: 80
      },
      resources: this.getResourcesForCategory(area)
    };
  }

  private getResourcesForCategory(category: string): Recommendation['resources'] {
    const resourceMap: Record<string, Recommendation['resources']> = {
      seo: [
        { type: 'guide', title: 'GMB SEO Guide', description: 'Complete SEO optimization guide' },
        { type: 'tool', title: 'SEO Analysis Tool', description: 'Analyze your SEO performance' }
      ],
      reviews: [
        { type: 'template', title: 'Review Templates', description: 'Templates for managing reviews' },
        { type: 'tool', title: 'Review Management', description: 'Automate review processes' }
      ],
      photos: [
        { type: 'guide', title: 'Photo Guidelines', description: 'Best practices for business photos' }
      ],
      competitive: [
        { type: 'tool', title: 'Competitor Analysis', description: 'Track competitor performance' }
      ]
    };

    return resourceMap[category] || [];
  }

  private getPriorityWeight(priority: string): number {
    switch (priority) {
      case 'critical': return 4;
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 1;
    }
  }
}
