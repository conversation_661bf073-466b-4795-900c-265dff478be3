# Test concurrent requests to analyzer service
$jobs = @()

# Start 5 concurrent requests
for ($i = 1; $i -le 5; $i++) {
    $job = Start-Job -ScriptBlock {
        param($testFile)
        $result = Measure-Command { 
            Invoke-RestMethod -Uri "http://localhost:3002/api/analyze/business" -Method POST -ContentType "application/json" -InFile $testFile 
        }
        return @{
            Duration = $result.TotalMilliseconds
            Success = $true
        }
    } -ArgumentList "test-data/mario-restaurant.json"
    $jobs += $job
}

# Wait for all jobs to complete
$results = $jobs | Wait-Job | Receive-Job

# Display results
Write-Host "Concurrent Request Test Results:"
Write-Host "================================"
for ($i = 0; $i -lt $results.Count; $i++) {
    Write-Host "Request $($i+1): $($results[$i].Duration) ms"
}

$avgTime = ($results | Measure-Object -Property Duration -Average).Average
Write-Host "Average Response Time: $avgTime ms"

# Cleanup
$jobs | Remove-Job
