{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAgB1D,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,eAAO,MAAM,YAAY,GACvB,OAAO,QAAQ,EACf,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,SA2HnB,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAGF,qBAAa,aAAc,SAAQ,KAAM,YAAW,QAAQ;IAC1D,UAAU,EAAE,MAAM,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,aAAa,EAAE,OAAO,CAAC;gBAEX,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,IAAI,GAAE,MAAyB;CAQvF;AAED,qBAAa,eAAgB,SAAQ,aAAa;gBACpC,OAAO,GAAE,MAA4B;CAGlD;AAED,qBAAa,aAAc,SAAQ,aAAa;gBAClC,OAAO,GAAE,MAA6B;CAGnD;AAED,qBAAa,iBAAkB,SAAQ,aAAa;gBACtC,OAAO,GAAE,MAAkC;CAGxD;AAED,qBAAa,cAAe,SAAQ,aAAa;gBACnC,OAAO,GAAE,MAAwB;CAG9C;AAED,qBAAa,aAAc,SAAQ,aAAa;gBAClC,OAAO,GAAE,MAA4B;CAGlD;AAED,qBAAa,oBAAqB,SAAQ,aAAa;gBACzC,OAAO,GAAE,MAA4B;CAGlD;AA4BD,eAAO,MAAM,gBAAgB,GAAI,QAAQ,GAAG,SAkB3C,CAAC"}