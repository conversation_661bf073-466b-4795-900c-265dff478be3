"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const joi_1 = __importDefault(require("joi"));
dotenv_1.default.config();
const envSchema = joi_1.default.object({
    NODE_ENV: joi_1.default.string().valid('development', 'production', 'test').default('development'),
    PORT: joi_1.default.number().default(3000),
    DATABASE_URL: joi_1.default.string().required(),
    REDIS_URL: joi_1.default.string().required(),
    GEMINI_API_KEY: joi_1.default.string().required(),
    OPENAI_API_KEY: joi_1.default.string().optional(),
    PERPLEXITY_API_KEY: joi_1.default.string().optional(),
    GOOGLE_MAPS_API_KEY: joi_1.default.string().required(),
    GOOGLE_STATIC_MAPS_API_KEY: joi_1.default.string().optional(),
    SMTP_HOST: joi_1.default.string().default('smtp.gmail.com'),
    SMTP_PORT: joi_1.default.number().default(587),
    SMTP_USER: joi_1.default.string().required(),
    SMTP_PASS: joi_1.default.string().required(),
    FROM_EMAIL: joi_1.default.string().email().required(),
    JWT_SECRET: joi_1.default.string().min(32).required(),
    ENCRYPTION_KEY: joi_1.default.string().length(32).required(),
    API_RATE_LIMIT_WINDOW_MS: joi_1.default.number().default(900000),
    API_RATE_LIMIT_MAX_REQUESTS: joi_1.default.number().default(100),
    API_BASE_URL: joi_1.default.string().uri().default('http://localhost:3000'),
    FRONTEND_URL: joi_1.default.string().uri().default('http://localhost:3001'),
    UPLOAD_DIR: joi_1.default.string().default('./uploads'),
    REPORTS_DIR: joi_1.default.string().default('./reports'),
    MAX_FILE_SIZE_MB: joi_1.default.number().default(10),
    SCRAPING_DELAY_MIN: joi_1.default.number().default(1000),
    SCRAPING_DELAY_MAX: joi_1.default.number().default(3000),
    SCRAPING_TIMEOUT: joi_1.default.number().default(30000),
    MAX_CONCURRENT_SCRAPERS: joi_1.default.number().default(3),
    USE_PROXY: joi_1.default.boolean().default(false),
    LOG_LEVEL: joi_1.default.string().valid('error', 'warn', 'info', 'debug').default('info'),
    SENTRY_DSN: joi_1.default.string().optional(),
    ENABLE_METRICS: joi_1.default.boolean().default(true),
    N8N_WEBHOOK_URL: joi_1.default.string().uri().optional(),
    N8N_API_KEY: joi_1.default.string().optional(),
    ENABLE_SWAGGER: joi_1.default.boolean().default(true),
    ENABLE_CORS: joi_1.default.boolean().default(true),
    CORS_ORIGIN: joi_1.default.string().default('http://localhost:3001'),
}).unknown();
const { error, value: envVars } = envSchema.validate(process.env);
if (error) {
    throw new Error(`Config validation error: ${error.message}`);
}
exports.config = {
    app: {
        env: envVars.NODE_ENV,
        port: envVars.PORT,
        apiBaseUrl: envVars.API_BASE_URL,
        frontendUrl: envVars.FRONTEND_URL,
        enableSwagger: envVars.ENABLE_SWAGGER,
        enableCors: envVars.ENABLE_CORS,
        corsOrigin: envVars.CORS_ORIGIN,
    },
    database: {
        url: envVars.DATABASE_URL,
        maxConnections: 20,
        connectionTimeout: 60000,
    },
    redis: {
        url: envVars.REDIS_URL,
        ttl: 3600,
        maxRetries: 3,
    },
    ai: {
        gemini: {
            apiKey: envVars.GEMINI_API_KEY,
            model: 'gemini-pro',
            maxTokens: 4096,
        },
        openai: {
            apiKey: envVars.OPENAI_API_KEY,
            model: 'gpt-4',
            maxTokens: 4096,
        },
        perplexity: {
            apiKey: envVars.PERPLEXITY_API_KEY,
            model: 'sonar-medium-online',
        },
    },
    google: {
        mapsApiKey: envVars.GOOGLE_MAPS_API_KEY,
        staticMapsApiKey: envVars.GOOGLE_STATIC_MAPS_API_KEY,
    },
    email: {
        host: envVars.SMTP_HOST,
        port: envVars.SMTP_PORT,
        user: envVars.SMTP_USER,
        pass: envVars.SMTP_PASS,
        from: envVars.FROM_EMAIL,
    },
    security: {
        jwtSecret: envVars.JWT_SECRET,
        encryptionKey: envVars.ENCRYPTION_KEY,
        rateLimitWindowMs: envVars.API_RATE_LIMIT_WINDOW_MS,
        rateLimitMaxRequests: envVars.API_RATE_LIMIT_MAX_REQUESTS,
    },
    storage: {
        uploadDir: envVars.UPLOAD_DIR,
        reportsDir: envVars.REPORTS_DIR,
        maxFileSizeMB: envVars.MAX_FILE_SIZE_MB,
    },
    scraping: {
        delayMin: envVars.SCRAPING_DELAY_MIN,
        delayMax: envVars.SCRAPING_DELAY_MAX,
        timeout: envVars.SCRAPING_TIMEOUT,
        maxConcurrent: envVars.MAX_CONCURRENT_SCRAPERS,
        useProxy: envVars.USE_PROXY,
    },
    monitoring: {
        logLevel: envVars.LOG_LEVEL,
        sentryDsn: envVars.SENTRY_DSN,
        enableMetrics: envVars.ENABLE_METRICS,
    },
    n8n: {
        webhookUrl: envVars.N8N_WEBHOOK_URL,
        apiKey: envVars.N8N_API_KEY,
    },
};
//# sourceMappingURL=index.js.map