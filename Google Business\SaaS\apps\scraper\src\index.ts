import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { GMBScrapingService } from './services/GMBScrapingService';
import { ScrapingQueue } from './queue/ScrapingQueue';
import { DataValidationService } from './services/DataValidationService';
import { CompetitorAnalysisService } from './services/CompetitorAnalysisService';
// Simple logger for Phase 2
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

// Load environment variables
dotenv.config();

// Configuration
const config = {
  app: {
    port: process.env.SCRAPER_PORT || 3001,
    corsOrigin: process.env.CORS_ORIGIN || '*',
  },
  security: {
    rateLimitWindowMs: 15 * 60 * 1000, // 15 minutes
    rateLimitMaxRequests: 50, // Lower limit for scraper service
  },
};

// Create Express app
const app = express();
const PORT = config.app.port;

// Middleware
app.use(helmet());
app.use(cors({ origin: config.app.corsOrigin }));
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMaxRequests,
  message: { error: 'Too many scraping requests from this IP, please try again later.' },
});
app.use('/api/', limiter);

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
  });
  next();
});

// Initialize services
const scrapingService = new GMBScrapingService();
const scrapingQueue = new ScrapingQueue();
const validationService = new DataValidationService();
const competitorService = new CompetitorAnalysisService();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      service: 'GMB Scraper',
      environment: process.env.NODE_ENV || 'development',
    },
  });
});

// Scraping endpoints
app.post('/api/scrape/business', async (req, res) => {
  try {
    const { businessName, address, placeId } = req.body;
    
    if (!businessName || !address) {
      return res.status(400).json({
        success: false,
        error: { message: 'Business name and address are required', code: 'MISSING_REQUIRED_FIELDS' },
      });
    }

    // Add to scraping queue
    const job = await scrapingQueue.addScrapingJob({
      businessName,
      address,
      placeId,
      priority: 'normal',
    });

    res.json({
      success: true,
      data: {
        jobId: job.id,
        status: 'queued',
        message: 'Scraping job added to queue',
      },
    });
  } catch (error: any) {
    logger.error('Error adding scraping job', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to add scraping job', code: 'SCRAPING_JOB_ERROR' },
    });
  }
});

app.get('/api/scrape/status/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const jobStatus = await scrapingQueue.getJobStatus(jobId);
    
    res.json({
      success: true,
      data: jobStatus,
    });
  } catch (error: any) {
    logger.error('Error getting job status', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get job status', code: 'JOB_STATUS_ERROR' },
    });
  }
});

// Direct scraping endpoint (for testing)
app.post('/api/scrape/direct', async (req, res) => {
  try {
    const { businessName, address, placeId } = req.body;

    if (!businessName || !address) {
      return res.status(400).json({
        success: false,
        error: { message: 'Business name and address are required', code: 'MISSING_REQUIRED_FIELDS' },
      });
    }

    const result = await scrapingService.scrapeBusinessData({
      businessName,
      address,
      placeId,
    });

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    logger.error('Error in direct scraping', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to scrape business data', code: 'SCRAPING_ERROR' },
    });
  }
});

// Data validation endpoint
app.post('/api/validate', async (req, res) => {
  try {
    const { data } = req.body;

    if (!data) {
      return res.status(400).json({
        success: false,
        error: { message: 'Data to validate is required', code: 'MISSING_DATA' },
      });
    }

    const validationResult = await validationService.validateAndClean(data);
    const qualityMetrics = validationService.calculateDataQuality(data, validationResult);

    res.json({
      success: true,
      data: {
        validation: validationResult,
        quality: qualityMetrics,
      },
    });
  } catch (error: any) {
    logger.error('Error in data validation', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to validate data', code: 'VALIDATION_ERROR' },
    });
  }
});

// Competitor analysis endpoint
app.post('/api/competitors/analyze', async (req, res) => {
  try {
    const { businessName, address, category, radius, maxCompetitors } = req.body;

    if (!businessName || !address || !category) {
      return res.status(400).json({
        success: false,
        error: { message: 'Business name, address, and category are required', code: 'MISSING_REQUIRED_FIELDS' },
      });
    }

    const analysisResult = await competitorService.analyzeCompetitors({
      businessName,
      address,
      category,
      radius,
      maxCompetitors,
    });

    res.json({
      success: true,
      data: analysisResult,
    });
  } catch (error: any) {
    logger.error('Error in competitor analysis', { error: error.message });
    res.status(500).json({
      success: false,
      error: { message: 'Failed to analyze competitors', code: 'COMPETITOR_ANALYSIS_ERROR' },
    });
  }
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'GMB Scraper Service',
    version: '1.0.0',
    status: 'running',
    service: 'scraper',
    endpoints: {
      health: '/health',
      scrape: '/api/scrape/business',
      status: '/api/scrape/status/:jobId',
      direct: '/api/scrape/direct',
      validate: '/api/validate',
      competitors: '/api/competitors/analyze',
    },
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: `Route ${req.originalUrl} not found`,
      code: 'NOT_FOUND',
    },
  });
});

// Error handler
app.use((error: any, req: any, res: any, next: any) => {
  const statusCode = error.statusCode || 500;
  logger.error('Scraper API Error', { message: error.message, statusCode });
  res.status(statusCode).json({
    success: false,
    error: {
      message: error.message || 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
    },
  });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`🕷️  GMB Scraper Service started on port ${PORT}`);
  logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
  logger.info(`🔍 Scraping API: http://localhost:${PORT}/api/scrape/business`);
  logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Initialize scraping queue
scrapingQueue.initialize().then(() => {
  logger.info('✅ Scraping queue initialized');
}).catch((error) => {
  logger.error('❌ Failed to initialize scraping queue', { error });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received. Starting graceful shutdown...');
  await scrapingQueue.close();
  await competitorService.close();
  server.close(() => {
    logger.info('HTTP server closed.');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received. Starting graceful shutdown...');
  await scrapingQueue.close();
  await competitorService.close();
  server.close(() => {
    logger.info('HTTP server closed.');
    process.exit(0);
  });
});

export default app;
