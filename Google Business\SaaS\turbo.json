{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "lint:fix": {"cache": false}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:watch": {"cache": false, "persistent": true}, "type-check": {"dependsOn": ["^type-check"]}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}}}