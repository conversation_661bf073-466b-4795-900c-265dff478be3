"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScoreCalculator = void 0;
const winston_1 = __importDefault(require("winston"));
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.json(),
    transports: [new winston_1.default.transports.Console()]
});
class ScoreCalculator {
    weights = {
        reviews: 0.25,
        visibility: 0.20,
        seo: 0.20,
        photos: 0.15,
        posts: 0.10,
        nap: 0.10
    };
    calculateOverallScore(data) {
        try {
            logger.info('Calculating overall score breakdown');
            const scores = {
                reviews: this.calculateReviewScore(data.reviews || []),
                visibility: this.calculateVisibilityScore(data.rankings || []),
                seo: this.calculateSEOScore(data.seoFactors || {}),
                photos: this.calculatePhotoScore(data.photos || []),
                posts: this.calculatePostsScore(data.posts || []),
                nap: this.calculateNAPScore(data.citations || [])
            };
            const overall = Object.entries(scores)
                .reduce((sum, [key, score]) => sum + (score * this.weights[key]), 0);
            const grade = this.calculateGrade(overall);
            const result = {
                overall: Math.round(overall),
                breakdown: scores,
                weights: this.weights,
                grade
            };
            logger.info('Score calculation completed', { overall: result.overall, grade });
            return result;
        }
        catch (error) {
            logger.error('Error calculating scores', { error: error.message });
            return this.getDefaultScoreBreakdown();
        }
    }
    calculateReviewScore(reviews) {
        if (!reviews || reviews.length === 0)
            return 0;
        const totalReviews = reviews.length;
        const avgRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0) / totalReviews;
        const quantityScore = Math.min(totalReviews * 2, 50);
        const qualityScore = (avgRating / 5) * 50;
        const recentReviews = reviews.filter(review => {
            const reviewDate = new Date(review.date || review.createdAt);
            const threeMonthsAgo = new Date();
            threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
            return reviewDate > threeMonthsAgo;
        }).length;
        const recentBonus = Math.min(recentReviews * 2, 10);
        return Math.min(quantityScore + qualityScore + recentBonus, 100);
    }
    calculateVisibilityScore(rankings) {
        if (!rankings || rankings.length === 0)
            return 0;
        const positions = rankings.map(r => r.position || r.rank || 10).filter(p => p > 0);
        if (positions.length === 0)
            return 0;
        const avgPosition = positions.reduce((sum, pos) => sum + pos, 0) / positions.length;
        const positionScore = Math.max(0, 110 - (avgPosition * 10));
        const localPackPresence = rankings.filter(r => r.type === 'local_pack' || r.position <= 3).length;
        const localPackBonus = Math.min(localPackPresence * 10, 20);
        return Math.min(positionScore + localPackBonus, 100);
    }
    calculateSEOScore(seoFactors) {
        if (!seoFactors || typeof seoFactors !== 'object')
            return 0;
        let score = 0;
        let maxScore = 0;
        if (seoFactors.businessName) {
            maxScore += 20;
            if (seoFactors.businessName.length > 0 && seoFactors.businessName.length <= 50) {
                score += 20;
            }
            else if (seoFactors.businessName.length > 0) {
                score += 10;
            }
        }
        if (seoFactors.description) {
            maxScore += 25;
            const descLength = seoFactors.description.length;
            if (descLength >= 100 && descLength <= 750) {
                score += 25;
            }
            else if (descLength > 0) {
                score += 15;
            }
        }
        if (seoFactors.categories) {
            maxScore += 20;
            const categoryCount = Array.isArray(seoFactors.categories) ? seoFactors.categories.length : 0;
            if (categoryCount >= 1) {
                score += Math.min(categoryCount * 5, 20);
            }
        }
        if (seoFactors.hours) {
            maxScore += 15;
            const hoursSet = Object.keys(seoFactors.hours).length;
            score += Math.min(hoursSet * 2, 15);
        }
        maxScore += 20;
        if (seoFactors.phone)
            score += 10;
        if (seoFactors.website)
            score += 10;
        return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
    }
    calculatePhotoScore(photos) {
        if (!photos || photos.length === 0)
            return 0;
        const totalPhotos = photos.length;
        const recommendedPhotos = 20;
        const quantityScore = Math.min((totalPhotos / recommendedPhotos) * 60, 60);
        const categories = ['exterior', 'interior', 'products', 'team', 'logo'];
        const categoriesPresent = categories.filter(category => photos.some(photo => photo.category === category)).length;
        const diversityScore = (categoriesPresent / categories.length) * 40;
        return Math.round(quantityScore + diversityScore);
    }
    calculatePostsScore(posts) {
        if (!posts || posts.length === 0)
            return 0;
        const totalPosts = posts.length;
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
        const recentPosts = posts.filter(post => {
            const postDate = new Date(post.date || post.createdAt);
            return postDate > threeMonthsAgo;
        }).length;
        const recentScore = Math.min(recentPosts * 10, 70);
        const consistencyBonus = recentPosts >= 4 ? 30 : recentPosts * 7.5;
        return Math.round(recentScore + consistencyBonus);
    }
    calculateNAPScore(citations) {
        if (!citations || citations.length === 0)
            return 50;
        const totalCitations = citations.length;
        const consistentCitations = citations.filter(citation => citation.consistent === true || citation.accuracy >= 0.9).length;
        const consistencyScore = totalCitations > 0 ? (consistentCitations / totalCitations) * 70 : 0;
        const quantityBonus = Math.min(totalCitations * 2, 30);
        return Math.round(consistencyScore + quantityBonus);
    }
    calculateGrade(score) {
        if (score >= 97)
            return 'A+';
        if (score >= 93)
            return 'A';
        if (score >= 90)
            return 'B+';
        if (score >= 87)
            return 'B';
        if (score >= 83)
            return 'C+';
        if (score >= 80)
            return 'C';
        if (score >= 77)
            return 'D+';
        if (score >= 70)
            return 'D';
        return 'F';
    }
    getDefaultScoreBreakdown() {
        return {
            overall: 0,
            breakdown: {
                reviews: 0,
                visibility: 0,
                seo: 0,
                photos: 0,
                posts: 0,
                nap: 0
            },
            weights: this.weights,
            grade: 'F'
        };
    }
    getScoreInterpretation(score) {
        if (score >= 90)
            return 'Excellent - Your GMB profile is highly optimized';
        if (score >= 80)
            return 'Good - Your profile is well-optimized with room for improvement';
        if (score >= 70)
            return 'Fair - Your profile needs significant optimization';
        if (score >= 60)
            return 'Poor - Your profile requires immediate attention';
        return 'Critical - Your profile needs comprehensive optimization';
    }
    getTopImprovementAreas(breakdown) {
        const areas = Object.entries(breakdown.breakdown).map(([area, score]) => ({
            area,
            score,
            weight: this.weights[area],
            impact: (100 - score) * this.weights[area]
        }));
        return areas
            .sort((a, b) => b.impact - a.impact)
            .slice(0, 3);
    }
}
exports.ScoreCalculator = ScoreCalculator;
//# sourceMappingURL=ScoreCalculator.js.map