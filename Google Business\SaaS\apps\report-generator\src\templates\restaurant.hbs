<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurant Performance Report - {{businessData.businessName}}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid {{branding.primaryColor}}; padding-bottom: 20px; }
        .logo { max-height: 60px; margin-bottom: 10px; }
        .business-name { font-size: 2.5em; font-weight: bold; color: {{branding.primaryColor}}; margin-bottom: 10px; }
        .report-title { font-size: 1.5em; color: {{branding.secondaryColor}}; }
        .restaurant-badge { background: #f59e0b; color: white; padding: 8px 16px; border-radius: 20px; font-size: 0.9em; margin: 10px 0; display: inline-block; }
        .section { margin-bottom: 40px; page-break-inside: avoid; }
        .section-title { font-size: 1.8em; color: {{branding.primaryColor}}; margin-bottom: 20px; border-left: 4px solid {{branding.primaryColor}}; padding-left: 15px; }
        .score-container { display: flex; justify-content: center; align-items: center; margin: 30px 0; }
        .score-circle { width: 150px; height: 150px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2.5em; font-weight: bold; background: {{scoreColor analysisData.scores.overall}}; }
        .grade { font-size: 3em; margin-left: 20px; font-weight: bold; color: {{gradeColor analysisData.scores.grade}}; }
        .restaurant-metrics { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
        .metric-card { padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; text-align: center; background: #f8f9fa; }
        .metric-value { font-size: 2em; font-weight: bold; color: {{branding.primaryColor}}; }
        .metric-label { color: {{branding.secondaryColor}}; margin-top: 5px; }
        .breakdown-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0; }
        .breakdown-item { padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; text-align: center; }
        .breakdown-score { font-size: 2em; font-weight: bold; color: {{branding.primaryColor}}; }
        .breakdown-label { color: {{branding.secondaryColor}}; margin-top: 5px; }
        .restaurant-insights { margin: 20px 0; }
        .insight-card { padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid; }
        .insight-strength { background: #f0fdf4; border-color: #22c55e; }
        .insight-weakness { background: #fef2f2; border-color: #ef4444; }
        .insight-opportunity { background: #fffbeb; border-color: #f59e0b; }
        .insight-threat { background: #fdf2f8; border-color: #ec4899; }
        .insight-title { font-weight: bold; margin-bottom: 8px; font-size: 1.1em; }
        .insight-description { color: #6b7280; }
        .restaurant-recommendations { margin: 20px 0; }
        .recommendation { padding: 20px; margin: 15px 0; border: 1px solid #e5e7eb; border-radius: 8px; background: white; }
        .recommendation-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .recommendation-title { font-weight: bold; font-size: 1.1em; color: #1f2937; }
        .priority-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; }
        .priority-critical { background: #fef2f2; color: #dc2626; }
        .priority-high { background: #fff7ed; color: #ea580c; }
        .priority-medium { background: #fffbeb; color: #d97706; }
        .priority-low { background: #f0fdf4; color: #16a34a; }
        .action-items { margin-top: 15px; }
        .action-item { padding: 8px 0; border-bottom: 1px solid #f3f4f6; display: flex; justify-content: space-between; }
        .action-task { flex: 1; }
        .action-meta { color: #6b7280; font-size: 0.9em; }
        .expected-impact { margin-top: 15px; padding: 12px; background: #f0f9ff; border-radius: 6px; border-left: 4px solid #0ea5e9; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; }
        @media print { .page-break { page-break-before: always; } }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            {{#if branding.logo}}<img src="{{branding.logo}}" alt="Logo" class="logo">{{/if}}
            <h1 class="business-name">{{businessData.businessName}}</h1>
            <div class="restaurant-badge">🍽️ Restaurant Performance Report</div>
            <p style="color: #6b7280; margin-top: 10px;">Generated on {{formatDate generatedAt}}</p>
        </div>

        <!-- Restaurant Overview -->
        <div class="section">
            <h2 class="section-title">Restaurant Overview</h2>
            <div class="restaurant-metrics">
                <div class="metric-card">
                    <div class="metric-value">{{businessData.rating}}/5</div>
                    <div class="metric-label">Average Rating</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{{businessData.reviewCount}}</div>
                    <div class="metric-label">Total Reviews</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{{analysisData.scores.overall}}</div>
                    <div class="metric-label">GMB Score</div>
                </div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="section">
            <h2 class="section-title">Performance Summary</h2>
            <div class="score-container">
                <div class="score-circle">{{analysisData.scores.overall}}</div>
                <div class="grade">{{analysisData.scores.grade}}</div>
            </div>
            <p style="text-align: center; font-size: 1.1em; margin: 20px 0;">
                Your restaurant's Google Business Profile scored {{analysisData.scores.overall}} out of 100, earning a grade of {{analysisData.scores.grade}}.
                {{#ifEquals analysisData.scores.grade "A"}}Outstanding performance! Your restaurant is highly optimized for local search.{{/ifEquals}}
                {{#ifEquals analysisData.scores.grade "B"}}Good performance with some opportunities to attract more diners.{{/ifEquals}}
                {{#ifEquals analysisData.scores.grade "C"}}Average performance - several optimization opportunities to increase visibility.{{/ifEquals}}
                {{#ifEquals analysisData.scores.grade "D"}}Below average performance - immediate attention needed to compete effectively.{{/ifEquals}}
                {{#ifEquals analysisData.scores.grade "F"}}Poor performance - comprehensive optimization required to attract customers.{{/ifEquals}}
            </p>
        </div>

        <!-- Performance Breakdown -->
        <div class="section page-break">
            <h2 class="section-title">Detailed Performance Breakdown</h2>
            <div class="breakdown-grid">
                <div class="breakdown-item">
                    <div class="breakdown-score">{{formatNumber analysisData.scores.breakdown.reviews}}</div>
                    <div class="breakdown-label">Customer Reviews (25%)</div>
                    <p style="font-size: 0.9em; color: #6b7280; margin-top: 8px;">Quality and quantity of customer feedback</p>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-score">{{formatNumber analysisData.scores.breakdown.visibility}}</div>
                    <div class="breakdown-label">Local Visibility (20%)</div>
                    <p style="font-size: 0.9em; color: #6b7280; margin-top: 8px;">How easily customers can find your restaurant</p>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-score">{{formatNumber analysisData.scores.breakdown.seo}}</div>
                    <div class="breakdown-label">SEO Optimization (20%)</div>
                    <p style="font-size: 0.9em; color: #6b7280; margin-top: 8px;">Search engine optimization factors</p>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-score">{{formatNumber analysisData.scores.breakdown.photos}}</div>
                    <div class="breakdown-label">Visual Content (15%)</div>
                    <p style="font-size: 0.9em; color: #6b7280; margin-top: 8px;">Food photos and restaurant imagery</p>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-score">{{formatNumber analysisData.scores.breakdown.posts}}</div>
                    <div class="breakdown-label">Social Posts (10%)</div>
                    <p style="font-size: 0.9em; color: #6b7280; margin-top: 8px;">Regular updates and engagement</p>
                </div>
                <div class="breakdown-item">
                    <div class="breakdown-score">{{formatNumber analysisData.scores.breakdown.nap}}</div>
                    <div class="breakdown-label">Business Info (10%)</div>
                    <p style="font-size: 0.9em; color: #6b7280; margin-top: 8px;">Name, address, phone consistency</p>
                </div>
            </div>
        </div>

        <!-- Restaurant-Specific Insights -->
        <div class="section page-break">
            <h2 class="section-title">Restaurant Performance Insights</h2>
            <div class="restaurant-insights">
                {{#each analysisData.insights}}
                <div class="insight-card insight-{{type}}">
                    <div class="insight-title">
                        {{#ifEquals type "strength"}}🌟{{/ifEquals}}
                        {{#ifEquals type "weakness"}}⚠️{{/ifEquals}}
                        {{#ifEquals type "opportunity"}}🚀{{/ifEquals}}
                        {{#ifEquals type "threat"}}🔍{{/ifEquals}}
                        {{title}}
                    </div>
                    <div class="insight-description">{{description}}</div>
                    {{#ifEquals ../businessData.category "restaurant"}}
                    <div style="margin-top: 8px; font-size: 0.9em; color: #059669;">
                        <strong>Restaurant Impact:</strong> 
                        {{#ifEquals type "strength"}}This strength helps attract hungry customers searching for dining options.{{/ifEquals}}
                        {{#ifEquals type "weakness"}}This weakness may cause potential diners to choose competitors instead.{{/ifEquals}}
                        {{#ifEquals type "opportunity"}}This opportunity could significantly increase your restaurant's visibility.{{/ifEquals}}
                        {{#ifEquals type "threat"}}This threat could impact your restaurant's local search ranking.{{/ifEquals}}
                    </div>
                    {{/ifEquals}}
                </div>
                {{/each}}
            </div>
        </div>

        <!-- Restaurant-Focused Recommendations -->
        <div class="section page-break">
            <h2 class="section-title">Priority Action Plan for Your Restaurant</h2>
            <div class="restaurant-recommendations">
                {{#each analysisData.recommendations}}
                <div class="recommendation">
                    <div class="recommendation-header">
                        <div class="recommendation-title">{{title}}</div>
                        <span class="priority-badge priority-{{priority}}">{{priority}} priority</span>
                    </div>
                    <p>{{description}}</p>
                    
                    <div class="action-items">
                        <strong>🎯 Action Steps:</strong>
                        {{#each actionItems}}
                        <div class="action-item">
                            <div class="action-task">{{add @index 1}}. {{task}}</div>
                            <div class="action-meta">{{effort}} effort • {{timeline}}</div>
                        </div>
                        {{/each}}
                    </div>
                    
                    <div class="expected-impact">
                        <strong>📈 Expected Results:</strong> 
                        Implementing this recommendation could increase your GMB score by <strong>+{{expectedImpact.scoreIncrease}} points</strong> 
                        within <strong>{{expectedImpact.timeframe}}</strong>, helping more hungry customers discover your restaurant.
                    </div>
                </div>
                {{/each}}
            </div>
        </div>

        <!-- Restaurant Success Tips -->
        <div class="section">
            <h2 class="section-title">Restaurant Success Tips</h2>
            <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                <h3 style="color: #0c4a6e; margin-bottom: 15px;">🍽️ Maximize Your Restaurant's Local Presence</h3>
                <ul style="color: #374151; line-height: 1.8;">
                    <li><strong>Food Photography:</strong> Upload high-quality photos of your signature dishes, restaurant interior, and happy customers</li>
                    <li><strong>Menu Updates:</strong> Keep your menu current with seasonal items and accurate pricing</li>
                    <li><strong>Customer Engagement:</strong> Respond to all reviews, especially negative ones, with professional and helpful responses</li>
                    <li><strong>Special Events:</strong> Use Google Posts to promote special events, happy hours, and seasonal menus</li>
                    <li><strong>Local Keywords:</strong> Include location-specific terms in your business description (e.g., "best Italian restaurant in downtown")</li>
                    <li><strong>Operating Hours:</strong> Keep hours updated, especially during holidays and special events</li>
                </ul>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Report generated by {{branding.companyName}} on {{formatDate generatedAt}}</p>
            <p>Specialized restaurant performance analysis • For questions about this report, contact your account manager</p>
        </div>
    </div>
</body>
</html>
