"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIAnalyzer = void 0;
const generative_ai_1 = require("@google/generative-ai");
const openai_1 = __importDefault(require("openai"));
const winston_1 = __importDefault(require("winston"));
const axios_1 = __importDefault(require("axios"));
const KeywordRankingService_1 = require("../services/KeywordRankingService");
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.json(),
    transports: [new winston_1.default.transports.Console()]
});
class AIAnalyzer {
    geminiClient;
    openaiClient;
    model;
    perplexityApiKey;
    keywordRankingService;
    constructor() {
        if (process.env.GEMINI_API_KEY) {
            this.geminiClient = new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY);
            this.model = this.geminiClient.getGenerativeModel({ model: 'gemini-pro' });
        }
        if (process.env.OPENAI_API_KEY) {
            this.openaiClient = new openai_1.default({
                apiKey: process.env.OPENAI_API_KEY
            });
        }
        this.perplexityApiKey = process.env.PERPLEXITY_API_KEY || '';
        this.keywordRankingService = new KeywordRankingService_1.KeywordRankingService();
        if (!process.env.GEMINI_API_KEY && !process.env.OPENAI_API_KEY) {
            logger.warn('No AI API keys found. Analysis will be limited.');
        }
        if (!this.perplexityApiKey) {
            logger.warn('Perplexity API key not found. Live verification will be limited.');
        }
    }
    async analyzeBusinessProfile(businessData, competitorData) {
        try {
            logger.info('Starting comprehensive business analysis', {
                businessId: businessData.id,
                hasCompetitors: !!competitorData?.length
            });
            const [seoAnalysis, sentimentAnalysis, competitiveAnalysis, photoAnalysis] = await Promise.allSettled([
                this.analyzeSEOFactors(businessData),
                this.analyzeReviewSentiment(businessData.reviews || []),
                this.analyzeCompetitivePosition(businessData, competitorData || []),
                this.analyzePhotoOptimization(businessData.photos || [])
            ]);
            const results = {
                seoAnalysis: seoAnalysis.status === 'fulfilled' ? seoAnalysis.value : this.getDefaultSEOAnalysis(),
                sentimentAnalysis: sentimentAnalysis.status === 'fulfilled' ? sentimentAnalysis.value : this.getDefaultSentimentAnalysis(),
                competitiveAnalysis: competitiveAnalysis.status === 'fulfilled' ? competitiveAnalysis.value : this.getDefaultCompetitiveAnalysis(),
                photoAnalysis: photoAnalysis.status === 'fulfilled' ? photoAnalysis.value : this.getDefaultPhotoAnalysis(),
                overallHealth: 0,
                timestamp: new Date().toISOString()
            };
            results.overallHealth = this.calculateOverallHealth(results);
            logger.info('Business analysis completed', {
                businessId: businessData.id,
                overallHealth: results.overallHealth
            });
            return results;
        }
        catch (error) {
            logger.error('Error in business profile analysis', { error: error.message });
            throw new Error(`Failed to analyze business profile: ${error.message}`);
        }
    }
    async analyzeSEOFactors(businessData) {
        try {
            const prompt = this.buildSEOPrompt(businessData);
            let analysisText;
            if (this.model) {
                try {
                    const result = await this.model.generateContent(prompt);
                    analysisText = result.response.text();
                }
                catch (geminiError) {
                    logger.warn('Gemini API failed, falling back to OpenAI', { error: geminiError });
                    analysisText = await this.getOpenAIAnalysis(prompt);
                }
            }
            else {
                analysisText = await this.getOpenAIAnalysis(prompt);
            }
            return this.parseSEOResponse(analysisText, businessData);
        }
        catch (error) {
            logger.error('Error in SEO analysis', { error: error.message });
            return this.getDefaultSEOAnalysis();
        }
    }
    async analyzeReviewSentiment(reviews) {
        try {
            if (!reviews || reviews.length === 0) {
                return this.getDefaultSentimentAnalysis();
            }
            const prompt = this.buildSentimentPrompt(reviews);
            let analysisText;
            if (this.model) {
                try {
                    const result = await this.model.generateContent(prompt);
                    analysisText = result.response.text();
                }
                catch (geminiError) {
                    logger.warn('Gemini API failed for sentiment analysis, falling back to OpenAI');
                    analysisText = await this.getOpenAIAnalysis(prompt);
                }
            }
            else {
                analysisText = await this.getOpenAIAnalysis(prompt);
            }
            return this.parseSentimentResponse(analysisText, reviews);
        }
        catch (error) {
            logger.error('Error in sentiment analysis', { error: error.message });
            return this.getDefaultSentimentAnalysis();
        }
    }
    async analyzeCompetitivePosition(businessData, competitors) {
        try {
            if (!competitors || competitors.length === 0) {
                return this.getDefaultCompetitiveAnalysis();
            }
            const prompt = this.buildCompetitivePrompt(businessData, competitors);
            let analysisText;
            if (this.model) {
                try {
                    const result = await this.model.generateContent(prompt);
                    analysisText = result.response.text();
                }
                catch (geminiError) {
                    logger.warn('Gemini API failed for competitive analysis, falling back to OpenAI');
                    analysisText = await this.getOpenAIAnalysis(prompt);
                }
            }
            else {
                analysisText = await this.getOpenAIAnalysis(prompt);
            }
            return this.parseCompetitiveResponse(analysisText, businessData, competitors);
        }
        catch (error) {
            logger.error('Error in competitive analysis', { error: error.message });
            return this.getDefaultCompetitiveAnalysis();
        }
    }
    async analyzePhotoOptimization(photos) {
        try {
            if (!photos || photos.length === 0) {
                return this.getDefaultPhotoAnalysis();
            }
            const prompt = this.buildPhotoPrompt(photos);
            let analysisText;
            if (this.model) {
                try {
                    const result = await this.model.generateContent(prompt);
                    analysisText = result.response.text();
                }
                catch (geminiError) {
                    logger.warn('Gemini API failed for photo analysis, falling back to OpenAI');
                    analysisText = await this.getOpenAIAnalysis(prompt);
                }
            }
            else {
                analysisText = await this.getOpenAIAnalysis(prompt);
            }
            return this.parsePhotoResponse(analysisText, photos);
        }
        catch (error) {
            logger.error('Error in photo analysis', { error: error.message });
            return this.getDefaultPhotoAnalysis();
        }
    }
    async getOpenAIAnalysis(prompt) {
        if (!this.openaiClient) {
            throw new Error('No AI service available');
        }
        const response = await this.openaiClient.chat.completions.create({
            model: 'gpt-4',
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 2000,
            temperature: 0.3
        });
        return response.choices[0]?.message?.content || '';
    }
    buildSEOPrompt(businessData) {
        return `
Analyze the following Google Business Profile data for SEO optimization opportunities:

Business Name: ${businessData.businessName || 'N/A'}
Description: ${businessData.description || 'N/A'}
Categories: ${JSON.stringify(businessData.categories || [])}
Hours: ${JSON.stringify(businessData.hours || {})}
Phone: ${businessData.phone || 'N/A'}
Website: ${businessData.website || 'N/A'}
Address: ${businessData.address || 'N/A'}

Please provide a detailed SEO analysis in JSON format with the following structure:
{
  "score": number (0-100),
  "factors": {
    "businessName": {"score": number, "issues": [], "recommendations": []},
    "description": {"score": number, "keywordDensity": number, "readability": number, "issues": [], "recommendations": []},
    "categories": {"score": number, "primary": string, "secondary": [], "relevance": number, "issues": [], "recommendations": []},
    "hours": {"score": number, "completeness": number, "accuracy": number, "issues": [], "recommendations": []},
    "contact": {"score": number, "napConsistency": number, "phoneFormat": number, "websiteQuality": number, "issues": [], "recommendations": []}
  },
  "keywordAnalysis": {
    "primary": [],
    "secondary": [],
    "missing": [],
    "density": {}
  }
}

Focus on local SEO best practices, keyword optimization, and Google Business Profile guidelines.
`;
    }
    buildSentimentPrompt(reviews) {
        const reviewTexts = reviews.slice(0, 50).map(r => r.text || r.comment || '').join('\n---\n');
        return `
Analyze the sentiment of these Google Business Profile reviews:

${reviewTexts}

Please provide a detailed sentiment analysis in JSON format with the following structure:
{
  "overall": {"score": number (0-100), "sentiment": "positive|neutral|negative", "confidence": number},
  "breakdown": {"positive": number, "neutral": number, "negative": number},
  "trends": {"recent": number, "historical": number, "trajectory": "improving|stable|declining"},
  "themes": {
    "positive": [{"theme": string, "frequency": number, "impact": number}],
    "negative": [{"theme": string, "frequency": number, "impact": number}]
  },
  "responseAnalysis": {"responseRate": number, "averageResponseTime": number, "responseQuality": number, "recommendations": []}
}

Focus on identifying key themes, sentiment trends, and actionable insights for reputation management.
`;
    }
    buildCompetitivePrompt(businessData, competitors) {
        const competitorSummary = competitors.slice(0, 10).map(c => ({
            name: c.businessName || c.name,
            rating: c.rating,
            reviewCount: c.reviewCount,
            categories: c.categories
        }));
        return `
Analyze the competitive position of this business against its competitors:

Target Business:
- Name: ${businessData.businessName}
- Rating: ${businessData.rating}
- Reviews: ${businessData.reviewCount}
- Categories: ${JSON.stringify(businessData.categories || [])}

Competitors:
${JSON.stringify(competitorSummary, null, 2)}

Please provide a competitive analysis in JSON format with the following structure:
{
  "position": {"rank": number, "totalCompetitors": number, "marketShare": number},
  "benchmarks": {
    "rating": {"business": number, "average": number, "percentile": number},
    "reviews": {"business": number, "average": number, "percentile": number},
    "photos": {"business": number, "average": number, "percentile": number},
    "posts": {"business": number, "average": number, "percentile": number}
  },
  "gaps": [{"area": string, "impact": "high|medium|low", "description": string, "recommendation": string}],
  "opportunities": [{"type": string, "potential": number, "effort": "low|medium|high", "description": string, "actionItems": []}]
}

Focus on identifying competitive advantages, gaps, and opportunities for improvement.
`;
    }
    buildPhotoPrompt(photos) {
        const photoSummary = {
            total: photos.length,
            categories: photos.reduce((acc, photo) => {
                const category = photo.category || 'uncategorized';
                acc[category] = (acc[category] || 0) + 1;
                return acc;
            }, {})
        };
        return `
Analyze the photo optimization for this Google Business Profile:

Photo Summary:
- Total Photos: ${photoSummary.total}
- Categories: ${JSON.stringify(photoSummary.categories)}

Please provide a photo analysis in JSON format with the following structure:
{
  "score": number (0-100),
  "quantity": {"total": number, "recommended": number, "gap": number},
  "quality": {"average": number, "issues": [], "recommendations": []},
  "categories": {"exterior": number, "interior": number, "products": number, "team": number, "logo": number, "missing": []},
  "optimization": {"geotagged": number, "highResolution": number, "properNaming": number, "recommendations": []}
}

Focus on Google Business Profile photo best practices and optimization opportunities.
`;
    }
    parseSEOResponse(analysisText, businessData) {
        try {
            const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed;
            }
        }
        catch (error) {
            logger.warn('Failed to parse AI response as JSON, using fallback analysis');
        }
        return this.createFallbackSEOAnalysis(businessData);
    }
    parseSentimentResponse(analysisText, reviews) {
        try {
            const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed;
            }
        }
        catch (error) {
            logger.warn('Failed to parse sentiment analysis, using fallback');
        }
        return this.createFallbackSentimentAnalysis(reviews);
    }
    parseCompetitiveResponse(analysisText, businessData, competitors) {
        try {
            const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed;
            }
        }
        catch (error) {
            logger.warn('Failed to parse competitive analysis, using fallback');
        }
        return this.createFallbackCompetitiveAnalysis(businessData, competitors);
    }
    parsePhotoResponse(analysisText, photos) {
        try {
            const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return parsed;
            }
        }
        catch (error) {
            logger.warn('Failed to parse photo analysis, using fallback');
        }
        return this.createFallbackPhotoAnalysis(photos);
    }
    calculateOverallHealth(results) {
        const weights = {
            seo: 0.3,
            sentiment: 0.25,
            competitive: 0.25,
            photos: 0.2
        };
        return Math.round((results.seoAnalysis.score || 0) * weights.seo +
            (results.sentimentAnalysis.overall?.score || 0) * weights.sentiment +
            (results.competitiveAnalysis.position?.rank ? (100 - results.competitiveAnalysis.position.rank * 10) : 50) * weights.competitive +
            (results.photoAnalysis.score || 0) * weights.photos);
    }
    createFallbackSEOAnalysis(businessData) {
        const hasDescription = !!businessData.description;
        const hasCategories = !!businessData.categories?.length;
        const hasHours = !!businessData.hours;
        const hasContact = !!businessData.phone || !!businessData.website;
        const score = Math.round((hasDescription ? 25 : 0) +
            (hasCategories ? 25 : 0) +
            (hasHours ? 25 : 0) +
            (hasContact ? 25 : 0));
        return {
            score,
            factors: {
                businessName: {
                    score: businessData.businessName ? 85 : 0,
                    issues: businessData.businessName ? [] : ['Business name is missing'],
                    recommendations: businessData.businessName ? [] : ['Add a clear, descriptive business name']
                },
                description: {
                    score: hasDescription ? 75 : 0,
                    keywordDensity: hasDescription ? 0.05 : 0,
                    readability: hasDescription ? 80 : 0,
                    issues: hasDescription ? [] : ['Business description is missing'],
                    recommendations: hasDescription ? ['Optimize description with relevant keywords'] : ['Add a comprehensive business description']
                },
                categories: {
                    score: hasCategories ? 80 : 0,
                    primary: businessData.categories?.[0] || '',
                    secondary: businessData.categories?.slice(1) || [],
                    relevance: hasCategories ? 85 : 0,
                    issues: hasCategories ? [] : ['Business categories not set'],
                    recommendations: hasCategories ? [] : ['Add relevant business categories']
                },
                hours: {
                    score: hasHours ? 90 : 0,
                    completeness: hasHours ? 100 : 0,
                    accuracy: hasHours ? 95 : 0,
                    issues: hasHours ? [] : ['Business hours not specified'],
                    recommendations: hasHours ? [] : ['Ensure business hours are accurate and complete']
                },
                contact: {
                    score: hasContact ? 85 : 0,
                    napConsistency: hasContact ? 90 : 0,
                    phoneFormat: businessData.phone ? 95 : 0,
                    websiteQuality: businessData.website ? 80 : 0,
                    issues: hasContact ? [] : ['Contact information incomplete'],
                    recommendations: hasContact ? [] : ['Verify all contact information is accurate']
                }
            },
            keywordAnalysis: {
                primary: [],
                secondary: [],
                missing: ['location keywords', 'service keywords', 'industry keywords'],
                density: {}
            }
        };
    }
    createFallbackSentimentAnalysis(reviews) {
        if (!reviews || reviews.length === 0) {
            return this.getDefaultSentimentAnalysis();
        }
        const ratings = reviews.map(r => r.rating || 0).filter(r => r > 0);
        const avgRating = ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0;
        const positive = ratings.filter(r => r >= 4).length;
        const neutral = ratings.filter(r => r === 3).length;
        const negative = ratings.filter(r => r <= 2).length;
        const total = ratings.length;
        const score = Math.round((avgRating / 5) * 100);
        const sentiment = score >= 80 ? 'positive' : score >= 60 ? 'neutral' : 'negative';
        return {
            overall: {
                score,
                sentiment,
                confidence: Math.min(total / 10, 1) * 100
            },
            breakdown: {
                positive: total > 0 ? Math.round((positive / total) * 100) : 0,
                neutral: total > 0 ? Math.round((neutral / total) * 100) : 0,
                negative: total > 0 ? Math.round((negative / total) * 100) : 0
            },
            trends: {
                recent: score,
                historical: score,
                trajectory: 'stable'
            },
            themes: {
                positive: [
                    { theme: 'Service Quality', frequency: positive, impact: 85 },
                    { theme: 'Customer Experience', frequency: positive, impact: 80 }
                ],
                negative: [
                    { theme: 'Service Issues', frequency: negative, impact: 90 },
                    { theme: 'Wait Times', frequency: negative, impact: 70 }
                ]
            },
            responseAnalysis: {
                responseRate: 0,
                averageResponseTime: 0,
                responseQuality: 0,
                recommendations: ['Respond to all reviews', 'Address negative feedback promptly']
            }
        };
    }
    createFallbackCompetitiveAnalysis(businessData, competitors) {
        if (!competitors || competitors.length === 0) {
            return this.getDefaultCompetitiveAnalysis();
        }
        const businessRating = businessData.rating || 0;
        const businessReviews = businessData.reviewCount || 0;
        const competitorRatings = competitors.map(c => c.rating || 0).filter(r => r > 0);
        const competitorReviews = competitors.map(c => c.reviewCount || 0);
        const avgRating = competitorRatings.length > 0 ? competitorRatings.reduce((a, b) => a + b, 0) / competitorRatings.length : 0;
        const avgReviews = competitorReviews.length > 0 ? competitorReviews.reduce((a, b) => a + b, 0) / competitorReviews.length : 0;
        const ratingPercentile = competitorRatings.filter(r => r < businessRating).length / competitorRatings.length * 100;
        const reviewPercentile = competitorReviews.filter(r => r < businessReviews).length / competitorReviews.length * 100;
        return {
            position: {
                rank: Math.max(1, Math.round((100 - ratingPercentile) / 10)),
                totalCompetitors: competitors.length,
                marketShare: Math.round(reviewPercentile)
            },
            benchmarks: {
                rating: {
                    business: businessRating,
                    average: Math.round(avgRating * 10) / 10,
                    percentile: Math.round(ratingPercentile)
                },
                reviews: {
                    business: businessReviews,
                    average: Math.round(avgReviews),
                    percentile: Math.round(reviewPercentile)
                },
                photos: {
                    business: businessData.totalPhotos || 0,
                    average: 15,
                    percentile: 50
                },
                posts: {
                    business: businessData.totalPosts || 0,
                    average: 5,
                    percentile: 50
                }
            },
            gaps: [
                {
                    area: 'Review Volume',
                    impact: businessReviews < avgReviews ? 'high' : 'low',
                    description: businessReviews < avgReviews ? 'Below average review count' : 'Good review volume',
                    recommendation: businessReviews < avgReviews ? 'Implement review generation strategy' : 'Maintain current review momentum'
                }
            ],
            opportunities: [
                {
                    type: 'Review Generation',
                    potential: businessReviews < avgReviews ? 85 : 30,
                    effort: 'medium',
                    description: 'Increase review volume to match competitors',
                    actionItems: ['Set up review request system', 'Follow up with customers', 'Incentivize reviews']
                }
            ]
        };
    }
    createFallbackPhotoAnalysis(photos) {
        const total = photos.length;
        const recommended = 20;
        return {
            score: Math.min(Math.round((total / recommended) * 100), 100),
            quantity: {
                total,
                recommended,
                gap: Math.max(0, recommended - total)
            },
            quality: {
                average: 75,
                issues: total < 5 ? ['Insufficient photo quantity'] : [],
                recommendations: total < recommended ? ['Add more high-quality photos'] : ['Maintain photo quality']
            },
            categories: {
                exterior: Math.round(total * 0.3),
                interior: Math.round(total * 0.3),
                products: Math.round(total * 0.2),
                team: Math.round(total * 0.1),
                logo: Math.min(total, 1),
                missing: total < recommended ? ['exterior', 'interior', 'products'] : []
            },
            optimization: {
                geotagged: 0,
                highResolution: Math.round(total * 0.8),
                properNaming: Math.round(total * 0.6),
                recommendations: ['Add geotags to photos', 'Use descriptive file names', 'Ensure high resolution']
            }
        };
    }
    getDefaultSEOAnalysis() {
        return {
            score: 0,
            factors: {
                businessName: { score: 0, issues: ['No data available'], recommendations: [] },
                description: { score: 0, keywordDensity: 0, readability: 0, issues: ['No data available'], recommendations: [] },
                categories: { score: 0, primary: '', secondary: [], relevance: 0, issues: ['No data available'], recommendations: [] },
                hours: { score: 0, completeness: 0, accuracy: 0, issues: ['No data available'], recommendations: [] },
                contact: { score: 0, napConsistency: 0, phoneFormat: 0, websiteQuality: 0, issues: ['No data available'], recommendations: [] }
            },
            keywordAnalysis: { primary: [], secondary: [], missing: [], density: {} }
        };
    }
    getDefaultSentimentAnalysis() {
        return {
            overall: { score: 0, sentiment: 'neutral', confidence: 0 },
            breakdown: { positive: 0, neutral: 0, negative: 0 },
            trends: { recent: 0, historical: 0, trajectory: 'stable' },
            themes: { positive: [], negative: [] },
            responseAnalysis: { responseRate: 0, averageResponseTime: 0, responseQuality: 0, recommendations: [] }
        };
    }
    getDefaultCompetitiveAnalysis() {
        return {
            position: { rank: 0, totalCompetitors: 0, marketShare: 0 },
            benchmarks: {
                rating: { business: 0, average: 0, percentile: 0 },
                reviews: { business: 0, average: 0, percentile: 0 },
                photos: { business: 0, average: 0, percentile: 0 },
                posts: { business: 0, average: 0, percentile: 0 }
            },
            gaps: [],
            opportunities: []
        };
    }
    getDefaultPhotoAnalysis() {
        return {
            score: 0,
            quantity: { total: 0, recommended: 20, gap: 20 },
            quality: { average: 0, issues: ['No photos available'], recommendations: ['Add high-quality photos'] },
            categories: { exterior: 0, interior: 0, products: 0, team: 0, logo: 0, missing: ['all categories'] },
            optimization: { geotagged: 0, highResolution: 0, properNaming: 0, recommendations: ['Add photos to analyze'] }
        };
    }
    async verifyBusinessWithPerplexity(businessData) {
        if (!this.perplexityApiKey) {
            return {
                isVerified: false,
                confidence: 0,
                verifiedData: {},
                issues: ['Perplexity API key not available for live verification']
            };
        }
        try {
            const searchQuery = `Verify the business "${businessData.businessName}" located at "${businessData.address}". Check if this business actually exists, is currently operating, and provide current contact information including phone number, website, and business hours.`;
            const response = await axios_1.default.post('https://api.perplexity.ai/chat/completions', {
                model: 'llama-3.1-sonar-small-128k-online',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a business verification specialist. Provide accurate, real-time business information. If a business does not exist or information cannot be verified, clearly state this. Never fabricate information.'
                    },
                    {
                        role: 'user',
                        content: searchQuery
                    }
                ],
                max_tokens: 1500,
                temperature: 0.1
            }, {
                headers: {
                    'Authorization': `Bearer ${this.perplexityApiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            const content = response.data.choices[0]?.message?.content;
            if (!content) {
                return {
                    isVerified: false,
                    confidence: 0,
                    verifiedData: {},
                    issues: ['No response from verification service']
                };
            }
            return this.parsePerplexityVerification(content, businessData);
        }
        catch (error) {
            logger.error('Perplexity business verification failed', { error: error.message });
            return {
                isVerified: false,
                confidence: 0,
                verifiedData: {},
                issues: [`Verification failed: ${error.message}`]
            };
        }
    }
    parsePerplexityVerification(content, originalData) {
        const lowerContent = content.toLowerCase();
        const issues = [];
        let confidence = 0;
        const verifiedData = { ...originalData };
        const existsIndicators = ['exists', 'found', 'located', 'operating', 'open', 'confirmed'];
        const notExistsIndicators = ['does not exist', 'not found', 'closed permanently', 'no longer operating', 'cannot verify'];
        const existsScore = existsIndicators.reduce((score, indicator) => lowerContent.includes(indicator) ? score + 1 : score, 0);
        const notExistsScore = notExistsIndicators.reduce((score, indicator) => lowerContent.includes(indicator) ? score + 1 : score, 0);
        if (notExistsScore > existsScore) {
            issues.push('Business existence could not be verified through live web search');
            return { isVerified: false, confidence: 0, verifiedData: {}, issues };
        }
        confidence = 0.6;
        const phoneMatch = content.match(/(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/);
        if (phoneMatch) {
            verifiedData.verifiedPhone = phoneMatch[1];
            confidence += 0.15;
        }
        else {
            issues.push('Phone number could not be verified');
        }
        const websiteMatch = content.match(/(https?:\/\/[^\s]+)/);
        if (websiteMatch) {
            verifiedData.verifiedWebsite = websiteMatch[1];
            confidence += 0.15;
        }
        else {
            issues.push('Website could not be verified');
        }
        const hoursMatch = content.match(/hours?:?\s*([^.]+)/i);
        if (hoursMatch) {
            verifiedData.verifiedHours = hoursMatch[1].trim();
            confidence += 0.1;
        }
        return {
            isVerified: confidence >= 0.7,
            confidence: Math.min(confidence, 1.0),
            verifiedData,
            issues
        };
    }
    async analyzeKeywordRankingsWithRealData(businessData) {
        try {
            const rankingRequest = {
                businessName: businessData.businessName,
                website: businessData.website,
                category: businessData.category || 'business',
                location: this.extractLocationFromAddress(businessData.address),
                targetKeywords: businessData.targetKeywords
            };
            return await this.keywordRankingService.analyzeKeywordRankings(rankingRequest);
        }
        catch (error) {
            logger.error('Real keyword ranking analysis failed', { error: error.message });
            return {
                totalKeywords: 0,
                averagePosition: 0,
                topRankingKeywords: [],
                improvementOpportunities: [],
                competitorComparison: { betterThan: 0, worseThan: 0 },
                methodology: `Keyword ranking analysis failed: ${error.message}`
            };
        }
    }
    extractLocationFromAddress(address) {
        if (!address)
            return 'Unknown Location';
        const parts = address.split(',').map(p => p.trim());
        if (parts.length >= 2) {
            return `${parts[parts.length - 2]}, ${parts[parts.length - 1]}`;
        }
        return address;
    }
}
exports.AIAnalyzer = AIAnalyzer;
//# sourceMappingURL=AIAnalyzer.js.map