import dotenv from 'dotenv';
import <PERSON><PERSON> from 'joi';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().default(3000),
  
  // Database
  DATABASE_URL: Joi.string().required(),
  REDIS_URL: Joi.string().required(),
  
  // AI Services
  GEMINI_API_KEY: Joi.string().required(),
  OPENAI_API_KEY: Joi.string().optional(),
  PERPLEXITY_API_KEY: Joi.string().optional(),
  
  // Google Services
  GOOGLE_MAPS_API_KEY: Joi.string().required(),
  GOOGLE_STATIC_MAPS_API_KEY: Joi.string().optional(),
  
  // Email
  SMTP_HOST: Joi.string().default('smtp.gmail.com'),
  SMTP_PORT: Joi.number().default(587),
  SMTP_USER: Joi.string().required(),
  SMTP_PASS: Joi.string().required(),
  FROM_EMAIL: Joi.string().email().required(),
  
  // Security
  JWT_SECRET: Joi.string().min(32).required(),
  ENCRYPTION_KEY: Joi.string().length(32).required(),
  API_RATE_LIMIT_WINDOW_MS: Joi.number().default(900000),
  API_RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  
  // Application
  API_BASE_URL: Joi.string().uri().default('http://localhost:3000'),
  FRONTEND_URL: Joi.string().uri().default('http://localhost:3001'),
  
  // File Storage
  UPLOAD_DIR: Joi.string().default('./uploads'),
  REPORTS_DIR: Joi.string().default('./reports'),
  MAX_FILE_SIZE_MB: Joi.number().default(10),
  
  // Scraping
  SCRAPING_DELAY_MIN: Joi.number().default(1000),
  SCRAPING_DELAY_MAX: Joi.number().default(3000),
  SCRAPING_TIMEOUT: Joi.number().default(30000),
  MAX_CONCURRENT_SCRAPERS: Joi.number().default(3),
  USE_PROXY: Joi.boolean().default(false),
  
  // Monitoring
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
  SENTRY_DSN: Joi.string().optional(),
  ENABLE_METRICS: Joi.boolean().default(true),
  
  // n8n
  N8N_WEBHOOK_URL: Joi.string().uri().optional(),
  N8N_API_KEY: Joi.string().optional(),
  
  // Development
  ENABLE_SWAGGER: Joi.boolean().default(true),
  ENABLE_CORS: Joi.boolean().default(true),
  CORS_ORIGIN: Joi.string().default('http://localhost:3001'),
}).unknown();

const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

export const config = {
  app: {
    env: envVars.NODE_ENV,
    port: envVars.PORT,
    apiBaseUrl: envVars.API_BASE_URL,
    frontendUrl: envVars.FRONTEND_URL,
    enableSwagger: envVars.ENABLE_SWAGGER,
    enableCors: envVars.ENABLE_CORS,
    corsOrigin: envVars.CORS_ORIGIN,
  },
  
  database: {
    url: envVars.DATABASE_URL,
    maxConnections: 20,
    connectionTimeout: 60000,
  },
  
  redis: {
    url: envVars.REDIS_URL,
    ttl: 3600,
    maxRetries: 3,
  },
  
  ai: {
    gemini: {
      apiKey: envVars.GEMINI_API_KEY,
      model: 'gemini-pro',
      maxTokens: 4096,
    },
    openai: {
      apiKey: envVars.OPENAI_API_KEY,
      model: 'gpt-4',
      maxTokens: 4096,
    },
    perplexity: {
      apiKey: envVars.PERPLEXITY_API_KEY,
      model: 'sonar-medium-online',
    },
  },
  
  google: {
    mapsApiKey: envVars.GOOGLE_MAPS_API_KEY,
    staticMapsApiKey: envVars.GOOGLE_STATIC_MAPS_API_KEY,
  },
  
  email: {
    host: envVars.SMTP_HOST,
    port: envVars.SMTP_PORT,
    user: envVars.SMTP_USER,
    pass: envVars.SMTP_PASS,
    from: envVars.FROM_EMAIL,
  },
  
  security: {
    jwtSecret: envVars.JWT_SECRET,
    encryptionKey: envVars.ENCRYPTION_KEY,
    rateLimitWindowMs: envVars.API_RATE_LIMIT_WINDOW_MS,
    rateLimitMaxRequests: envVars.API_RATE_LIMIT_MAX_REQUESTS,
  },
  
  storage: {
    uploadDir: envVars.UPLOAD_DIR,
    reportsDir: envVars.REPORTS_DIR,
    maxFileSizeMB: envVars.MAX_FILE_SIZE_MB,
  },
  
  scraping: {
    delayMin: envVars.SCRAPING_DELAY_MIN,
    delayMax: envVars.SCRAPING_DELAY_MAX,
    timeout: envVars.SCRAPING_TIMEOUT,
    maxConcurrent: envVars.MAX_CONCURRENT_SCRAPERS,
    useProxy: envVars.USE_PROXY,
  },
  
  monitoring: {
    logLevel: envVars.LOG_LEVEL,
    sentryDsn: envVars.SENTRY_DSN,
    enableMetrics: envVars.ENABLE_METRICS,
  },
  
  n8n: {
    webhookUrl: envVars.N8N_WEBHOOK_URL,
    apiKey: envVars.N8N_API_KEY,
  },
} as const;

export type Config = typeof config;
