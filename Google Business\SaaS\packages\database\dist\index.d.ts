import { PrismaClient } from '@prisma/client';
declare class ExtendedPrismaClient extends PrismaClient {
    constructor();
    getBusinessInsights(businessId: string): Promise<any>;
    findNearbyCompetitors(latitude: number, longitude: number, radiusMiles?: number, limit?: number): Promise<any>;
    getBusinessAnalytics(businessId: string, days?: number): Promise<{
        rating: import("@prisma/client/runtime/library").Decimal | null;
        reviewCount: number | null;
        totalPhotos: number | null;
        totalPosts: number | null;
        scrapedAt: Date;
    }[]>;
    getCompetitorRankings(businessId: string): Promise<{
        id: string;
        rating: import("@prisma/client/runtime/library").Decimal | null;
        reviewCount: number | null;
        name: string;
        distance: import("@prisma/client/runtime/library").Decimal | null;
        localRanking: number | null;
        mapRanking: number | null;
    }[]>;
    healthCheck(): Promise<{
        status: string;
        timestamp: Date;
        error?: undefined;
    } | {
        status: string;
        error: any;
        timestamp: Date;
    }>;
    logApiUsage(data: {
        userId?: string;
        endpoint: string;
        method: string;
        statusCode: number;
        responseTime: number;
        userAgent?: string;
        ipAddress?: string;
    }): Promise<{
        id: string;
        userId: string | null;
        endpoint: string;
        method: string;
        statusCode: number;
        responseTime: number;
        userAgent: string | null;
        ipAddress: string | null;
        timestamp: Date;
    }>;
    logSystemMetric(metricType: string, metricName: string, value: number, unit?: string, tags?: any): Promise<{
        id: string;
        timestamp: Date;
        metricType: string;
        metricName: string;
        value: import("@prisma/client/runtime/library").Decimal;
        unit: string | null;
        tags: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
}
declare const prisma: ExtendedPrismaClient;
export { prisma };
export default prisma;
//# sourceMappingURL=index.d.ts.map