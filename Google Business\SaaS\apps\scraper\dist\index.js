"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const GMBScrapingService_1 = require("./services/GMBScrapingService");
const ScrapingQueue_1 = require("./queue/ScrapingQueue");
const DataValidationService_1 = require("./services/DataValidationService");
const CompetitorAnalysisService_1 = require("./services/CompetitorAnalysisService");
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
dotenv_1.default.config();
const config = {
    app: {
        port: process.env.SCRAPER_PORT || 3001,
        corsOrigin: process.env.CORS_ORIGIN || '*',
    },
    security: {
        rateLimitWindowMs: 15 * 60 * 1000,
        rateLimitMaxRequests: 50,
    },
};
const app = (0, express_1.default)();
const PORT = config.app.port;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({ origin: config.app.corsOrigin }));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: { error: 'Too many scraping requests from this IP, please try again later.' },
});
app.use('/api/', limiter);
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        logger.info(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    });
    next();
});
const scrapingService = new GMBScrapingService_1.GMBScrapingService();
const scrapingQueue = new ScrapingQueue_1.ScrapingQueue();
const validationService = new DataValidationService_1.DataValidationService();
const competitorService = new CompetitorAnalysisService_1.CompetitorAnalysisService();
app.get('/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            service: 'GMB Scraper',
            environment: process.env.NODE_ENV || 'development',
        },
    });
});
app.post('/api/scrape/business', async (req, res) => {
    try {
        const { businessName, address, placeId } = req.body;
        if (!businessName || !address) {
            return res.status(400).json({
                success: false,
                error: { message: 'Business name and address are required', code: 'MISSING_REQUIRED_FIELDS' },
            });
        }
        const job = await scrapingQueue.addScrapingJob({
            businessName,
            address,
            placeId,
            priority: 'normal',
        });
        res.json({
            success: true,
            data: {
                jobId: job.id,
                status: 'queued',
                message: 'Scraping job added to queue',
            },
        });
    }
    catch (error) {
        logger.error('Error adding scraping job', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to add scraping job', code: 'SCRAPING_JOB_ERROR' },
        });
    }
});
app.get('/api/scrape/status/:jobId', async (req, res) => {
    try {
        const { jobId } = req.params;
        const jobStatus = await scrapingQueue.getJobStatus(jobId);
        res.json({
            success: true,
            data: jobStatus,
        });
    }
    catch (error) {
        logger.error('Error getting job status', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to get job status', code: 'JOB_STATUS_ERROR' },
        });
    }
});
app.post('/api/scrape/direct', async (req, res) => {
    try {
        const { businessName, address, placeId } = req.body;
        if (!businessName || !address) {
            return res.status(400).json({
                success: false,
                error: { message: 'Business name and address are required', code: 'MISSING_REQUIRED_FIELDS' },
            });
        }
        const result = await scrapingService.scrapeBusinessData({
            businessName,
            address,
            placeId,
        });
        res.json({
            success: true,
            data: result,
        });
    }
    catch (error) {
        logger.error('Error in direct scraping', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to scrape business data', code: 'SCRAPING_ERROR' },
        });
    }
});
app.post('/api/validate', async (req, res) => {
    try {
        const { data } = req.body;
        if (!data) {
            return res.status(400).json({
                success: false,
                error: { message: 'Data to validate is required', code: 'MISSING_DATA' },
            });
        }
        const validationResult = await validationService.validateAndClean(data);
        const qualityMetrics = validationService.calculateDataQuality(data, validationResult);
        res.json({
            success: true,
            data: {
                validation: validationResult,
                quality: qualityMetrics,
            },
        });
    }
    catch (error) {
        logger.error('Error in data validation', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to validate data', code: 'VALIDATION_ERROR' },
        });
    }
});
app.post('/api/competitors/analyze', async (req, res) => {
    try {
        const { businessName, address, category, radius, maxCompetitors } = req.body;
        if (!businessName || !address || !category) {
            return res.status(400).json({
                success: false,
                error: { message: 'Business name, address, and category are required', code: 'MISSING_REQUIRED_FIELDS' },
            });
        }
        const analysisResult = await competitorService.analyzeCompetitors({
            businessName,
            address,
            category,
            radius,
            maxCompetitors,
        });
        res.json({
            success: true,
            data: analysisResult,
        });
    }
    catch (error) {
        logger.error('Error in competitor analysis', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to analyze competitors', code: 'COMPETITOR_ANALYSIS_ERROR' },
        });
    }
});
app.get('/', (req, res) => {
    res.json({
        message: 'GMB Scraper Service',
        version: '1.0.0',
        status: 'running',
        service: 'scraper',
        endpoints: {
            health: '/health',
            scrape: '/api/scrape/business',
            status: '/api/scrape/status/:jobId',
            direct: '/api/scrape/direct',
            validate: '/api/validate',
            competitors: '/api/competitors/analyze',
        },
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            message: `Route ${req.originalUrl} not found`,
            code: 'NOT_FOUND',
        },
    });
});
app.use((error, req, res, next) => {
    const statusCode = error.statusCode || 500;
    logger.error('Scraper API Error', { message: error.message, statusCode });
    res.status(statusCode).json({
        success: false,
        error: {
            message: error.message || 'Internal server error',
            code: error.code || 'INTERNAL_ERROR',
        },
    });
});
const server = app.listen(PORT, () => {
    logger.info(`🕷️  GMB Scraper Service started on port ${PORT}`);
    logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
    logger.info(`🔍 Scraping API: http://localhost:${PORT}/api/scrape/business`);
    logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
scrapingQueue.initialize().then(() => {
    logger.info('✅ Scraping queue initialized');
}).catch((error) => {
    logger.error('❌ Failed to initialize scraping queue', { error });
});
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received. Starting graceful shutdown...');
    await scrapingQueue.close();
    await competitorService.close();
    server.close(() => {
        logger.info('HTTP server closed.');
        process.exit(0);
    });
});
process.on('SIGINT', async () => {
    logger.info('SIGINT received. Starting graceful shutdown...');
    await scrapingQueue.close();
    await competitorService.close();
    server.close(() => {
        logger.info('HTTP server closed.');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=index.js.map