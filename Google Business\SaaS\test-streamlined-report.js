const { WorldClassReportGenerator } = require('./apps/report-generator/dist/services/WorldClassReportGenerator');
const fs = require('fs');
const path = require('path');

console.log('🏆 Testing Streamlined Professional GMB Audit Report');

// Enhanced sample data for different business types
const testBusinesses = [
  {
    name: 'Raga Dental Implants',
    data: {
      businessData: {
        businessName: "Raga Dental Implants and Laser",
        address: "123 Main Street, Thanjavur, Tamil Nadu 613001",
        phone: "+91-4362-123456",
        website: "https://ragadental.com",
        category: "Dental Clinic",
        description: "Premier dental care specializing in implants and laser dentistry in Thanjavur",
        
        reviews: [
          {
            author: "<PERSON><PERSON>",
            rating: 5,
            text: "Excellent service! Dr. <PERSON> and his team provided outstanding dental care. The laser treatment was completely painless.",
            date: "2024-01-15",
            response: {
              text: "Thank you for your kind words! We're delighted you had a comfortable experience.",
              date: "2024-01-16"
            }
          },
          {
            author: "<PERSON>",
            rating: 3,
            text: "Good dental clinic with modern equipment. The waiting time was a bit long but the treatment was satisfactory.",
            date: "2024-01-10"
          },
          {
            author: "Anonymous",
            rating: 2,
            text: "The appointment was delayed by 45 minutes and the staff seemed rushed. Treatment quality was okay but service needs improvement.",
            date: "2024-01-05"
          }
        ],
        
        photos: [
          { url: "photo1.jpg", category: "exterior", alt: "Clinic exterior" },
          { url: "photo2.jpg", category: "interior", alt: "Reception area" },
          { url: "photo3.jpg", category: "team", alt: "Dr. Raga and team" },
          { url: "photo4.jpg", category: "product", alt: "Dental equipment" }
        ]
      },
      
      analysisData: {
        scores: {
          overall: 72,
          grade: 'B',
          breakdown: {
            reviews: 75,
            visibility: 68,
            seo: 70,
            photos: 80,
            posts: 65,
            nap: 85
          }
        },
        insights: [
          {
            category: 'reviews',
            title: 'Strong Review Performance',
            description: 'Your business has a good review rating with active customer engagement.',
            impact: 'high',
            priority: 'maintain'
          },
          {
            category: 'seo',
            title: 'SEO Optimization Needed',
            description: 'Business description lacks location-specific keywords for better local search visibility.',
            impact: 'medium',
            priority: 'improve'
          },
          {
            category: 'photos',
            title: 'Photo Quality Excellence',
            description: 'High-quality photos are driving customer engagement and trust.',
            impact: 'high',
            priority: 'maintain'
          }
        ],
        analysis: {
          seo: {
            title: { score: 70, issues: ["Missing location keywords"], recommendations: ["Add city name to title"] },
            description: { score: 65, issues: ["Generic description"], recommendations: ["Include service keywords"] }
          },
          visibility: {
            localPackPresence: true,
            mapRanking: 4,
            searchRanking: 7
          },
          reviews: {
            averageRating: 4.2,
            totalReviews: 47,
            recentReviews: 8,
            responseRate: 85
          }
        }
      }
    }
  },
  {
    name: 'Spice Garden Restaurant',
    data: {
      businessData: {
        businessName: "Spice Garden Indian Restaurant",
        address: "456 Food Street, Austin, TX 78701",
        phone: "******-555-0123",
        website: "https://spicegarden.com",
        category: "Restaurant",
        description: "Authentic Indian cuisine with traditional recipes and modern presentation",
        
        reviews: [
          {
            author: "Sarah Johnson",
            rating: 5,
            text: "Amazing food! The butter chicken was incredible and the service was excellent. Highly recommend!",
            date: "2024-01-20"
          },
          {
            author: "Mike Chen",
            rating: 4,
            text: "Great flavors and good portion sizes. The ambiance is nice for a date night.",
            date: "2024-01-18"
          }
        ],
        
        photos: [
          { url: "rest1.jpg", category: "interior", alt: "Dining area" },
          { url: "rest2.jpg", category: "product", alt: "Signature dishes" },
          { url: "rest3.jpg", category: "exterior", alt: "Restaurant front" }
        ]
      },
      
      analysisData: {
        scores: {
          overall: 78,
          grade: 'B+',
          breakdown: {
            reviews: 85,
            visibility: 72,
            seo: 75,
            photos: 70,
            posts: 80,
            nap: 90
          }
        },
        insights: [
          {
            category: 'reviews',
            title: 'Excellent Customer Satisfaction',
            description: 'Outstanding review ratings and high response rate demonstrate excellent customer service.',
            impact: 'high',
            priority: 'maintain'
          },
          {
            category: 'visibility',
            title: 'Strong Local Presence',
            description: 'High visibility in local search results with good map ranking performance.',
            impact: 'high',
            priority: 'maintain'
          },
          {
            category: 'photos',
            title: 'Photo Portfolio Enhancement',
            description: 'Photo collection could benefit from more diverse category coverage.',
            impact: 'medium',
            priority: 'improve'
          }
        ],
        analysis: {
          seo: {
            title: { score: 75, issues: ["Could include cuisine type"], recommendations: ["Add 'Indian Restaurant' to title"] },
            description: { score: 70, issues: ["Missing local keywords"], recommendations: ["Include Austin, TX in description"] }
          },
          visibility: {
            localPackPresence: true,
            mapRanking: 2,
            searchRanking: 3
          },
          reviews: {
            averageRating: 4.6,
            totalReviews: 124,
            recentReviews: 15,
            responseRate: 92
          }
        }
      }
    }
  }
];

async function testStreamlinedReport() {
  console.log('\n🎨 Generating Streamlined Professional Reports...\n');
  
  const generator = new WorldClassReportGenerator();
  
  for (const business of testBusinesses) {
    try {
      console.log(`📊 Generating report for ${business.name}...`);
      
      const result = await generator.generateWorldClassReport(business.data);
      
      if (result.success) {
        // Save the report
        const filename = `${business.name.toLowerCase().replace(/\s+/g, '-')}-streamlined-${Date.now()}.html`;
        const filepath = path.join(__dirname, 'reports', filename);
        
        fs.writeFileSync(filepath, result.htmlContent);
        
        console.log(`✅ ${business.name} Report Generated Successfully!`);
        console.log(`   📊 Overall Score: ${business.data.analysisData.scores.overall}/100 (Grade: ${business.data.analysisData.scores.grade})`);
        console.log(`   ⏱️ Generation Time: ${result.generationTime}ms`);
        console.log(`   📄 Report Size: ${result.size.toLocaleString()} bytes`);
        console.log(`   💾 Saved to: ${filepath}`);
        
        // Verify sections
        const sectionsCheck = {
          'Cover Page': result.htmlContent.includes('cover-page'),
          'Executive Summary': result.htmlContent.includes('Executive Summary'),
          'Performance Scorecard': result.htmlContent.includes('Performance Scorecard'),
          'Keyword Ranking': result.htmlContent.includes('🔍 Keyword Ranking Analysis'),
          'Local Visibility Map': result.htmlContent.includes('🗺️ Local Visibility Map'),
          'Photo Audit': result.htmlContent.includes('📸 Photo Audit'),
          'Review Sentiment': result.htmlContent.includes('💬 Review Sentiment Summary'),
          'SEO Description': result.htmlContent.includes('📝 SEO-Optimized Business Description'),
          'NAP Consistency': result.htmlContent.includes('📋 NAP Consistency Table')
        };
        
        console.log('\n   🏆 STREAMLINED SECTIONS VERIFICATION:');
        Object.entries(sectionsCheck).forEach(([section, present]) => {
          console.log(`   ${present ? '✅' : '❌'} ${section}`);
        });
        
        const allSectionsPresent = Object.values(sectionsCheck).every(Boolean);
        console.log(`\n   🎯 IMPLEMENTATION STATUS: ${allSectionsPresent ? '✅ COMPLETE' : '⚠️ PARTIAL'}`);
        
      } else {
        console.log(`❌ Failed to generate report for ${business.name}`);
      }
      
      console.log('\n' + '='.repeat(80) + '\n');
      
    } catch (error) {
      console.error(`❌ Error generating report for ${business.name}:`, error.message);
    }
  }
  
  console.log('🚀 STREAMLINED PROFESSIONAL GMB AUDIT REPORTS COMPLETED!');
  console.log('📋 Features implemented:');
  console.log('   • Clean, performance-focused structure');
  console.log('   • Google Maps Static API integration');
  console.log('   • Dynamic business category adaptation');
  console.log('   • Professional visual design');
  console.log('   • Lean content under 10 pages');
  console.log('   • Export-ready HTML/PDF format');
}

testStreamlinedReport();
