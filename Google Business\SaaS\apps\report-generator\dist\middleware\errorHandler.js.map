{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,qCAAuC;AAGvC,MAAM,MAAM,GAAG,IAAA,sBAAY,EAAC;IAC1B,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CACvC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,EACrC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CACjC;IACD,UAAU,EAAE;QACV,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC7C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC;KACzE;CACF,CAAC,CAAC;AAQI,MAAM,YAAY,GAAG,CAC1B,KAAe,EACf,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IAEF,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE;QAC5B,KAAK,EAAE,KAAK,CAAC,OAAO;QACpB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;IAGH,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IACzC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IACvD,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,gBAAgB,CAAC;IAG1C,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,kBAAkB,CAAC;QAC1B,OAAO,GAAG,sBAAsB,CAAC;IACnC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,cAAc,CAAC;QACtB,OAAO,GAAG,yBAAyB,CAAC;IACtC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;QAC3C,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,WAAW,CAAC;QACnB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QAC1C,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,WAAW,CAAC;QACnB,OAAO,GAAG,oBAAoB,CAAC;IACjC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QAC1C,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,UAAU,CAAC;QAClB,OAAO,GAAG,mBAAmB,CAAC;IAChC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAsB,EAAE,CAAC;QACjD,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,qBAAqB,CAAC;QAC7B,OAAO,GAAG,mBAAmB,CAAC;IAChC,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QAClE,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,gBAAgB,CAAC;QACxB,OAAO,GAAG,2BAA2B,CAAC;IACxC,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,eAAe,CAAC;QACvB,OAAO,GAAG,8BAA8B,CAAC;IAC3C,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,eAAe,CAAC;QACvB,OAAO,GAAG,8BAA8B,CAAC;IAC3C,CAAC;IAGD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7E,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,sBAAsB,CAAC;QAC9B,OAAO,GAAG,+BAA+B,CAAC;IAC5C,CAAC;IAGD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxE,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,wBAAwB,CAAC;QAChC,OAAO,GAAG,0BAA0B,CAAC;IACvC,CAAC;IAGD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QAC3E,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,sBAAsB,CAAC;QAC9B,OAAO,GAAG,sBAAsB,CAAC;IACnC,CAAC;IAGD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3E,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,yBAAyB,CAAC;QACjC,OAAO,GAAG,iCAAiC,CAAC;IAC9C,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC5B,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,gBAAgB,CAAC;QACxB,OAAO,GAAG,gBAAgB,CAAC;IAC7B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACnC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,oBAAoB,CAAC;QAC5B,OAAO,GAAG,oBAAoB,CAAC;IACjC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC9D,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,gBAAgB,CAAC;QACxB,OAAO,GAAG,qBAAqB,CAAC;IAClC,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAChE,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;IAGD,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,OAAO;YACP,IAAI;YACJ,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI;gBAC5C,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,OAAO,EAAE,KAAK;aACf,CAAC;SACH;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AA/HW,QAAA,YAAY,gBA+HvB;AAGK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAGF,MAAa,aAAc,SAAQ,KAAK;IACtC,UAAU,CAAS;IACnB,IAAI,CAAS;IACb,aAAa,CAAU;IAEvB,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,OAAe,gBAAgB;QACpF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAbD,sCAaC;AAED,MAAa,eAAgB,SAAQ,aAAa;IAChD,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IAC1C,CAAC;CACF;AAJD,0CAIC;AAED,MAAa,aAAc,SAAQ,aAAa;IAC9C,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IACnC,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,iBAAkB,SAAQ,aAAa;IAClD,YAAY,UAAkB,yBAAyB;QACrD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACtC,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,cAAe,SAAQ,aAAa;IAC/C,YAAY,UAAkB,eAAe;QAC3C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IACnC,CAAC;CACF;AAJD,wCAIC;AAED,MAAa,aAAc,SAAQ,aAAa;IAC9C,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;IAClC,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,oBAAqB,SAAQ,aAAa;IACrD,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,oDAIC;AAGD,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAW,EAAE,OAAqB,EAAE,EAAE;IACtE,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;QAC1C,MAAM,EAAE,MAAM,EAAE,OAAO,IAAI,MAAM;QACjC,KAAK,EAAE,MAAM,EAAE,KAAK;QACpB,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;KAC5B,CAAC,CAAC;IAGH,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAY,EAAE,EAAE;IAC/C,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;QACjC,KAAK,EAAE,KAAK,CAAC,OAAO;QACpB,KAAK,EAAE,KAAK,CAAC,KAAK;KACnB,CAAC,CAAC;IAGH,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAGI,MAAM,gBAAgB,GAAG,CAAC,MAAW,EAAE,EAAE;IAC9C,MAAM,QAAQ,GAAG,CAAC,MAAc,EAAE,EAAE;QAClC,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,iCAAiC,CAAC,CAAC;QAEjE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAGH,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;YAC9E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IACjD,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC;AAlBW,QAAA,gBAAgB,oBAkB3B"}