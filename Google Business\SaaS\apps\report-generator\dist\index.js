"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const dotenv_1 = __importDefault(require("dotenv"));
const winston_1 = require("winston");
const SimpleReportGenerator_1 = __importDefault(require("./services/SimpleReportGenerator"));
const SimpleVisualizationEngine_1 = __importDefault(require("./services/SimpleVisualizationEngine"));
const SimpleDeliveryService_1 = __importDefault(require("./services/SimpleDeliveryService"));
const AnalyzerClient_1 = __importDefault(require("./services/AnalyzerClient"));
const WorldClassReportGenerator_1 = require("./services/WorldClassReportGenerator");
const validation_simple_1 = require("./middleware/validation-simple");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.REPORT_GENERATOR_PORT || 3003;
const reportGenerator = new SimpleReportGenerator_1.default();
const visualizationEngine = new SimpleVisualizationEngine_1.default();
const deliveryService = new SimpleDeliveryService_1.default();
const analyzerClient = new AnalyzerClient_1.default();
const worldClassReportGenerator = new WorldClassReportGenerator_1.WorldClassReportGenerator();
const logger = (0, winston_1.createLogger)({
    level: 'info',
    format: require('winston').format.combine(require('winston').format.timestamp(), require('winston').format.json()),
    transports: [
        new (require('winston').transports.Console)(),
        new (require('winston').transports.File)({ filename: 'logs/report-generator.log' })
    ]
});
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', async (req, res) => {
    try {
        const analyzerHealthy = await analyzerClient.healthCheck();
        res.json({
            success: true,
            data: {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                service: 'GMB Report Generator',
                environment: process.env.NODE_ENV || 'development',
                dependencies: {
                    analyzerService: analyzerHealthy ? 'healthy' : 'unavailable'
                }
            }
        });
    }
    catch (error) {
        res.json({
            success: true,
            data: {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                service: 'GMB Report Generator',
                environment: process.env.NODE_ENV || 'development',
                dependencies: {
                    analyzerService: 'unavailable'
                }
            }
        });
    }
});
app.post('/api/generate/pdf', validation_simple_1.validateReportRequest, async (req, res) => {
    try {
        const { analysisData, businessData, options = {} } = req.body;
        logger.info('PDF generation requested', {
            businessName: businessData?.businessName,
            template: options.template || 'default'
        });
        const reportData = {
            businessData,
            analysisData,
            options: {
                ...options,
                format: 'pdf'
            }
        };
        const result = await reportGenerator.generatePDFReport(reportData);
        res.json({
            success: true,
            data: {
                ...result,
                filename: `GMB-Audit-Report-${businessData.businessName?.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.pdf`,
                downloadUrl: `/api/download/${result.reportId}`
            }
        });
    }
    catch (error) {
        logger.error('PDF generation request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process PDF generation request',
                code: 'PDF_GENERATION_ERROR'
            }
        });
    }
});
app.post('/api/generate/integrated-report', validation_simple_1.validateIntegratedReport, async (req, res) => {
    try {
        const { businessData, competitorData, options = {} } = req.body;
        logger.info('Integrated report generation requested', {
            businessName: businessData.businessName,
            hasCompetitors: !!competitorData?.length
        });
        const analysisResponse = await analyzerClient.analyzeBusinessProfile(businessData, competitorData);
        if (!analysisResponse.success) {
            throw new Error('Failed to analyze business profile');
        }
        const reportData = {
            businessData,
            analysisData: analysisResponse.data,
            options: { ...options, format: 'pdf' }
        };
        const reportResult = await reportGenerator.generatePDFReport(reportData);
        const chartTypes = options.chartTypes || ['score', 'breakdown', 'competitive'];
        const chartRequests = chartTypes.map((type) => ({
            chartType: type === 'score' ? 'score-gauge' :
                type === 'breakdown' ? 'breakdown-chart' :
                    type === 'competitive' ? 'competitive-comparison' : 'insights-matrix',
            data: analysisResponse.data,
            options: {}
        }));
        const chartResults = {};
        for (const request of chartRequests) {
            const chartResult = await visualizationEngine.generateChart(request);
            chartResults[request.chartType.replace('-gauge', '').replace('-chart', '').replace('-comparison', '')] = chartResult;
        }
        res.json({
            success: true,
            data: {
                report: reportResult,
                charts: chartResults,
                analysis: {
                    scores: analysisResponse.data.scores,
                    insights: analysisResponse.data.insights,
                    recommendations: analysisResponse.data.recommendations,
                    overallHealth: analysisResponse.data.analysis.overallHealth
                },
                timestamp: new Date().toISOString(),
                source: 'integrated_analysis'
            }
        });
    }
    catch (error) {
        logger.error('Integrated report generation failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to generate integrated report',
                code: 'INTEGRATED_REPORT_ERROR',
                details: error instanceof Error ? error.message : String(error)
            }
        });
    }
});
app.post('/api/generate/world-class-report', validation_simple_1.validateIntegratedReport, async (req, res) => {
    try {
        const { businessData, competitorData, options = {} } = req.body;
        logger.info('World-class report generation requested', {
            businessName: businessData.businessName,
            hasCompetitors: !!competitorData?.length,
            template: options.template || 'default'
        });
        const analysisResponse = await analyzerClient.analyzeBusinessProfile(businessData, competitorData);
        if (!analysisResponse.success) {
            throw new Error('Failed to analyze business profile');
        }
        const worldClassReportData = {
            businessData,
            analysisData: analysisResponse.data,
            options: {
                ...options,
                template: options.template || 'healthcare',
                branding: {
                    companyName: 'GMB Audit Pro',
                    primaryColor: '#2563eb',
                    secondaryColor: '#059669',
                    ...options.branding
                },
                includeAppendix: options.includeAppendix !== false,
                includeTemplates: options.includeTemplates !== false
            }
        };
        const worldClassResult = await worldClassReportGenerator.generateWorldClassReport(worldClassReportData);
        const chartTypes = options.chartTypes || ['score', 'breakdown', 'competitive', 'sentiment'];
        const chartRequests = chartTypes.map((type) => ({
            chartType: type === 'score' ? 'score-gauge' :
                type === 'breakdown' ? 'breakdown-chart' :
                    type === 'competitive' ? 'competitive-comparison' :
                        type === 'sentiment' ? 'insights-matrix' : 'insights-matrix',
            data: analysisResponse.data,
            options: { enhanced: true, worldClass: true }
        }));
        const chartResults = {};
        for (const request of chartRequests) {
            const chartResult = await visualizationEngine.generateChart(request);
            chartResults[request.chartType.replace('-gauge', '').replace('-chart', '').replace('-comparison', '').replace('-analysis', '')] = chartResult;
        }
        res.json({
            success: true,
            data: {
                report: {
                    reportId: worldClassResult.reportId,
                    htmlContent: worldClassResult.htmlContent,
                    size: worldClassResult.size,
                    generationTime: worldClassResult.generationTime,
                    format: 'world-class-html',
                    template: options.template || 'healthcare',
                    features: [
                        'Professional cover page',
                        'Executive summary',
                        'Visual scorecard dashboard',
                        'Geographic heatmap',
                        'Image quality audit',
                        'Review sentiment analysis',
                        'Strategic recommendations',
                        '30-day engagement plan',
                        'Ready-to-use templates',
                        'Technical appendix'
                    ]
                },
                charts: chartResults,
                analysis: {
                    scores: analysisResponse.data.scores,
                    insights: analysisResponse.data.insights,
                    recommendations: analysisResponse.data.recommendations,
                    overallHealth: analysisResponse.data.analysis.overallHealth
                },
                businessImpact: {
                    potentialRevenueIncrease: '25-40%',
                    patientAcquisitionIncrease: '15-25 new patients/month',
                    localSearchImprovement: '3-5 position improvement',
                    reviewGenerationIncrease: '10-15 new reviews/month',
                    photoEngagementIncrease: '50-75% increase in profile views'
                },
                timestamp: new Date().toISOString(),
                source: 'world_class_analysis',
                version: '2.0'
            }
        });
    }
    catch (error) {
        logger.error('World-class report generation failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to generate world-class report',
                code: 'WORLD_CLASS_REPORT_ERROR',
                details: error instanceof Error ? error.message : String(error)
            }
        });
    }
});
app.post('/api/generate/charts', validation_simple_1.validateChartGeneration, async (req, res) => {
    try {
        const { analysisData, chartTypes = ['score', 'breakdown', 'competitive'] } = req.body;
        logger.info('Chart generation requested', { chartTypes });
        const chartRequests = chartTypes.map((type) => ({
            chartType: type === 'score' ? 'score-gauge' :
                type === 'breakdown' ? 'breakdown-chart' :
                    type === 'competitive' ? 'competitive-comparison' : 'insights-matrix',
            data: analysisData,
            options: {}
        }));
        const chartResults = await visualizationEngine.generateMultipleCharts(chartRequests);
        const charts = {};
        chartResults.forEach((result, index) => {
            const originalType = chartTypes[index];
            charts[originalType] = {
                id: result.chartId,
                type: result.chartType,
                data: result.data,
                metadata: {
                    width: 800,
                    height: 600,
                    title: `${originalType} Chart`,
                    timestamp: result.timestamp
                }
            };
        });
        res.json({
            success: true,
            data: {
                charts,
                timestamp: new Date().toISOString(),
                status: 'generated'
            }
        });
    }
    catch (error) {
        logger.error('Chart generation request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process chart generation request',
                code: 'CHART_GENERATION_ERROR'
            }
        });
    }
});
app.post('/api/deliver/email', validation_simple_1.validateEmailDelivery, async (req, res) => {
    try {
        const { reportId, recipient, subject, message } = req.body;
        logger.info('Email delivery requested', { reportId, recipient });
        const deliveryRequest = {
            reportId,
            recipient,
            subject,
            message,
            attachPDF: true
        };
        const result = await deliveryService.sendEmailReport(deliveryRequest);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        logger.error('Email delivery request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process email delivery request',
                code: 'EMAIL_DELIVERY_ERROR'
            }
        });
    }
});
app.post('/api/deliver/whatsapp', validation_simple_1.validateWhatsAppDelivery, async (req, res) => {
    try {
        const { reportId, phoneNumber, message } = req.body;
        logger.info('WhatsApp delivery requested', { reportId, phoneNumber });
        const deliveryRequest = {
            reportId,
            phoneNumber,
            message,
            includeLink: true
        };
        const result = await deliveryService.sendWhatsAppReport(deliveryRequest);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        logger.error('WhatsApp delivery request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process WhatsApp delivery request',
                code: 'WHATSAPP_DELIVERY_ERROR'
            }
        });
    }
});
app.get('/api/download/:reportId', validation_simple_1.validateReportId, async (req, res) => {
    try {
        const { reportId } = req.params;
        logger.info('Report download requested', { reportId });
        res.json({
            success: true,
            data: {
                reportId,
                message: 'Download functionality will be implemented in Phase 4',
                timestamp: new Date().toISOString(),
                status: 'Phase 4 implementation in progress'
            }
        });
    }
    catch (error) {
        logger.error('Report download request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process download request',
                code: 'DOWNLOAD_ERROR'
            }
        });
    }
});
app.get('/api/portal/:reportId', validation_simple_1.validateReportId, async (req, res) => {
    try {
        const { reportId } = req.params;
        logger.info('Portal access requested', { reportId });
        const cachedReport = reportGenerator.getCachedReport(reportId);
        if (!cachedReport) {
            return res.status(404).json({
                success: false,
                error: {
                    message: 'Report not found',
                    code: 'REPORT_NOT_FOUND'
                }
            });
        }
        if (cachedReport.htmlContent) {
            res.setHeader('Content-Type', 'text/html');
            res.send(cachedReport.htmlContent);
        }
        else {
            res.json({
                success: true,
                data: {
                    reportId,
                    title: 'GMB Audit Report Portal',
                    format: cachedReport.format,
                    template: cachedReport.template,
                    timestamp: cachedReport.timestamp,
                    status: cachedReport.status
                }
            });
        }
    }
    catch (error) {
        logger.error('Portal access request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process portal access request',
                code: 'PORTAL_ACCESS_ERROR'
            }
        });
    }
});
app.get('/api/templates', async (req, res) => {
    try {
        logger.info('Template listing requested');
        const templates = [
            { id: 'default', name: 'Default Template', description: 'Standard GMB audit report' },
            { id: 'restaurant', name: 'Restaurant Template', description: 'Specialized for restaurants' },
            { id: 'service', name: 'Service Business Template', description: 'For service-based businesses' },
            { id: 'healthcare', name: 'Healthcare Template', description: 'Medical and healthcare businesses' }
        ];
        res.json({
            success: true,
            data: {
                templates,
                status: 'Phase 4 implementation in progress'
            }
        });
    }
    catch (error) {
        logger.error('Template listing request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process template listing request',
                code: 'TEMPLATE_LIST_ERROR'
            }
        });
    }
});
app.post('/api/deliver/multi', async (req, res) => {
    try {
        const { reportId, email, whatsapp } = req.body;
        logger.info('Multi-channel delivery requested', { reportId, email: !!email, whatsapp: !!whatsapp });
        const results = await deliveryService.sendMultiChannelReport(email, whatsapp);
        res.json({
            success: true,
            data: {
                deliveries: results,
                totalSent: results.filter(r => r.status === 'sent').length,
                totalFailed: results.filter(r => r.status === 'failed').length,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        logger.error('Multi-channel delivery failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to process multi-channel delivery',
                code: 'MULTI_DELIVERY_ERROR'
            }
        });
    }
});
app.get('/api/delivery/stats', async (req, res) => {
    try {
        const stats = deliveryService.getDeliveryStats();
        const history = deliveryService.getDeliveryHistory(10);
        res.json({
            success: true,
            data: {
                statistics: stats,
                recentDeliveries: history,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        logger.error('Delivery stats request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to get delivery statistics',
                code: 'DELIVERY_STATS_ERROR'
            }
        });
    }
});
app.get('/api/delivery/:deliveryId', async (req, res) => {
    try {
        const { deliveryId } = req.params;
        const delivery = deliveryService.getDeliveryStatus(deliveryId);
        if (!delivery) {
            return res.status(404).json({
                success: false,
                error: {
                    message: 'Delivery not found',
                    code: 'DELIVERY_NOT_FOUND'
                }
            });
        }
        res.json({
            success: true,
            data: delivery
        });
    }
    catch (error) {
        logger.error('Delivery status request failed', { error: error instanceof Error ? error.message : String(error) });
        res.status(500).json({
            success: false,
            error: {
                message: 'Failed to get delivery status',
                code: 'DELIVERY_STATUS_ERROR'
            }
        });
    }
});
app.get('/api/docs', (req, res) => {
    res.json({
        success: true,
        data: {
            service: 'GMB Report Generator API',
            version: '1.0.0',
            endpoints: {
                health: '/health',
                generatePDF: '/api/generate/pdf',
                generateIntegratedReport: '/api/generate/integrated-report',
                generateWorldClassReport: '/api/generate/world-class-report',
                generateCharts: '/api/generate/charts',
                deliverEmail: '/api/deliver/email',
                deliverWhatsApp: '/api/deliver/whatsapp',
                deliverMulti: '/api/deliver/multi',
                deliveryStatus: '/api/delivery/:deliveryId',
                deliveryStats: '/api/delivery/stats',
                download: '/api/download/:reportId',
                portal: '/api/portal/:reportId',
                templates: '/api/templates'
            },
            description: 'Professional report generation and delivery service for GMB audits'
        }
    });
});
const server = app.listen(PORT, () => {
    logger.info(`GMB Report Generator started on port ${PORT}`);
    logger.info('Available endpoints:');
    logger.info('  GET  /health - Health check');
    logger.info('  POST /api/generate/pdf - Generate PDF report');
    logger.info('  POST /api/generate/charts - Generate visualizations');
    logger.info('  POST /api/deliver/email - Send report via email');
    logger.info('  POST /api/deliver/whatsapp - Send report via WhatsApp');
    logger.info('  GET  /api/download/:reportId - Download report');
    logger.info('  GET  /api/portal/:reportId - Web portal access');
    logger.info('  GET  /api/templates - List available templates');
    logger.info('  GET  /api/docs - API documentation');
});
server.on('error', (error) => {
    logger.error('Server error:', error);
});
process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', error);
});
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection at:', promise, 'reason:', reason);
});
process.on('SIGINT', () => {
    logger.info('Received SIGINT, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
process.on('SIGTERM', () => {
    logger.info('Received SIGTERM, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=index.js.map