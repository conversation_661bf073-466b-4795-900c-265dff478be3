const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🛡️ Phase 4 Testing & Verification - Error Handling & Resilience Testing\n');

function makeRequest(options, data = null, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const responseData = res.headers['content-type']?.includes('application/json') 
            ? JSON.parse(body) 
            : body;
          resolve({ 
            status: res.statusCode, 
            data: responseData, 
            responseTime,
            headers: res.headers
          });
        } catch (error) {
          resolve({ 
            status: res.statusCode, 
            data: body, 
            responseTime,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({ error: error.message, responseTime: Date.now() - startTime });
    });

    req.setTimeout(timeout, () => {
      req.destroy();
      reject({ error: 'Request timeout', responseTime: timeout });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testResilience() {
  const results = {
    timestamp: new Date().toISOString(),
    inputValidation: {},
    errorHandling: {},
    fallbackMechanisms: {},
    serviceUnavailable: {},
    malformedData: {},
    summary: {}
  };

  try {
    // 1. Input Validation Testing
    console.log('1. 🔍 Input Validation Testing...');
    
    const validationTests = [
      {
        name: 'Missing Business Data',
        endpoint: '/api/generate/integrated-report',
        data: { competitorData: [] },
        expectedStatus: 400
      },
      {
        name: 'Invalid Business Name',
        endpoint: '/api/generate/integrated-report',
        data: { businessData: { businessName: "" }, competitorData: [] },
        expectedStatus: 400
      },
      {
        name: 'Malformed JSON',
        endpoint: '/api/generate/integrated-report',
        data: '{"businessData":{"businessName":"Test"',
        expectedStatus: 400,
        raw: true
      },
      {
        name: 'Empty Request Body',
        endpoint: '/api/generate/integrated-report',
        data: {},
        expectedStatus: 400
      },
      {
        name: 'Invalid Email Format',
        endpoint: '/api/deliver/email',
        data: { reportId: 'test-123', recipient: 'invalid-email', subject: 'Test' },
        expectedStatus: 400
      }
    ];

    results.inputValidation.tests = [];
    
    for (const test of validationTests) {
      console.log(`   Testing: ${test.name}...`);
      
      try {
        const response = await makeRequest({
          hostname: 'localhost',
          port: 3003,
          path: test.endpoint,
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, test.raw ? test.data : JSON.stringify(test.data));

        const passed = response.status === test.expectedStatus;
        results.inputValidation.tests.push({
          name: test.name,
          expectedStatus: test.expectedStatus,
          actualStatus: response.status,
          passed,
          hasErrorMessage: response.data && response.data.error && response.data.error.message,
          responseTime: response.responseTime
        });

        console.log(`      ${passed ? '✅' : '❌'} Expected ${test.expectedStatus}, got ${response.status}`);
        
      } catch (error) {
        results.inputValidation.tests.push({
          name: test.name,
          expectedStatus: test.expectedStatus,
          actualStatus: 0,
          passed: false,
          error: error.error,
          responseTime: error.responseTime
        });
        console.log(`      ❌ Request failed: ${error.error}`);
      }
    }

    // 2. Error Handling Testing
    console.log('\n2. ⚠️ Error Handling Testing...');
    
    const errorTests = [
      {
        name: 'Non-existent Report ID',
        endpoint: '/api/portal/non-existent-report-id',
        method: 'GET',
        expectedStatus: 404
      },
      {
        name: 'Invalid Report ID Format',
        endpoint: '/api/portal/invalid-format',
        method: 'GET',
        expectedStatus: 404
      },
      {
        name: 'Non-existent Endpoint',
        endpoint: '/api/non-existent-endpoint',
        method: 'GET',
        expectedStatus: 404
      },
      {
        name: 'Method Not Allowed',
        endpoint: '/api/generate/integrated-report',
        method: 'GET',
        expectedStatus: [404, 405] // Either is acceptable
      }
    ];

    results.errorHandling.tests = [];
    
    for (const test of errorTests) {
      console.log(`   Testing: ${test.name}...`);
      
      try {
        const response = await makeRequest({
          hostname: 'localhost',
          port: 3003,
          path: test.endpoint,
          method: test.method
        });

        const expectedStatuses = Array.isArray(test.expectedStatus) ? test.expectedStatus : [test.expectedStatus];
        const passed = expectedStatuses.includes(response.status);
        
        results.errorHandling.tests.push({
          name: test.name,
          expectedStatus: test.expectedStatus,
          actualStatus: response.status,
          passed,
          hasErrorMessage: response.data && (response.data.error || response.data.message),
          responseTime: response.responseTime
        });

        console.log(`      ${passed ? '✅' : '❌'} Expected ${test.expectedStatus}, got ${response.status}`);
        
      } catch (error) {
        results.errorHandling.tests.push({
          name: test.name,
          expectedStatus: test.expectedStatus,
          actualStatus: 0,
          passed: false,
          error: error.error,
          responseTime: error.responseTime
        });
        console.log(`      ❌ Request failed: ${error.error}`);
      }
    }

    // 3. Fallback Mechanism Testing
    console.log('\n3. 🔄 Fallback Mechanism Testing...');
    
    // Test with analyzer service potentially unavailable
    console.log('   Testing fallback when analyzer service is unavailable...');
    
    try {
      // First, check if analyzer is available
      const analyzerHealth = await makeRequest({
        hostname: 'localhost',
        port: 3002,
        path: '/health',
        method: 'GET'
      }, null, 3000);

      console.log(`   Analyzer service status: ${analyzerHealth.status === 200 ? 'Available' : 'Unavailable'}`);

      // Test integrated report generation (should use fallback if analyzer unavailable)
      const fallbackTest = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: '/api/generate/integrated-report',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }, {
        businessData: {
          businessName: "Fallback Test Business",
          address: "123 Test St, Test City, TC 12345",
          phone: "******-TEST-123",
          website: "https://testbusiness.com"
        },
        competitorData: [],
        options: { template: 'default' }
      });

      const fallbackWorking = fallbackTest.status === 200 && fallbackTest.data.success;
      
      results.fallbackMechanisms = {
        analyzerServiceAvailable: analyzerHealth.status === 200,
        integratedReportGeneration: {
          status: fallbackTest.status,
          success: fallbackWorking,
          responseTime: fallbackTest.responseTime,
          usedFallback: analyzerHealth.status !== 200,
          hasAnalysisData: fallbackTest.data.data && fallbackTest.data.data.analysis
        }
      };

      console.log(`      ${fallbackWorking ? '✅' : '❌'} Integrated report generation: ${fallbackTest.status}`);
      console.log(`      ${results.fallbackMechanisms.integratedReportGeneration.hasAnalysisData ? '✅' : '❌'} Analysis data present`);
      
    } catch (error) {
      results.fallbackMechanisms = {
        error: error.error,
        analyzerServiceAvailable: false,
        integratedReportGeneration: { success: false, error: error.error }
      };
      console.log(`      ❌ Fallback test failed: ${error.error}`);
    }

    // 4. Malformed Data Handling
    console.log('\n4. 🔧 Malformed Data Handling...');
    
    const malformedTests = [
      {
        name: 'Extremely Large Business Name',
        data: {
          businessData: {
            businessName: 'A'.repeat(10000),
            address: "123 Test St"
          }
        }
      },
      {
        name: 'Special Characters in Business Name',
        data: {
          businessData: {
            businessName: "Test<script>alert('xss')</script>Business",
            address: "123 Test St"
          }
        }
      },
      {
        name: 'Null Values',
        data: {
          businessData: {
            businessName: null,
            address: null
          }
        }
      },
      {
        name: 'Array Instead of Object',
        data: {
          businessData: ["not", "an", "object"]
        }
      }
    ];

    results.malformedData.tests = [];
    
    for (const test of malformedTests) {
      console.log(`   Testing: ${test.name}...`);
      
      try {
        const response = await makeRequest({
          hostname: 'localhost',
          port: 3003,
          path: '/api/generate/integrated-report',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, test.data);

        const handledGracefully = response.status === 400 && response.data && response.data.error;
        
        results.malformedData.tests.push({
          name: test.name,
          status: response.status,
          handledGracefully,
          hasErrorMessage: response.data && response.data.error && response.data.error.message,
          responseTime: response.responseTime
        });

        console.log(`      ${handledGracefully ? '✅' : '❌'} Status ${response.status}, graceful: ${handledGracefully}`);
        
      } catch (error) {
        results.malformedData.tests.push({
          name: test.name,
          status: 0,
          handledGracefully: false,
          error: error.error,
          responseTime: error.responseTime
        });
        console.log(`      ❌ Request failed: ${error.error}`);
      }
    }

    // 5. Service Unavailable Testing
    console.log('\n5. 🚫 Service Unavailable Testing...');
    
    // Test requests to non-existent ports
    const unavailableTests = [
      { name: 'Non-existent Service Port', port: 9999, path: '/health' },
      { name: 'Wrong Port for Service', port: 3004, path: '/api/test' }
    ];

    results.serviceUnavailable.tests = [];
    
    for (const test of unavailableTests) {
      console.log(`   Testing: ${test.name}...`);
      
      try {
        const response = await makeRequest({
          hostname: 'localhost',
          port: test.port,
          path: test.path,
          method: 'GET'
        }, null, 3000);

        results.serviceUnavailable.tests.push({
          name: test.name,
          status: response.status,
          unexpected: true,
          responseTime: response.responseTime
        });

        console.log(`      ⚠️ Unexpected response: ${response.status}`);
        
      } catch (error) {
        const expectedError = error.error.includes('ECONNREFUSED') || error.error.includes('timeout');
        
        results.serviceUnavailable.tests.push({
          name: test.name,
          status: 0,
          expectedError,
          error: error.error,
          responseTime: error.responseTime
        });

        console.log(`      ${expectedError ? '✅' : '❌'} Expected connection error: ${error.error}`);
      }
    }

    // 6. Summary and Assessment
    console.log('\n6. 📊 Resilience Summary...');
    
    const validationPassed = results.inputValidation.tests.filter(t => t.passed).length;
    const validationTotal = results.inputValidation.tests.length;
    
    const errorHandlingPassed = results.errorHandling.tests.filter(t => t.passed).length;
    const errorHandlingTotal = results.errorHandling.tests.length;
    
    const malformedHandled = results.malformedData.tests.filter(t => t.handledGracefully).length;
    const malformedTotal = results.malformedData.tests.length;
    
    const serviceUnavailableHandled = results.serviceUnavailable.tests.filter(t => t.expectedError).length;
    const serviceUnavailableTotal = results.serviceUnavailable.tests.length;
    
    results.summary = {
      inputValidation: {
        passed: validationPassed,
        total: validationTotal,
        percentage: ((validationPassed / validationTotal) * 100).toFixed(1)
      },
      errorHandling: {
        passed: errorHandlingPassed,
        total: errorHandlingTotal,
        percentage: ((errorHandlingPassed / errorHandlingTotal) * 100).toFixed(1)
      },
      malformedDataHandling: {
        handled: malformedHandled,
        total: malformedTotal,
        percentage: ((malformedHandled / malformedTotal) * 100).toFixed(1)
      },
      serviceUnavailableHandling: {
        handled: serviceUnavailableHandled,
        total: serviceUnavailableTotal,
        percentage: serviceUnavailableTotal > 0 ? ((serviceUnavailableHandled / serviceUnavailableTotal) * 100).toFixed(1) : '100.0'
      },
      fallbackMechanisms: {
        working: results.fallbackMechanisms.integratedReportGeneration && results.fallbackMechanisms.integratedReportGeneration.success
      }
    };
    
    console.log(`   🔍 Input Validation: ${validationPassed}/${validationTotal} passed (${results.summary.inputValidation.percentage}%)`);
    console.log(`   ⚠️ Error Handling: ${errorHandlingPassed}/${errorHandlingTotal} passed (${results.summary.errorHandling.percentage}%)`);
    console.log(`   🔧 Malformed Data: ${malformedHandled}/${malformedTotal} handled (${results.summary.malformedDataHandling.percentage}%)`);
    console.log(`   🚫 Service Unavailable: ${serviceUnavailableHandled}/${serviceUnavailableTotal} handled (${results.summary.serviceUnavailableHandling.percentage}%)`);
    console.log(`   🔄 Fallback Mechanisms: ${results.summary.fallbackMechanisms.working ? '✅ Working' : '❌ Issues'}`);

    // Save results
    const resultsPath = path.join(__dirname, 'resilience-test-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed resilience results saved to: ${resultsPath}`);

    // Final assessment
    console.log('\n🎯 Resilience Assessment:');
    
    const validationHealthy = parseFloat(results.summary.inputValidation.percentage) >= 80;
    const errorHandlingHealthy = parseFloat(results.summary.errorHandling.percentage) >= 80;
    const malformedHealthy = parseFloat(results.summary.malformedDataHandling.percentage) >= 70;
    const unavailableHealthy = parseFloat(results.summary.serviceUnavailableHandling.percentage) >= 80;
    const fallbackHealthy = results.summary.fallbackMechanisms.working;
    
    console.log(`   🔍 Input Validation: ${validationHealthy ? '✅ EXCELLENT' : '⚠️ NEEDS IMPROVEMENT'}`);
    console.log(`   ⚠️ Error Handling: ${errorHandlingHealthy ? '✅ EXCELLENT' : '⚠️ NEEDS IMPROVEMENT'}`);
    console.log(`   🔧 Malformed Data Handling: ${malformedHealthy ? '✅ GOOD' : '⚠️ NEEDS IMPROVEMENT'}`);
    console.log(`   🚫 Service Unavailable Handling: ${unavailableHealthy ? '✅ EXCELLENT' : '⚠️ NEEDS IMPROVEMENT'}`);
    console.log(`   🔄 Fallback Mechanisms: ${fallbackHealthy ? '✅ WORKING' : '❌ ISSUES'}`);
    
    if (validationHealthy && errorHandlingHealthy && malformedHealthy && unavailableHealthy && fallbackHealthy) {
      console.log('\n   ✅ RESILIENCE TESTING: PASSED - System is robust and production ready!');
    } else {
      console.log('\n   ⚠️ RESILIENCE TESTING: IMPROVEMENTS RECOMMENDED');
    }

  } catch (error) {
    console.error('❌ Resilience testing failed:', error.message);
    results.error = error.message;
  }

  return results;
}

// Run resilience testing
testResilience().then(results => {
  console.log('\n🏁 Resilience Testing Complete!');
}).catch(error => {
  console.error('💥 Resilience testing suite crashed:', error.message);
});
