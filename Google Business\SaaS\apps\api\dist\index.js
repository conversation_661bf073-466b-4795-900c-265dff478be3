"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const config = {
    app: {
        port: process.env.PORT || 3000,
        corsOrigin: process.env.CORS_ORIGIN || '*',
    },
    security: {
        rateLimitWindowMs: 15 * 60 * 1000,
        rateLimitMaxRequests: 100,
    },
};
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
};
const app = (0, express_1.default)();
const PORT = config.app.port;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({ origin: config.app.corsOrigin }));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: { error: 'Too many requests from this IP, please try again later.' },
});
app.use('/api/', limiter);
app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - start;
        logger.info(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    });
    next();
});
app.get('/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
        },
    });
});
app.get('/api/status', (req, res) => {
    res.json({
        success: true,
        data: {
            message: 'GMB Audit Generator API is running',
            version: '1.0.0',
            phase: 'Phase 1 - Core Infrastructure',
        },
    });
});
app.get('/', (req, res) => {
    res.json({
        message: 'GMB Audit Generator API',
        version: '1.0.0',
        status: 'running',
        phase: 'Phase 1 - Core Infrastructure',
        endpoints: {
            health: '/health',
            status: '/api/status',
        },
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            message: `Route ${req.originalUrl} not found`,
            code: 'NOT_FOUND',
        },
    });
});
app.use((error, req, res, next) => {
    const statusCode = error.statusCode || 500;
    logger.error('API Error', { message: error.message, statusCode });
    res.status(statusCode).json({
        success: false,
        error: {
            message: error.message || 'Internal server error',
            code: error.code || 'INTERNAL_ERROR',
        },
    });
});
const server = app.listen(PORT, () => {
    logger.info(`🚀 GMB Audit Generator API started on port ${PORT}`);
    logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
    logger.info(`📡 API Status: http://localhost:${PORT}/api/status`);
    logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
process.on('SIGTERM', () => {
    logger.info('SIGTERM received. Starting graceful shutdown...');
    server.close(() => {
        logger.info('HTTP server closed.');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger.info('SIGINT received. Starting graceful shutdown...');
    server.close(() => {
        logger.info('HTTP server closed.');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=index.js.map