import axios from 'axios';

// Simple logger for Phase 2
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.debug(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

export interface KeywordRankingRequest {
  businessName: string;
  website?: string;
  category: string;
  location: string;
  targetKeywords?: string[];
}

export interface KeywordRanking {
  keyword: string;
  position: number;
  searchVolume: number;
  difficulty: number;
  url?: string;
  lastChecked: Date;
  source: string;
  isVerified: boolean;
}

export interface RankingAnalysis {
  totalKeywords: number;
  averagePosition: number;
  topRankingKeywords: KeywordRanking[];
  improvementOpportunities: KeywordRanking[];
  competitorComparison: {
    betterThan: number;
    worseThan: number;
  };
  methodology: string;
}

export class KeywordRankingService {
  private perplexityApiKey: string;
  private searchConsoleConnected: boolean;

  constructor() {
    this.perplexityApiKey = process.env.PERPLEXITY_API_KEY || '';
    this.searchConsoleConnected = false; // Would be true if Google Search Console is connected
    
    if (!this.perplexityApiKey) {
      logger.warn('Perplexity API key not found. Keyword ranking analysis will be limited.');
    }
  }

  /**
   * Analyze keyword rankings using real data sources
   */
  async analyzeKeywordRankings(request: KeywordRankingRequest): Promise<RankingAnalysis> {
    logger.info('Starting keyword ranking analysis', { 
      businessName: request.businessName,
      location: request.location 
    });

    try {
      let rankings: KeywordRanking[] = [];

      // 1. Try Google Search Console first (most accurate)
      if (this.searchConsoleConnected && request.website) {
        rankings = await this.getSearchConsoleRankings(request);
      }
      
      // 2. Fallback to Perplexity Sonar live search
      if (rankings.length === 0 && this.perplexityApiKey) {
        rankings = await this.getPerplexityRankings(request);
      }

      // 3. If no real data available, return transparent message
      if (rankings.length === 0) {
        return this.getNoDataAvailableAnalysis(request);
      }

      // Analyze the rankings
      const analysis = this.analyzeRankings(rankings);
      
      logger.info('Keyword ranking analysis completed', { 
        businessName: request.businessName,
        totalKeywords: analysis.totalKeywords,
        averagePosition: analysis.averagePosition
      });

      return analysis;

    } catch (error: any) {
      logger.error('Keyword ranking analysis failed', { error: error.message });
      return this.getErrorAnalysis(error.message);
    }
  }

  /**
   * Get rankings from Google Search Console (requires integration)
   */
  private async getSearchConsoleRankings(request: KeywordRankingRequest): Promise<KeywordRanking[]> {
    // This would integrate with Google Search Console API
    // For now, return empty array indicating integration needed
    logger.info('Google Search Console integration required for accurate ranking data');
    return [];
  }

  /**
   * Get rankings using Perplexity Sonar live search
   */
  private async getPerplexityRankings(request: KeywordRankingRequest): Promise<KeywordRanking[]> {
    try {
      const keywords = request.targetKeywords || this.generateRelevantKeywords(request);
      const rankings: KeywordRanking[] = [];

      for (const keyword of keywords.slice(0, 10)) { // Limit to 10 keywords to avoid API limits
        const ranking = await this.checkKeywordRanking(keyword, request);
        if (ranking) {
          rankings.push(ranking);
        }
        
        // Add delay to respect API limits
        await this.delay(1000);
      }

      return rankings;

    } catch (error: any) {
      logger.error('Perplexity ranking check failed', { error: error.message });
      return [];
    }
  }

  /**
   * Check individual keyword ranking using Perplexity
   */
  private async checkKeywordRanking(keyword: string, request: KeywordRankingRequest): Promise<KeywordRanking | null> {
    try {
      const searchQuery = `Search for "${keyword}" in ${request.location} and find where "${request.businessName}" ranks in the search results. Provide the exact position number if found, or state if not found in top 50 results.`;

      const response = await axios.post('https://api.perplexity.ai/chat/completions', {
        model: 'llama-3.1-sonar-small-128k-online',
        messages: [
          {
            role: 'system',
            content: 'You are a SEO ranking checker. Provide accurate search result positions. If a business is not found in search results, clearly state this. Never fabricate ranking positions.'
          },
          {
            role: 'user',
            content: searchQuery
          }
        ],
        max_tokens: 500,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${this.perplexityApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        return null;
      }

      // Parse the ranking from the response
      const position = this.parseRankingPosition(content);
      if (position === null) {
        return null;
      }

      return {
        keyword,
        position,
        searchVolume: this.estimateSearchVolume(keyword, request.category),
        difficulty: this.estimateKeywordDifficulty(keyword),
        lastChecked: new Date(),
        source: 'Perplexity Sonar Live Search',
        isVerified: true
      };

    } catch (error: any) {
      logger.error('Individual keyword ranking check failed', { keyword, error: error.message });
      return null;
    }
  }

  /**
   * Parse ranking position from Perplexity response
   */
  private parseRankingPosition(content: string): number | null {
    const lowerContent = content.toLowerCase();
    
    // Look for position indicators
    const positionMatch = content.match(/position\s*(\d+)|rank\s*(\d+)|#(\d+)|\b(\d+)(?:st|nd|rd|th)\s*position/i);
    if (positionMatch) {
      const position = parseInt(positionMatch[1] || positionMatch[2] || positionMatch[3] || positionMatch[4]);
      return position <= 50 ? position : null; // Only return if in top 50
    }

    // Check for "not found" indicators
    if (lowerContent.includes('not found') || lowerContent.includes('does not appear') || lowerContent.includes('not in the results')) {
      return null;
    }

    // Look for page indicators (assuming 10 results per page)
    const pageMatch = content.match(/page\s*(\d+)/i);
    if (pageMatch) {
      const page = parseInt(pageMatch[1]);
      return page <= 5 ? (page - 1) * 10 + 5 : null; // Estimate middle of page
    }

    return null;
  }

  /**
   * Generate relevant keywords for the business
   */
  private generateRelevantKeywords(request: KeywordRankingRequest): string[] {
    const { businessName, category, location } = request;
    
    return [
      `${category} ${location}`,
      `best ${category} ${location}`,
      `${category} near me`,
      businessName,
      `${businessName} ${location}`,
      `top ${category} ${location}`,
      `${category} services ${location}`,
      `professional ${category} ${location}`
    ];
  }

  /**
   * Estimate search volume based on keyword and category
   */
  private estimateSearchVolume(keyword: string, category: string): number {
    // This is a conservative estimation - in production, would use keyword research tools
    const baseVolumes: { [key: string]: number } = {
      'dental': 2000,
      'restaurant': 3000,
      'salon': 1500,
      'lawyer': 1800,
      'doctor': 2500
    };

    const baseVolume = baseVolumes[category.toLowerCase()] || 1000;
    
    // Adjust based on keyword specificity
    if (keyword.includes('near me')) return Math.floor(baseVolume * 1.5);
    if (keyword.includes('best')) return Math.floor(baseVolume * 1.2);
    if (keyword.includes('top')) return Math.floor(baseVolume * 1.1);
    
    return baseVolume;
  }

  /**
   * Estimate keyword difficulty
   */
  private estimateKeywordDifficulty(keyword: string): number {
    // Conservative difficulty estimation (0-100 scale)
    if (keyword.includes('near me')) return 65;
    if (keyword.includes('best')) return 75;
    if (keyword.includes('top')) return 70;
    if (keyword.split(' ').length > 3) return 45; // Long-tail keywords are easier
    
    return 60; // Default moderate difficulty
  }

  /**
   * Analyze the collected rankings
   */
  private analyzeRankings(rankings: KeywordRanking[]): RankingAnalysis {
    const totalKeywords = rankings.length;
    const averagePosition = rankings.reduce((sum, r) => sum + r.position, 0) / totalKeywords;
    
    const topRankingKeywords = rankings
      .filter(r => r.position <= 10)
      .sort((a, b) => a.position - b.position);
    
    const improvementOpportunities = rankings
      .filter(r => r.position > 10 && r.position <= 30)
      .sort((a, b) => b.searchVolume - a.searchVolume);

    return {
      totalKeywords,
      averagePosition: Math.round(averagePosition * 10) / 10,
      topRankingKeywords,
      improvementOpportunities,
      competitorComparison: {
        betterThan: Math.floor(totalKeywords * 0.3), // Conservative estimate
        worseThan: Math.floor(totalKeywords * 0.4)
      },
      methodology: 'Live search ranking verification using Perplexity Sonar API with real-time search result analysis'
    };
  }

  /**
   * Return analysis when no data is available
   */
  private getNoDataAvailableAnalysis(request: KeywordRankingRequest): RankingAnalysis {
    return {
      totalKeywords: 0,
      averagePosition: 0,
      topRankingKeywords: [],
      improvementOpportunities: [],
      competitorComparison: {
        betterThan: 0,
        worseThan: 0
      },
      methodology: 'Keyword ranking analysis requires Google Search Console integration or Perplexity Sonar API access for accurate data'
    };
  }

  /**
   * Return analysis when error occurs
   */
  private getErrorAnalysis(errorMessage: string): RankingAnalysis {
    return {
      totalKeywords: 0,
      averagePosition: 0,
      topRankingKeywords: [],
      improvementOpportunities: [],
      competitorComparison: {
        betterThan: 0,
        worseThan: 0
      },
      methodology: `Keyword ranking analysis failed: ${errorMessage}`
    };
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
