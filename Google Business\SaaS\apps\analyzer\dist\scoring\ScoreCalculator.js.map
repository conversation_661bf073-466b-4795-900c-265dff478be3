{"version": 3, "file": "ScoreCalculator.js", "sourceRoot": "", "sources": ["../../src/scoring/ScoreCalculator.ts"], "names": [], "mappings": ";;;;;;AACA,sDAA8B;AAE9B,MAAM,MAAM,GAAG,iBAAO,CAAC,YAAY,CAAC;IAClC,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE;IAC7B,UAAU,EAAE,CAAC,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;CAC/C,CAAC,CAAC;AAEH,MAAa,eAAe;IACT,OAAO,GAAG;QACzB,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,GAAG,EAAE,IAAI;QACT,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,IAAI;QACX,GAAG,EAAE,IAAI;KACV,CAAC;IAEF,qBAAqB,CAAC,IAAkB;QACtC,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;gBACtD,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;gBAC9D,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;gBAClD,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBACnD,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACjD,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;aAClD,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;iBACnC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAC5B,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAgC,CAAC,CAAC,EAAE,CAAC,CAClE,CAAC;YAEJ,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE3C,MAAM,MAAM,GAAmB;gBAC7B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC5B,SAAS,EAAE,MAAM;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK;aACN,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/E,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACzC,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,OAAc;QACzC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE/C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC;QAGhG,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,MAAM,YAAY,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAG1C,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC5C,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;YAClC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YACvD,OAAO,UAAU,GAAG,cAAc,CAAC;QACrC,CAAC,CAAC,CAAC,MAAM,CAAC;QAEV,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,YAAY,GAAG,WAAW,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAEO,wBAAwB,CAAC,QAAe;QAC9C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAGjD,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAErC,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;QAGpF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;QAG5D,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;QAClG,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,cAAc,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IAEO,iBAAiB,CAAC,UAAe;QACvC,IAAI,CAAC,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ;YAAE,OAAO,CAAC,CAAC;QAE5D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,QAAQ,GAAG,CAAC,CAAC;QAGjB,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC5B,QAAQ,IAAI,EAAE,CAAC;YACf,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;gBAC/E,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;iBAAM,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAGD,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YAC3B,QAAQ,IAAI,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YACjD,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;gBAC3C,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;iBAAM,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gBAC1B,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAGD,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAC1B,QAAQ,IAAI,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9F,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;gBACvB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAGD,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,QAAQ,IAAI,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;YACtD,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACtC,CAAC;QAGD,QAAQ,IAAI,EAAE,CAAC;QACf,IAAI,UAAU,CAAC,KAAK;YAAE,KAAK,IAAI,EAAE,CAAC;QAClC,IAAI,UAAU,CAAC,OAAO;YAAE,KAAK,IAAI,EAAE,CAAC;QAEpC,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAEO,mBAAmB,CAAC,MAAa;QACvC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE7C,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAG7B,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAG3E,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CACrD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAClD,CAAC,MAAM,CAAC;QAET,MAAM,cAAc,GAAG,CAAC,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAEpE,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,cAAc,CAAC,CAAC;IACpD,CAAC;IAEO,mBAAmB,CAAC,KAAY;QACtC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE3C,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAGhC,MAAM,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,OAAO,QAAQ,GAAG,cAAc,CAAC;QACnC,CAAC,CAAC,CAAC,MAAM,CAAC;QAGV,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAGnD,MAAM,gBAAgB,GAAG,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,CAAC;QAEnE,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,gBAAgB,CAAC,CAAC;IACpD,CAAC;IAEO,iBAAiB,CAAC,SAAgB;QACxC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEpD,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;QAGxC,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CACtD,QAAQ,CAAC,UAAU,KAAK,IAAI,IAAI,QAAQ,CAAC,QAAQ,IAAI,GAAG,CACzD,CAAC,MAAM,CAAC;QAET,MAAM,gBAAgB,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAG9F,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,aAAa,CAAC,CAAC;IACtD,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,wBAAwB;QAC9B,OAAO;YACL,OAAO,EAAE,CAAC;YACV,SAAS,EAAE;gBACT,OAAO,EAAE,CAAC;gBACV,UAAU,EAAE,CAAC;gBACb,GAAG,EAAE,CAAC;gBACN,MAAM,EAAE,CAAC;gBACT,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;aACP;YACD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,GAAG;SACX,CAAC;IACJ,CAAC;IAGD,sBAAsB,CAAC,KAAa;QAClC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,kDAAkD,CAAC;QAC3E,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,iEAAiE,CAAC;QAC1F,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,oDAAoD,CAAC;QAC7E,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,kDAAkD,CAAC;QAC3E,OAAO,0DAA0D,CAAC;IACpE,CAAC;IAGD,sBAAsB,CAAC,SAAyB;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACxE,IAAI;YACJ,KAAK;YACL,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAiC,CAAC;YACvD,MAAM,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAiC,CAAC;SACxE,CAAC,CAAC,CAAC;QAEJ,OAAO,KAAK;aACT,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;aACnC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC;CACF;AA1PD,0CA0PC"}