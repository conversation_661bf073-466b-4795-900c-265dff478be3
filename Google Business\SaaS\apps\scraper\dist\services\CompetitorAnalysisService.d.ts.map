{"version": 3, "file": "CompetitorAnalysisService.d.ts", "sourceRoot": "", "sources": ["../../src/services/CompetitorAnalysisService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAYnE,MAAM,WAAW,uBAAuB;IACtC,YAAY,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,WAAW,CAAC,EAAE;QACZ,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,cAAe,SAAQ,OAAO;IAC7C,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,WAAW,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;IACvC,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,UAAU,EAAE,MAAM,EAAE,CAAC;CACtB;AAED,MAAM,WAAW,wBAAwB;IACvC,cAAc,EAAE;QACd,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,WAAW,EAAE,cAAc,EAAE,CAAC;IAC9B,QAAQ,EAAE;QACR,gBAAgB,EAAE,MAAM,CAAC;QACzB,aAAa,EAAE,MAAM,CAAC;QACtB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,gBAAgB,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;QAC5C,eAAe,EAAE,MAAM,EAAE,CAAC;QAC1B,eAAe,EAAE,MAAM,EAAE,CAAC;KAC3B,CAAC;IACF,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,qBAAa,yBAAyB;IACpC,OAAO,CAAC,eAAe,CAAqB;IAC5C,OAAO,CAAC,iBAAiB,CAAwB;IACjD,OAAO,CAAC,kBAAkB,CAAS;IACnC,OAAO,CAAC,gBAAgB,CAAS;;IAgBpB,kBAAkB,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,wBAAwB,CAAC;YAyCtF,eAAe;IAqD7B,OAAO,CAAC,qBAAqB;IAa7B,OAAO,CAAC,0BAA0B;YASpB,iBAAiB;IA0C/B,OAAO,CAAC,cAAc;IAatB,OAAO,CAAC,yBAAyB;IAajC,OAAO,CAAC,4BAA4B;YAoBtB,oBAAoB;IAmClC,OAAO,CAAC,4BAA4B;IA2BpC,OAAO,CAAC,iBAAiB;IAMzB,OAAO,CAAC,oCAAoC;IAoC5C,OAAO,CAAC,2BAA2B;IA8BnC,OAAO,CAAC,uBAAuB;IAuB/B,OAAO,CAAC,uBAAuB;YAmCjB,iCAAiC;YA+DjC,+BAA+B;IAgD7C,OAAO,CAAC,uBAAuB;IAuD/B,OAAO,CAAC,sBAAsB;IA8B9B,OAAO,CAAC,gBAAgB;YAWV,cAAc;IAmCf,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAGpC"}