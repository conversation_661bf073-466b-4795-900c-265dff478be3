"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gracefulShutdown = exports.TooManyRequestsError = exports.ConflictError = exports.ForbiddenError = exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AppErrorClass = exports.asyncHandler = exports.errorHandler = void 0;
const winston_1 = require("winston");
const logger = (0, winston_1.createLogger)({
    level: 'error',
    format: require('winston').format.combine(require('winston').format.timestamp(), require('winston').format.json()),
    transports: [
        new (require('winston').transports.Console)(),
        new (require('winston').transports.File)({ filename: 'logs/error.log' })
    ]
});
const errorHandler = (error, req, res, next) => {
    logger.error('Request error', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
    });
    let statusCode = error.statusCode || 500;
    let message = error.message || 'Internal server error';
    let code = error.code || 'INTERNAL_ERROR';
    if (error.name === 'ValidationError') {
        statusCode = 400;
        code = 'VALIDATION_ERROR';
        message = 'Invalid request data';
    }
    else if (error.name === 'UnauthorizedError') {
        statusCode = 401;
        code = 'UNAUTHORIZED';
        message = 'Authentication required';
    }
    else if (error.name === 'ForbiddenError') {
        statusCode = 403;
        code = 'FORBIDDEN';
        message = 'Access denied';
    }
    else if (error.name === 'NotFoundError') {
        statusCode = 404;
        code = 'NOT_FOUND';
        message = 'Resource not found';
    }
    else if (error.name === 'ConflictError') {
        statusCode = 409;
        code = 'CONFLICT';
        message = 'Resource conflict';
    }
    else if (error.name === 'TooManyRequestsError') {
        statusCode = 429;
        code = 'RATE_LIMIT_EXCEEDED';
        message = 'Too many requests';
    }
    if (error.name === 'MongoError' || error.name === 'MongooseError') {
        statusCode = 500;
        code = 'DATABASE_ERROR';
        message = 'Database operation failed';
    }
    if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        code = 'INVALID_TOKEN';
        message = 'Invalid authentication token';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        code = 'TOKEN_EXPIRED';
        message = 'Authentication token expired';
    }
    if (error.message.includes('puppeteer') || error.message.includes('browser')) {
        statusCode = 500;
        code = 'PDF_GENERATION_ERROR';
        message = 'Failed to generate PDF report';
    }
    if (error.message.includes('chart') || error.message.includes('canvas')) {
        statusCode = 500;
        code = 'CHART_GENERATION_ERROR';
        message = 'Failed to generate chart';
    }
    if (error.message.includes('SMTP') || error.message.includes('nodemailer')) {
        statusCode = 500;
        code = 'EMAIL_DELIVERY_ERROR';
        message = 'Failed to send email';
    }
    if (error.message.includes('Twilio') || error.message.includes('WhatsApp')) {
        statusCode = 500;
        code = 'WHATSAPP_DELIVERY_ERROR';
        message = 'Failed to send WhatsApp message';
    }
    if (error.code === 'ENOENT') {
        statusCode = 404;
        code = 'FILE_NOT_FOUND';
        message = 'File not found';
    }
    else if (error.code === 'EACCES') {
        statusCode = 403;
        code = 'FILE_ACCESS_DENIED';
        message = 'File access denied';
    }
    else if (error.code === 'EMFILE' || error.code === 'ENFILE') {
        statusCode = 500;
        code = 'TOO_MANY_FILES';
        message = 'Too many open files';
    }
    if (process.env.NODE_ENV === 'production' && statusCode === 500) {
        message = 'Internal server error';
    }
    res.status(statusCode).json({
        success: false,
        error: {
            message,
            code,
            ...(process.env.NODE_ENV === 'development' && {
                stack: error.stack,
                details: error
            })
        }
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
class AppErrorClass extends Error {
    statusCode;
    code;
    isOperational;
    constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppErrorClass = AppErrorClass;
class ValidationError extends AppErrorClass {
    constructor(message = 'Validation failed') {
        super(message, 400, 'VALIDATION_ERROR');
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends AppErrorClass {
    constructor(message = 'Resource not found') {
        super(message, 404, 'NOT_FOUND');
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends AppErrorClass {
    constructor(message = 'Authentication required') {
        super(message, 401, 'UNAUTHORIZED');
    }
}
exports.UnauthorizedError = UnauthorizedError;
class ForbiddenError extends AppErrorClass {
    constructor(message = 'Access denied') {
        super(message, 403, 'FORBIDDEN');
    }
}
exports.ForbiddenError = ForbiddenError;
class ConflictError extends AppErrorClass {
    constructor(message = 'Resource conflict') {
        super(message, 409, 'CONFLICT');
    }
}
exports.ConflictError = ConflictError;
class TooManyRequestsError extends AppErrorClass {
    constructor(message = 'Too many requests') {
        super(message, 429, 'RATE_LIMIT_EXCEEDED');
    }
}
exports.TooManyRequestsError = TooManyRequestsError;
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Promise Rejection', {
        reason: reason?.message || reason,
        stack: reason?.stack,
        promise: promise.toString()
    });
    if (process.env.NODE_ENV === 'production') {
        process.exit(1);
    }
});
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack
    });
    process.exit(1);
});
const gracefulShutdown = (server) => {
    const shutdown = (signal) => {
        logger.info(`Received ${signal}. Starting graceful shutdown...`);
        server.close(() => {
            logger.info('HTTP server closed');
            process.exit(0);
        });
        setTimeout(() => {
            logger.error('Could not close connections in time, forcefully shutting down');
            process.exit(1);
        }, 30000);
    };
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
};
exports.gracefulShutdown = gracefulShutdown;
//# sourceMappingURL=errorHandler.js.map