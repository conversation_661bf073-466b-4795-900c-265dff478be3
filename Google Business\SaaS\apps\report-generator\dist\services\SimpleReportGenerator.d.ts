export interface ReportData {
    businessData: any;
    analysisData: any;
    options?: {
        template?: string;
        format?: 'html' | 'pdf';
        includeCharts?: boolean;
    };
}
export interface ReportResponse {
    reportId: string;
    format: string;
    template: string;
    filePath?: string;
    htmlContent?: string;
    timestamp: string;
    status: string;
}
export declare class SimpleReportGenerator {
    private reportsCache;
    private templatesDir;
    constructor();
    generateHTMLReport(data: ReportData): Promise<ReportResponse>;
    generatePDFReport(data: ReportData): Promise<ReportResponse>;
    private renderTemplate;
    private replacePlaceholders;
    private generateInsightsHtml;
    private generateRecommendationsHtml;
    private getDefaultTemplate;
    getCachedReport(reportId: string): ReportResponse | undefined;
    clearCache(): void;
}
export default SimpleReportGenerator;
//# sourceMappingURL=SimpleReportGenerator.d.ts.map