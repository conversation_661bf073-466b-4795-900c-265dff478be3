require('dotenv').config();
const { WorldClassReportGenerator } = require('./apps/report-generator/dist/services/WorldClassReportGenerator.js');

async function testFullReport() {
  const generator = new WorldClassReportGenerator();
  
  const reportData = {
    businessData: {
      businessName: 'Naturals Training Academy',
      address: 'Thanjavur, Tamil Nadu, India',
      category: 'Beauty Training',
      phone: '+91-4362-274274',
      website: 'https://naturals.in/training-academy',
      description: 'Professional beauty training academy offering comprehensive courses in cosmetology, hair styling, and makeup artistry.',
      reviews: [],
      photos: [],
      posts: []
    },
    analysisData: {
      analysis: {},
      scores: {
        overall: 85,
        grade: 'B+',
        breakdown: {
          reviews: 88,
          visibility: 82,
          seo: 85,
          photos: 80,
          posts: 75,
          nap: 90
        }
      },
      insights: [
        { type: 'positive', text: 'Strong review rating of 4.8 stars' },
        { type: 'improvement', text: 'Add more photos to showcase services' },
        { type: 'opportunity', text: 'Increase posting frequency for better engagement' }
      ],
      recommendations: [],
      timestamp: new Date().toISOString()
    }
  };

  console.log('🔍 Testing full audit report generation...');
  console.log('Business:', reportData.businessData.businessName);

  try {
    const result = await generator.generateWorldClassReport(reportData);
    const report = result.htmlContent;
    
    // Look for specific competitor names in the report
    const competitorNames = [
      'LUV U The Women World',
      'Princess Beauty Parlour',
      'BEWITCH BEAUTY STUDIO',
      'Ananias Beauty'
    ];

    console.log('Searching for competitor content in report...');

    let foundCompetitors = [];
    competitorNames.forEach(name => {
      if (report.includes(name)) {
        foundCompetitors.push(name);
        console.log(`✅ Found competitor: ${name}`);
      }
    });

    // Check Local Visibility Map section specifically
    const mapSection = report.match(/🗺️ Local Visibility Map([\s\S]*?)(?=<section|$)/);
    if (mapSection) {
      console.log('\n📍 Local Visibility Map section analysis:');
      const mapContent = mapSection[1];

      if (mapContent.includes('High Density') || mapContent.includes('Medium Density')) {
        console.log('✅ Map density analysis found');
      }

      // Look for competitor count
      const densityMatch = mapContent.match(/(\d+) within/);
      if (densityMatch) {
        console.log(`✅ Competitor density: ${densityMatch[1]} competitors found within radius`);
      }
    }

    if (foundCompetitors.length > 0) {
      console.log(`\n🎉 SUCCESS! Found ${foundCompetitors.length} real competitors in the report:`);
      foundCompetitors.forEach((comp, i) => console.log(`${i+1}. ${comp}`));
    } else if (report.includes('Real competitor data requires')) {
      console.log('\n❌ FAILED: Still showing placeholder competitor data');
    } else {
      console.log('\n⚠️  Competitors may be integrated but not directly visible in text');
    }
    
    console.log(`\n📊 Report generated successfully! Size: ${report.length} bytes`);
    
  } catch (error) {
    console.error('❌ ERROR generating report:', error.message);
  }
}

testFullReport();
