import { Request, Response, NextFunction } from 'express';
export interface AppError extends Error {
    statusCode?: number;
    code?: string;
    isOperational?: boolean;
}
export declare const errorHandler: (error: AppError, req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare class AppErrorClass extends Error implements AppError {
    statusCode: number;
    code: string;
    isOperational: boolean;
    constructor(message: string, statusCode?: number, code?: string);
}
export declare class ValidationError extends AppErrorClass {
    constructor(message?: string);
}
export declare class NotFoundError extends AppErrorClass {
    constructor(message?: string);
}
export declare class UnauthorizedError extends AppErrorClass {
    constructor(message?: string);
}
export declare class ForbiddenError extends AppErrorClass {
    constructor(message?: string);
}
export declare class ConflictError extends AppErrorClass {
    constructor(message?: string);
}
export declare class TooManyRequestsError extends AppErrorClass {
    constructor(message?: string);
}
export declare const gracefulShutdown: (server: any) => void;
//# sourceMappingURL=errorHandler.d.ts.map