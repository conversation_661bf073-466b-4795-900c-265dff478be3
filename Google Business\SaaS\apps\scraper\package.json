{"name": "@gmb-audit/scraper", "version": "1.0.0", "description": "GMB data scraping service with anti-detection measures", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@gmb-audit/config": "*", "@gmb-audit/database": "*", "@gmb-audit/shared": "*", "@gmb-audit/types": "*", "axios": "^1.6.2", "bull": "^4.12.0", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "playwright": "^1.40.0", "playwright-extra": "^4.3.6", "proxy-agent": "^6.3.1", "puppeteer-extra-plugin-stealth": "^2.11.2", "sharp": "^0.33.0", "user-agents": "^1.0.1235", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/user-agents": "^1.0.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}}