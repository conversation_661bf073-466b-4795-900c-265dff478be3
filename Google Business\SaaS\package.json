{"name": "gmb-audit-generator", "version": "1.0.0", "description": "World-class Google Business Profile audit report generator", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:api": "turbo run dev --filter=@gmb-audit/api", "dev:scraper": "turbo run dev --filter=@gmb-audit/scraper", "dev:analyzer": "turbo run dev --filter=@gmb-audit/analyzer", "dev:report-generator": "turbo run dev --filter=@gmb-audit/report-generator", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "test": "turbo run test", "test:watch": "turbo run test:watch", "type-check": "turbo run type-check", "clean": "turbo run clean", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:down": "docker-compose -f docker-compose.dev.yml down", "setup": "npm install && npm run db:generate && npm run docker:dev"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/gmb-audit-generator.git"}, "keywords": ["google-business", "local-seo", "audit", "report-generator", "automation"], "author": "Your Name", "license": "MIT"}