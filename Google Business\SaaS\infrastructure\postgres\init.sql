-- Initialize PostgreSQL database for GMB Audit Generator
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis" CASCADE;
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create custom types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('ADMIN', 'USER', 'API_USER');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE report_type AS ENUM ('BASIC', 'COMPREHENSIVE', 'COMPETITOR_ANALYSIS', 'CUSTOM');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE report_status AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE delivery_method AS ENUM ('EMAIL', 'WHATSAPP', 'DOWNLOAD', 'API');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE delivery_status AS ENUM ('PENDING', 'SENT', 'DELIVERED', 'FAILED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance (will be created by Prisma, but good to have as backup)
-- These will be created after tables are generated by Prisma

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate distance between two points
CREATE OR REPLACE FUNCTION calculate_distance(
    lat1 DECIMAL,
    lon1 DECIMAL,
    lat2 DECIMAL,
    lon2 DECIMAL
) RETURNS DECIMAL AS $$
DECLARE
    earth_radius DECIMAL := 3959; -- Earth radius in miles
    dlat DECIMAL;
    dlon DECIMAL;
    a DECIMAL;
    c DECIMAL;
BEGIN
    dlat := RADIANS(lat2 - lat1);
    dlon := RADIANS(lon2 - lon1);
    
    a := SIN(dlat/2) * SIN(dlat/2) + 
         COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * 
         SIN(dlon/2) * SIN(dlon/2);
    
    c := 2 * ATAN2(SQRT(a), SQRT(1-a));
    
    RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql;

-- Create a function to generate business insights
CREATE OR REPLACE FUNCTION get_business_insights(business_id_param TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_reviews', COALESCE(SUM(review_count), 0),
        'avg_rating', COALESCE(AVG(rating), 0),
        'total_photos', COALESCE(SUM(total_photos), 0),
        'last_updated', MAX(scraped_at),
        'data_points', COUNT(*)
    )
    INTO result
    FROM gmb_data
    WHERE business_id = business_id_param;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create materialized view for business analytics (will be refreshed periodically)
-- This will be created after the tables exist

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE gmb_audit_db TO gmb_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO gmb_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO gmb_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO gmb_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO gmb_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO gmb_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO gmb_user;

-- Log successful initialization
INSERT INTO system_metrics (metric_type, metric_name, value, unit, timestamp)
VALUES ('system', 'database_initialized', 1, 'boolean', CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;
