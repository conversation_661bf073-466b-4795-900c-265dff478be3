const http = require('http');

// Test data
const testBusinessData = {
  businessName: "Joe's Pizza",
  address: "123 Main St, New York, NY 10001",
  phone: "(*************",
  website: "https://joespizza.com"
};

const testAnalysisData = {
  overallScore: 75,
  grade: "B",
  breakdown: {
    reviews: 80,
    visibility: 70,
    seo: 75,
    photos: 65,
    posts: 85,
    nap: 90
  },
  insights: [
    {
      type: "strength",
      title: "Strong Review Score",
      description: "Your business has excellent customer reviews with an average rating of 4.5 stars.",
      impact: "high"
    },
    {
      type: "weakness", 
      title: "Limited Photo Gallery",
      description: "Your business profile has only 5 photos. Adding more high-quality images can improve visibility.",
      impact: "medium"
    },
    {
      type: "opportunity",
      title: "Increase Post Frequency",
      description: "Regular posts can improve engagement and visibility in local search results.",
      impact: "medium"
    }
  ],
  recommendations: [
    {
      title: "Add More Photos",
      description: "Upload at least 10 high-quality photos showcasing your products, interior, and exterior.",
      priority: "high",
      actionItems: [
        "Take professional photos of your best dishes",
        "Capture interior and exterior shots",
        "Add photos of your team in action"
      ],
      expectedImpact: "medium",
      effortLevel: "low"
    },
    {
      title: "Optimize Business Description",
      description: "Update your business description to include relevant keywords and services.",
      priority: "medium",
      actionItems: [
        "Research local keywords",
        "Update business description",
        "Include services and specialties"
      ],
      expectedImpact: "high",
      effortLevel: "low"
    }
  ]
};

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testEndpoints() {
  console.log('🧪 Testing Report Generator Endpoints\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await makeRequest({
      hostname: 'localhost',
      port: 3003,
      path: '/health',
      method: 'GET'
    });
    console.log(`   Status: ${healthResponse.status}`);
    console.log(`   Response: ${JSON.stringify(healthResponse.data, null, 2)}\n`);

    // Test 2: Generate PDF Report
    console.log('2. Testing PDF Generation...');
    const pdfResponse = await makeRequest({
      hostname: 'localhost',
      port: 3003,
      path: '/api/generate/pdf',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      businessData: testBusinessData,
      analysisData: testAnalysisData,
      options: {
        template: 'default',
        format: 'pdf'
      }
    });
    console.log(`   Status: ${pdfResponse.status}`);
    console.log(`   Response: ${JSON.stringify(pdfResponse.data, null, 2)}\n`);

    // Test 3: Generate Charts
    console.log('3. Testing Chart Generation...');
    const chartResponse = await makeRequest({
      hostname: 'localhost',
      port: 3003,
      path: '/api/generate/charts',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      analysisData: testAnalysisData,
      chartTypes: ['score', 'breakdown', 'competitive']
    });
    console.log(`   Status: ${chartResponse.status}`);
    console.log(`   Response: ${JSON.stringify(chartResponse.data, null, 2)}\n`);

    // Test 4: Access Report Portal (if PDF was generated)
    if (pdfResponse.data && pdfResponse.data.success && pdfResponse.data.data.reportId) {
      console.log('4. Testing Report Portal...');
      const portalResponse = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: `/api/portal/${pdfResponse.data.data.reportId}`,
        method: 'GET'
      });
      console.log(`   Status: ${portalResponse.status}`);
      if (typeof portalResponse.data === 'string' && portalResponse.data.includes('<html>')) {
        console.log(`   Response: HTML content received (${portalResponse.data.length} characters)\n`);
      } else {
        console.log(`   Response: ${JSON.stringify(portalResponse.data, null, 2)}\n`);
      }

      // Test 5: Email Delivery
      console.log('5. Testing Email Delivery...');
      const emailResponse = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: '/api/deliver/email',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }, {
        reportId: pdfResponse.data.data.reportId,
        recipient: '<EMAIL>',
        subject: 'Your GMB Audit Report',
        message: 'Please find your Google Business Profile audit report attached.'
      });
      console.log(`   Status: ${emailResponse.status}`);
      console.log(`   Response: ${JSON.stringify(emailResponse.data, null, 2)}\n`);

      // Test 6: WhatsApp Delivery
      console.log('6. Testing WhatsApp Delivery...');
      const whatsappResponse = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: '/api/deliver/whatsapp',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }, {
        reportId: pdfResponse.data.data.reportId,
        phoneNumber: '+1234567890',
        message: 'Your GMB audit report is ready!'
      });
      console.log(`   Status: ${whatsappResponse.status}`);
      console.log(`   Response: ${JSON.stringify(whatsappResponse.data, null, 2)}\n`);

      // Test 7: Multi-Channel Delivery
      console.log('7. Testing Multi-Channel Delivery...');
      const multiResponse = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: '/api/deliver/multi',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }, {
        reportId: pdfResponse.data.data.reportId,
        email: {
          reportId: pdfResponse.data.data.reportId,
          recipient: '<EMAIL>',
          subject: 'Multi-Channel Test'
        },
        whatsapp: {
          reportId: pdfResponse.data.data.reportId,
          phoneNumber: '+9876543210',
          message: 'Multi-channel delivery test'
        }
      });
      console.log(`   Status: ${multiResponse.status}`);
      console.log(`   Response: ${JSON.stringify(multiResponse.data, null, 2)}\n`);

      // Test 8: Delivery Statistics
      console.log('8. Testing Delivery Statistics...');
      const statsResponse = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: '/api/delivery/stats',
        method: 'GET'
      });
      console.log(`   Status: ${statsResponse.status}`);
      console.log(`   Response: ${JSON.stringify(statsResponse.data, null, 2)}\n`);
    }

    console.log('✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run tests
testEndpoints();
