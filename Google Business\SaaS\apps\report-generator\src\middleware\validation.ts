import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';

// Validation schemas
const reportRequestSchema = Joi.object({
  analysisData: Joi.object({
    scores: Joi.object({
      overall: Joi.number().min(0).max(100).required(),
      grade: Joi.string().valid('A', 'B', 'C', 'D', 'F').required(),
      breakdown: Joi.object({
        reviews: Joi.number().min(0).max(100).required(),
        visibility: Joi.number().min(0).max(100).required(),
        seo: Joi.number().min(0).max(100).required(),
        photos: Joi.number().min(0).max(100).required(),
        posts: Joi.number().min(0).max(100).required(),
        nap: Joi.number().min(0).max(100).required()
      }).required()
    }).required(),
    insights: Joi.array().items(
      Joi.object({
        type: Joi.string().valid('strength', 'weakness', 'opportunity', 'threat').required(),
        title: Joi.string().required(),
        description: Joi.string().required(),
        impact: Joi.string().valid('high', 'medium', 'low').required(),
        confidence: Joi.number().min(0).max(1).required()
      })
    ).required(),
    recommendations: Joi.array().items(
      Joi.object({
        title: Joi.string().required(),
        description: Joi.string().required(),
        priority: Joi.string().valid('critical', 'high', 'medium', 'low').required(),
        actionItems: Joi.array().items(
          Joi.object({
            task: Joi.string().required(),
            effort: Joi.string().valid('low', 'medium', 'high').required(),
            timeline: Joi.string().required()
          })
        ).required(),
        expectedImpact: Joi.object({
          scoreIncrease: Joi.number().min(0).required(),
          timeframe: Joi.string().required()
        }).required()
      })
    ).required()
  }).required(),
  businessData: Joi.object({
    businessName: Joi.string().required(),
    address: Joi.string().optional(),
    phone: Joi.string().optional(),
    website: Joi.string().uri().optional(),
    category: Joi.string().optional(),
    rating: Joi.number().min(0).max(5).optional(),
    reviewCount: Joi.number().min(0).optional()
  }).required(),
  options: Joi.object({
    template: Joi.string().valid('default', 'restaurant', 'service', 'healthcare', 'executive').optional(),
    branding: Joi.object({
      logo: Joi.string().optional(),
      primaryColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional(),
      secondaryColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional(),
      companyName: Joi.string().optional()
    }).optional(),
    includeCharts: Joi.boolean().optional(),
    includeRecommendations: Joi.boolean().optional(),
    includeInsights: Joi.boolean().optional(),
    format: Joi.string().valid('pdf', 'html').optional(),
    quality: Joi.string().valid('standard', 'high').optional()
  }).optional()
});

const emailDeliverySchema = Joi.object({
  reportId: Joi.string().uuid().required(),
  recipient: Joi.string().email().required(),
  subject: Joi.string().max(200).optional(),
  message: Joi.string().max(2000).optional()
});

const whatsappDeliverySchema = Joi.object({
  reportId: Joi.string().uuid().required(),
  phoneNumber: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).required(),
  message: Joi.string().max(1000).optional()
});

const chartGenerationSchema = Joi.object({
  analysisData: Joi.object().required(),
  chartTypes: Joi.array().items(
    Joi.string().valid(
      'score-gauge',
      'breakdown-chart',
      'competitive-comparison',
      'insights-matrix',
      'recommendations-priority'
    )
  ).min(1).required()
});

// Validation middleware functions
export const validateReportRequest = (req: Request, res: Response, next: NextFunction) => {
  const { error } = reportRequestSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid request data',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }
  
  next();
};

export const validateEmailDelivery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = emailDeliverySchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid email delivery data',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }
  
  next();
};

export const validateWhatsAppDelivery = (req: Request, res: Response, next: NextFunction) => {
  const { error } = whatsappDeliverySchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid WhatsApp delivery data',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }
  
  next();
};

export const validateChartGeneration = (req: Request, res: Response, next: NextFunction) => {
  const { error } = chartGenerationSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid chart generation data',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }
  
  next();
};

// Generic validation helper
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        }
      });
    }
    
    next();
  };
};

// Parameter validation
export const validateReportId = (req: Request, res: Response, next: NextFunction) => {
  const { reportId } = req.params;
  
  if (!reportId || !Joi.string().uuid().validate(reportId).error === undefined) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Invalid report ID format',
        code: 'INVALID_REPORT_ID'
      }
    });
  }
  
  next();
};

// Rate limiting validation
export const validateRateLimit = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip || 'unknown';
    const now = Date.now();
    
    const clientData = requests.get(clientId);
    
    if (!clientData || now > clientData.resetTime) {
      requests.set(clientId, {
        count: 1,
        resetTime: now + windowMs
      });
      return next();
    }
    
    if (clientData.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: {
          message: 'Too many requests',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
        }
      });
    }
    
    clientData.count++;
    next();
  };
};

// File size validation for uploads
export const validateFileSize = (maxSize: number = 10 * 1024 * 1024) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.headers['content-length']) {
      const contentLength = parseInt(req.headers['content-length']);
      if (contentLength > maxSize) {
        return res.status(413).json({
          success: false,
          error: {
            message: 'File too large',
            code: 'FILE_TOO_LARGE',
            maxSize: maxSize
          }
        });
      }
    }
    
    next();
  };
};
