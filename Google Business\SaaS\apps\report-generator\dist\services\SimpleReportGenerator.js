"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleReportGenerator = void 0;
const uuid_1 = require("uuid");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class SimpleReportGenerator {
    reportsCache = new Map();
    templatesDir;
    constructor() {
        this.templatesDir = path.join(__dirname, '../templates');
    }
    async generateHTMLReport(data) {
        const reportId = (0, uuid_1.v4)();
        try {
            const template = data.options?.template || 'default';
            const htmlContent = await this.renderTemplate(template, data);
            const response = {
                reportId,
                format: 'html',
                template,
                htmlContent,
                timestamp: new Date().toISOString(),
                status: 'generated'
            };
            this.reportsCache.set(reportId, response);
            return response;
        }
        catch (error) {
            throw new Error(`Failed to generate HTML report: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async generatePDFReport(data) {
        const reportId = (0, uuid_1.v4)();
        try {
            const htmlReport = await this.generateHTMLReport(data);
            const response = {
                reportId,
                format: 'pdf',
                template: data.options?.template || 'default',
                htmlContent: htmlReport.htmlContent,
                timestamp: new Date().toISOString(),
                status: 'html_generated_pdf_pending'
            };
            this.reportsCache.set(reportId, response);
            return response;
        }
        catch (error) {
            throw new Error(`Failed to generate PDF report: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async renderTemplate(templateName, data) {
        try {
            const templatePath = path.join(this.templatesDir, `${templateName}.html`);
            let template;
            if (fs.existsSync(templatePath)) {
                template = fs.readFileSync(templatePath, 'utf-8');
            }
            else {
                template = this.getDefaultTemplate();
            }
            const rendered = this.replacePlaceholders(template, data);
            return rendered;
        }
        catch (error) {
            throw new Error(`Failed to render template '${templateName}': ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    replacePlaceholders(template, data) {
        const { businessData, analysisData } = data;
        let rendered = template;
        if (businessData) {
            rendered = rendered.replace(/{{businessName}}/g, businessData.businessName || 'Unknown Business');
            rendered = rendered.replace(/{{businessAddress}}/g, businessData.address || 'Address not provided');
            rendered = rendered.replace(/{{businessPhone}}/g, businessData.phone || 'Phone not provided');
            rendered = rendered.replace(/{{businessWebsite}}/g, businessData.website || 'Website not provided');
        }
        if (analysisData) {
            rendered = rendered.replace(/{{overallScore}}/g, analysisData.overallScore?.toString() || '0');
            rendered = rendered.replace(/{{grade}}/g, analysisData.grade || 'F');
            rendered = rendered.replace(/{{reportDate}}/g, new Date().toLocaleDateString());
            if (analysisData.breakdown) {
                Object.keys(analysisData.breakdown).forEach(category => {
                    const score = analysisData.breakdown[category];
                    rendered = rendered.replace(new RegExp(`{{${category}Score}}`, 'g'), score?.toString() || '0');
                });
            }
            if (analysisData.insights) {
                const insightsHtml = this.generateInsightsHtml(analysisData.insights);
                rendered = rendered.replace(/{{insights}}/g, insightsHtml);
            }
            if (analysisData.recommendations) {
                const recommendationsHtml = this.generateRecommendationsHtml(analysisData.recommendations);
                rendered = rendered.replace(/{{recommendations}}/g, recommendationsHtml);
            }
        }
        return rendered;
    }
    generateInsightsHtml(insights) {
        if (!insights || insights.length === 0) {
            return '<p>No insights available.</p>';
        }
        const insightsByType = insights.reduce((acc, insight) => {
            const type = insight.type || 'general';
            if (!acc[type])
                acc[type] = [];
            acc[type].push(insight);
            return acc;
        }, {});
        let html = '<div class="insights-container">';
        Object.keys(insightsByType).forEach(type => {
            html += `<div class="insight-category">`;
            html += `<h4 class="insight-type">${type.charAt(0).toUpperCase() + type.slice(1)}s</h4>`;
            html += '<ul class="insight-list">';
            insightsByType[type].forEach((insight) => {
                html += `<li class="insight-item">`;
                html += `<strong>${insight.title || 'Insight'}:</strong> `;
                html += `${insight.description || 'No description available'}`;
                if (insight.impact) {
                    html += ` <span class="impact-badge impact-${insight.impact}">${insight.impact}</span>`;
                }
                html += `</li>`;
            });
            html += '</ul></div>';
        });
        html += '</div>';
        return html;
    }
    generateRecommendationsHtml(recommendations) {
        if (!recommendations || recommendations.length === 0) {
            return '<p>No recommendations available.</p>';
        }
        let html = '<div class="recommendations-container">';
        html += '<ol class="recommendation-list">';
        recommendations.forEach((rec, index) => {
            html += `<li class="recommendation-item">`;
            html += `<div class="recommendation-header">`;
            html += `<h5>${rec.title || `Recommendation ${index + 1}`}</h5>`;
            if (rec.priority) {
                html += ` <span class="priority-badge priority-${rec.priority}">${rec.priority}</span>`;
            }
            html += `</div>`;
            html += `<p class="recommendation-description">${rec.description || 'No description available'}</p>`;
            if (rec.actionItems && rec.actionItems.length > 0) {
                html += '<ul class="action-items">';
                rec.actionItems.forEach((action) => {
                    const actionText = typeof action === 'string' ? action :
                        action.task || action.description ||
                            JSON.stringify(action);
                    html += `<li>${actionText}</li>`;
                });
                html += '</ul>';
            }
            if (rec.expectedImpact || rec.effortLevel) {
                html += '<div class="recommendation-meta">';
                if (rec.expectedImpact) {
                    const impactText = typeof rec.expectedImpact === 'object' ?
                        `+${rec.expectedImpact.scoreIncrease || 0} points in ${rec.expectedImpact.timeframe || 'unknown time'}` :
                        rec.expectedImpact;
                    html += `<span class="meta-item">Impact: ${impactText}</span>`;
                }
                if (rec.effortLevel) {
                    html += `<span class="meta-item">Effort: ${rec.effortLevel}</span>`;
                }
                html += '</div>';
            }
            html += `</li>`;
        });
        html += '</ol></div>';
        return html;
    }
    getDefaultTemplate() {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GMB Audit Report - {{businessName}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #3b82f6; padding-bottom: 20px; margin-bottom: 30px; }
        .score-section { text-align: center; margin: 30px 0; }
        .score-circle { display: inline-block; width: 120px; height: 120px; border-radius: 50%; background: #3b82f6; color: white; line-height: 120px; font-size: 24px; font-weight: bold; }
        .grade { font-size: 48px; margin: 10px 0; }
        .section { margin: 30px 0; }
        .section h3 { color: #1f2937; border-bottom: 1px solid #e5e7eb; padding-bottom: 10px; }
        .insight-category { margin: 20px 0; }
        .insight-type { color: #374151; margin: 15px 0 10px 0; }
        .insight-list { list-style-type: disc; margin-left: 20px; }
        .insight-item { margin: 8px 0; }
        .recommendation-list { margin-left: 20px; }
        .recommendation-item { margin: 15px 0; }
        .recommendation-header { display: flex; align-items: center; gap: 10px; }
        .priority-badge, .impact-badge { padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .priority-high { background: #fee2e2; color: #dc2626; }
        .priority-medium { background: #fef3c7; color: #d97706; }
        .priority-low { background: #dcfce7; color: #16a34a; }
        .impact-high { background: #dbeafe; color: #2563eb; }
        .impact-medium { background: #f3e8ff; color: #7c3aed; }
        .impact-low { background: #f0f9ff; color: #0284c7; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Google Business Profile Audit Report</h1>
            <h2>{{businessName}}</h2>
            <p>{{businessAddress}}</p>
            <p>Report Date: {{reportDate}}</p>
        </div>

        <div class="score-section">
            <div class="score-circle">{{overallScore}}</div>
            <div class="grade">Grade: {{grade}}</div>
        </div>

        <div class="section">
            <h3>Key Insights</h3>
            {{insights}}
        </div>

        <div class="section">
            <h3>Recommendations</h3>
            {{recommendations}}
        </div>

        <div class="footer">
            <p>Generated by GMB Audit Report Generator</p>
        </div>
    </div>
</body>
</html>`;
    }
    getCachedReport(reportId) {
        return this.reportsCache.get(reportId);
    }
    clearCache() {
        this.reportsCache.clear();
    }
}
exports.SimpleReportGenerator = SimpleReportGenerator;
exports.default = SimpleReportGenerator;
//# sourceMappingURL=SimpleReportGenerator.js.map