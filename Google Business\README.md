# Google Business Profile (GBP) Audit System

## Complete n8n Workflow for Automated GBP Audits & Lead Generation

This folder contains a comprehensive n8n workflow system that generates professional Google Business Profile audit reports for lead generation.

## 🚀 Quick Start

### 1. Import the Workflow
Copy the JSON code from `GBP_Audit_Complete_Workflow.json` and import it into your n8n instance.

### 2. Test the Workflow
Send a POST request to the webhook endpoint with this sample data:

```json
{
  "businessName": "Joe's Pizza",
  "city": "New York",
  "state": "NY",
  "country": "USA",
  "category": "restaurant",
  "phone": "******-0123",
  "website": "https://joespizza.com",
  "contactName": "<PERSON>",
  "email": "<EMAIL>",
  "contactPhone": "******-0123",
  "company": "Joe's Pizza LLC"
}
```

### 3. Expected Output
The workflow will generate a comprehensive audit report with:
- Overall GBP Health Score (0-100)
- NAP Consistency Analysis
- Competitor Benchmarking
- Local Ranking Analysis
- Actionable Recommendations
- Lead Capture Integration

## 📋 Workflow Components

### Core Nodes:
1. **Webhook Trigger** - Receives audit requests
2. **Input Validator** - Validates and processes input data
3. **Google My Business Lookup** - Simulates GMB API calls
4. **NAP Consistency Check** - Checks business data across directories
5. **Competitor Analysis** - Analyzes top 3 local competitors
6. **Local Ranking Tracker** - Tracks keyword positions
7. **Report Generator** - Creates professional HTML/PDF reports
8. **Lead Capture System** - Captures and nurtures leads

## 🔧 Configuration Required

### API Keys Needed:
- Google My Business API
- Yelp Fusion API
- SerpAPI (for ranking data)
- SendGrid/Mailgun (for email automation)
- HubSpot/CRM API (for lead management)

### Environment Variables:
```
GMB_API_KEY=your_google_my_business_api_key
YELP_API_KEY=your_yelp_fusion_api_key
SERP_API_KEY=your_serpapi_key
SENDGRID_API_KEY=your_sendgrid_api_key
HUBSPOT_API_KEY=your_hubspot_api_key
```

## 🎯 Complete Workflow Implementation

### Files Included:
- **`GBP_Audit_Complete_Workflow.json`** - Complete n8n workflow (ready to import)
- **`test_workflow.json`** - Sample test data for workflow testing
- **`README.md`** - This comprehensive documentation

### Workflow Architecture:
The workflow contains **12 nodes** in a sophisticated processing pipeline:

1. **Webhook Trigger** (`/gbp-audit` endpoint)
2. **Input Validator** (validates required fields)
3. **Validation Check** (IF node for routing)
4. **Google My Business Lookup** (mock GMB API data)
5. **NAP Consistency Check** (directory analysis)
6. **Competitor Analysis** (competitive benchmarking)
7. **Local Ranking Analysis** (keyword position tracking)
8. **Report Aggregator** (combines all data + scoring)
9. **HTML Report Generator** (professional report creation)
10. **Lead Capture System** (lead scoring + qualification)
11. **Success Response** (returns audit results)
12. **Error Response** (handles validation failures)

### Data Flow:
```
POST /gbp-audit → Input Validation → [Valid?]
                                        ↓ Yes    ↓ No
                                   GMB Lookup   Error
                                        ↓
                              [Parallel Processing]
                              ↓       ↓       ↓
                         NAP Check  Competitor  Ranking
                              ↓       ↓       ↓
                              Report Aggregator
                                      ↓
                              HTML Report Generator
                                      ↓
                              Lead Capture System
                                      ↓
                              Success Response
```

## 🚀 Import & Setup Instructions

### Step 1: Import Workflow
1. Open your n8n instance
2. Click "Import from File" or "Import from URL"
3. Copy the entire contents of `GBP_Audit_Complete_Workflow.json`
4. Paste into n8n import dialog
5. Click "Import"

### Step 2: Activate Workflow
1. Click the workflow to open it
2. Click "Active" toggle in top-right
3. Note the webhook URL (will be displayed)

### Step 3: Test the Workflow
Use the sample data from `test_workflow.json`:

**cURL Example:**
```bash
curl -X POST "YOUR_N8N_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "businessName": "Joe'\''s Pizza",
    "city": "New York",
    "state": "NY",
    "country": "USA",
    "category": "restaurant",
    "phone": "******-0123",
    "website": "https://joespizza.com",
    "contactName": "Joe Smith",
    "email": "<EMAIL>",
    "contactPhone": "******-0123",
    "company": "Joe'\''s Pizza LLC"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "auditId": "audit_1706644800000_abc123def",
  "businessName": "Joe's Pizza",
  "overallScore": 67,
  "reportUrl": "https://reports.example.com/gbp-audit/audit_1706644800000_abc123def",
  "leadScore": 75,
  "priority": "High",
  "criticalIssues": 2,
  "recommendations": 8,
  "estimatedImpact": {
    "monthlyLeads": 82,
    "revenueOpportunity": 4950,
    "timeToResults": "30-90 days"
  },
  "message": "GBP audit completed successfully. Report generated and lead captured.",
  "nextSteps": [
    "Review the comprehensive audit report",
    "Implement priority recommendations",
    "Schedule a consultation for detailed strategy",
    "Monitor improvements over 30-90 days"
  ],
  "timestamp": "2025-01-30T20:00:00.000Z"
}
```