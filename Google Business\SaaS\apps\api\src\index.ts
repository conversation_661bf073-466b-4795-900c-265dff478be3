import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Simple configuration for Phase 1
const config = {
  app: {
    port: process.env.PORT || 3000,
    corsOrigin: process.env.CORS_ORIGIN || '*',
  },
  security: {
    rateLimitWindowMs: 15 * 60 * 1000, // 15 minutes
    rateLimitMaxRequests: 100,
  },
};

// Simple logger for Phase 1
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
};

// Create Express app
const app = express();
const PORT = config.app.port;

// Middleware
app.use(helmet());
app.use(cors({ origin: config.app.corsOrigin }));
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMaxRequests,
  message: { error: 'Too many requests from this IP, please try again later.' },
});
app.use('/api/', limiter);

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
  });
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    },
  });
});

// Basic API routes for Phase 1
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    data: {
      message: 'GMB Audit Generator API is running',
      version: '1.0.0',
      phase: 'Phase 1 - Core Infrastructure',
    },
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'GMB Audit Generator API',
    version: '1.0.0',
    status: 'running',
    phase: 'Phase 1 - Core Infrastructure',
    endpoints: {
      health: '/health',
      status: '/api/status',
    },
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: `Route ${req.originalUrl} not found`,
      code: 'NOT_FOUND',
    },
  });
});

// Error handler
app.use((error: any, req: any, res: any, next: any) => {
  const statusCode = error.statusCode || 500;
  logger.error('API Error', { message: error.message, statusCode });
  res.status(statusCode).json({
    success: false,
    error: {
      message: error.message || 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
    },
  });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`🚀 GMB Audit Generator API started on port ${PORT}`);
  logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
  logger.info(`📡 API Status: http://localhost:${PORT}/api/status`);
  logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Starting graceful shutdown...');
  server.close(() => {
    logger.info('HTTP server closed.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received. Starting graceful shutdown...');
  server.close(() => {
    logger.info('HTTP server closed.');
    process.exit(0);
  });
});

export default app;
