{"name": "@gmb-audit/report-generator", "version": "1.0.0", "description": "Report generation service for GMB audit reports", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "uuid": "^9.0.1", "joi": "^17.11.0", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/node": "^20.10.6", "typescript": "^5.3.3", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.11", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0"}, "keywords": ["gmb", "audit", "report", "pdf", "visualization", "charts"], "author": "GMB Audit Team", "license": "MIT"}