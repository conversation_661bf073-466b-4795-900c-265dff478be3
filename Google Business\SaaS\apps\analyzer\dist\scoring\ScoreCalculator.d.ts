import { ScoreBreakdown, AnalysisData } from '../types/analysis';
export declare class ScoreCalculator {
    private readonly weights;
    calculateOverallScore(data: AnalysisData): ScoreBreakdown;
    private calculateReviewScore;
    private calculateVisibilityScore;
    private calculateSEOScore;
    private calculatePhotoScore;
    private calculatePostsScore;
    private calculateNAPScore;
    private calculateGrade;
    private getDefaultScoreBreakdown;
    getScoreInterpretation(score: number): string;
    getTopImprovementAreas(breakdown: ScoreBreakdown): Array<{
        area: string;
        score: number;
        weight: number;
        impact: number;
    }>;
}
//# sourceMappingURL=ScoreCalculator.d.ts.map