export interface AnalysisResult {
    seoAnalysis: SEOAnalysis;
    sentimentAnalysis: SentimentAnalysis;
    competitiveAnalysis: CompetitiveAnalysis;
    photoAnalysis: PhotoAnalysis;
    overallHealth: number;
    timestamp: string;
}
export interface SEOAnalysis {
    onPageFactors: {
        businessName: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        description: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        categories: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        attributes: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        hours: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        website: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        phone: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
    };
    localSEOFactors: {
        napConsistency: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        citationQuality: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        reviewSignals: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        proximityFactors: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
    };
    technicalFactors: {
        websiteOptimization: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        mobileOptimization: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        pageSpeed: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        structuredData: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
    };
    overallScore: number;
    criticalIssues: string[];
    quickWins: string[];
}
export interface SentimentAnalysis {
    overall: {
        score: number;
        sentiment: 'positive' | 'neutral' | 'negative';
        confidence: number;
    };
    breakdown: {
        positive: number;
        neutral: number;
        negative: number;
    };
    trends: {
        recent: number;
        historical: number;
        trajectory: 'improving' | 'stable' | 'declining';
    };
    themes: {
        positive: Array<{
            theme: string;
            frequency: number;
            impact: number;
        }>;
        negative: Array<{
            theme: string;
            frequency: number;
            impact: number;
        }>;
    };
    responseAnalysis: {
        responseRate: number;
        averageResponseTime: number;
        responseQuality: number;
        recommendations: string[];
    };
}
export interface CompetitiveAnalysis {
    position: {
        rank: number;
        totalCompetitors: number;
        percentile: number;
    };
    strengths: Array<{
        factor: string;
        advantage: number;
        description: string;
    }>;
    weaknesses: Array<{
        factor: string;
        gap: number;
        description: string;
    }>;
    opportunities: Array<{
        factor: string;
        potential: number;
        description: string;
    }>;
    threats: Array<{
        factor: string;
        risk: number;
        description: string;
    }>;
    benchmarks: {
        averageRating: number;
        averageReviews: number;
        averagePhotos: number;
        averagePosts: number;
    };
}
export interface PhotoAnalysis {
    quantity: {
        score: number;
        current: number;
        recommended: number;
    };
    quality: {
        score: number;
        issues: string[];
        recommendations: string[];
    };
    diversity: {
        score: number;
        categories: string[];
        missing: string[];
    };
    recency: {
        score: number;
        lastUpdate: string;
        recommendations: string[];
    };
    optimization: {
        score: number;
        issues: string[];
        recommendations: string[];
    };
    overallScore: number;
}
export interface ScoreBreakdown {
    overall: number;
    grade: string;
    breakdown: {
        reviews: number;
        visibility: number;
        seo: number;
        photos: number;
        posts: number;
        nap: number;
    };
    weights: {
        reviews: number;
        visibility: number;
        seo: number;
        photos: number;
        posts: number;
        nap: number;
    };
}
export interface Insight {
    id: string;
    type: 'strength' | 'weakness' | 'opportunity' | 'threat';
    category: 'seo' | 'reviews' | 'photos' | 'competitive' | 'technical';
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    confidence: number;
    actionable: boolean;
    relatedMetrics: string[];
}
export interface Recommendation {
    id: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    category: 'seo' | 'reviews' | 'photos' | 'competitive' | 'technical';
    title: string;
    description: string;
    actionItems: Array<{
        task: string;
        effort: 'low' | 'medium' | 'high';
        timeline: string;
        impact: number;
    }>;
    expectedImpact: {
        scoreIncrease: number;
        timeframe: string;
        confidence: number;
    };
    resources: Array<{
        type: 'guide' | 'tool' | 'template';
        title: string;
        url?: string;
        description: string;
    }>;
}
export interface AnalyzerResponse {
    success: boolean;
    data: {
        analysis: AnalysisResult;
        scores: ScoreBreakdown;
        insights: Insight[];
        recommendations: Recommendation[];
        timestamp: string;
    };
}
export interface BusinessData {
    businessName: string;
    address?: string;
    phone?: string;
    website?: string;
    reviews?: any[];
    photos?: any[];
    posts?: any[];
    rankings?: any[];
    seoFactors?: any;
    citations?: any[];
}
export default class AnalyzerClient {
    private baseUrl;
    private timeout;
    constructor();
    analyzeBusinessProfile(businessData: BusinessData, competitorData?: any[]): Promise<AnalyzerResponse>;
    calculateScore(analysisData: any): Promise<ScoreBreakdown>;
    private makeRequest;
    private getFallbackAnalysis;
    private getFallbackScore;
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=AnalyzerClient.d.ts.map