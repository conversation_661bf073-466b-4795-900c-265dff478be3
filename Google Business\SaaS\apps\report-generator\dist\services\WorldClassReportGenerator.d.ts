import { VerificationResult } from './BusinessDataVerifier';
interface BusinessData {
    businessName: string;
    address?: string;
    phone?: string;
    website?: string;
    category?: string;
    description?: string;
    reviews?: any[];
    photos?: any[];
    posts?: any[];
    rankings?: any[];
    seoFactors?: any;
    citations?: any[];
}
interface AnalysisData {
    analysis: any;
    scores: {
        overall: number;
        grade: string;
        breakdown: {
            reviews: number;
            visibility: number;
            seo: number;
            photos: number;
            posts: number;
            nap: number;
        };
    };
    insights: any[];
    recommendations: any[];
    timestamp: string;
}
interface ReportData {
    businessData: BusinessData;
    analysisData: AnalysisData;
    options?: {
        template?: string;
        branding?: {
            companyName?: string;
            logo?: string;
            primaryColor?: string;
            secondaryColor?: string;
        };
        includeAppendix?: boolean;
        includeTemplates?: boolean;
    };
}
export declare class WorldClassReportGenerator {
    private readonly verifier;
    private readonly competitorService;
    private readonly templates;
    generateWorldClassReport(data: ReportData): Promise<{
        success: boolean;
        reportId: string;
        htmlContent: string;
        size: number;
        generationTime: number;
        verification?: VerificationResult;
        dataReliabilityWarnings?: string[];
    }>;
    private buildCompleteReport;
    private generateCoverPage;
    private generateExecutiveSummary;
    private generateScorecard;
    private generateReportId;
    private getGradeDescription;
    private getInsightIcon;
    private getInsightPriority;
    private calculateAverageRating;
    private generatePerformanceSummary;
    private getVisibilityRank;
    private getImpactLevel;
    private getScoreStatus;
    private getScoreColor;
    private getStatusText;
    private generateKeywordRanking;
    private generateLocalVisibilityMap;
    private generatePhotoAudit;
    private generateReviewSentiment;
    private generateSEODescription;
    private generateNAPConsistency;
    private generateGeoHeatmap;
    private generateImageAudit;
    private generateReviewAnalysis;
    private generateEngagementPlan;
    private generateTemplates;
    private generateAppendix;
    private assembleFullReport;
    private getReportCSS;
    private analyzeCoverage;
    private analyzePhotoCategories;
    private calculatePhotoQuality;
    private generatePhotoUploadCalendar;
    private getCategoryIcon;
    private formatCategoryName;
    private analyzeSentiment;
    private extractKeywords;
    private calculateResponseMetrics;
    private generateStars;
    private enhanceRecommendations;
    private getCurrentMetric;
    private getTargetMetric;
    private getImplementationSteps;
    private getTimeline;
    private getEffortLevel;
    private getExpectedResults;
    private getROIEstimate;
    private createPriorityMatrix;
    private create30DayPlan;
    private generatePostSuggestions;
    private createReviewStrategy;
    private getPostTypeIcon;
    private generateTemplateLibrary;
    private getRealKeywordData;
    private detectBusinessType;
    private getKeywordsByBusinessType;
    private getRealCompetitors;
    private calculateKeywordOpportunity;
    private getRankClass;
    private getQualityClass;
    private getConsistencyClass;
    private generateOptimizedDescription;
    private extractCityFromAddress;
    private extractLocationFromBusiness;
    private parseRealLocationFromAddress;
    private generateCompetitorLocations;
    private generateGoogleMapsUrl;
    private generatePhotoMetadata;
    private generateReviewExport;
    private generateNAPConsistencyData;
    private calculateRecommendationImpact;
    private generatePhoneVariation;
    private calculateDataQuality;
    private generateDataWarnings;
    private validateBusinessDataIntegrity;
    private generateDataReliabilitySection;
    private getConfidenceClass;
    private analyzeRealReviewSentiment;
    private selectRepresentativeReviews;
    private extractKeywordsFromReviews;
    private generateNoReviewsAvailableSection;
}
export {};
//# sourceMappingURL=WorldClassReportGenerator.d.ts.map