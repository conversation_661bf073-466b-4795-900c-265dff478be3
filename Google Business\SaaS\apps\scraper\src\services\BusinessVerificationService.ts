import axios from 'axios';
import { GMBData } from './GMBScrapingService';

// Simple logger for Phase 2
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.debug(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

export interface BusinessVerificationRequest {
  businessName: string;
  address: string;
  phone?: string;
  website?: string;
  category?: string;
}

export interface VerificationResult {
  isVerified: boolean;
  confidence: number; // 0-1 scale
  sources: string[];
  issues: string[];
  verifiedData: Partial<GMBData>;
  methodology: string;
  lastVerified: Date;
}

export interface DirectoryVerification {
  directoryName: string;
  found: boolean;
  businessName?: string;
  address?: string;
  phone?: string;
  website?: string;
  rating?: number;
  reviewCount?: number;
  consistency: {
    nameMatch: boolean;
    addressMatch: boolean;
    phoneMatch: boolean;
  };
}

export class BusinessVerificationService {
  private perplexityApiKey: string;
  private googleMapsApiKey: string;

  constructor() {
    this.perplexityApiKey = process.env.PERPLEXITY_API_KEY || '';
    this.googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || '';
    
    if (!this.perplexityApiKey) {
      logger.warn('Perplexity API key not found. Live verification will be limited.');
    }
    if (!this.googleMapsApiKey) {
      logger.warn('Google Maps API key not found. Geocoding will be limited.');
    }
  }

  /**
   * Verify business information using multiple real-time sources
   */
  async verifyBusiness(request: BusinessVerificationRequest): Promise<VerificationResult> {
    logger.info('Starting business verification', { businessName: request.businessName });

    const sources: string[] = [];
    const issues: string[] = [];
    let verifiedData: Partial<GMBData> = {};
    let totalConfidence = 0;
    let verificationCount = 0;

    try {
      // 1. Verify with Perplexity Sonar live web search
      if (this.perplexityApiKey) {
        const perplexityResult = await this.verifyWithPerplexity(request);
        if (perplexityResult.found) {
          sources.push('Perplexity Sonar Live Search');
          verifiedData = { ...verifiedData, ...perplexityResult.data };
          totalConfidence += perplexityResult.confidence;
          verificationCount++;
        } else {
          issues.push('Business not found in live web search');
        }
      }

      // 2. Verify with Google Places API
      if (this.googleMapsApiKey) {
        const placesResult = await this.verifyWithGooglePlaces(request);
        if (placesResult.found) {
          sources.push('Google Places API');
          verifiedData = { ...verifiedData, ...placesResult.data };
          totalConfidence += placesResult.confidence;
          verificationCount++;
        } else {
          issues.push('Business not found in Google Places');
        }
      }

      // 3. Verify business hours and contact information
      const contactResult = await this.verifyContactInformation(request);
      if (contactResult.verified) {
        sources.push('Contact Information Verification');
        totalConfidence += contactResult.confidence;
        verificationCount++;
      } else {
        issues.push(...contactResult.issues);
      }

      // 4. Cross-reference with online directories
      const directoryResults = await this.verifyWithDirectories(request);
      if (directoryResults.length > 0) {
        sources.push(`${directoryResults.length} Online Directories`);
        totalConfidence += this.calculateDirectoryConfidence(directoryResults);
        verificationCount++;
      }

      // Calculate overall confidence
      const overallConfidence = verificationCount > 0 ? totalConfidence / verificationCount : 0;
      const isVerified = overallConfidence >= 0.7 && issues.length < 3;

      const result: VerificationResult = {
        isVerified,
        confidence: Math.round(overallConfidence * 100) / 100,
        sources,
        issues,
        verifiedData,
        methodology: this.getVerificationMethodology(),
        lastVerified: new Date()
      };

      logger.info('Business verification completed', { 
        businessName: request.businessName,
        isVerified,
        confidence: result.confidence,
        sources: sources.length
      });

      return result;

    } catch (error: any) {
      logger.error('Business verification failed', { error: error.message });
      return {
        isVerified: false,
        confidence: 0,
        sources: [],
        issues: [`Verification failed: ${error.message}`],
        verifiedData: {},
        methodology: this.getVerificationMethodology(),
        lastVerified: new Date()
      };
    }
  }

  /**
   * Verify business using Perplexity Sonar live web search
   */
  private async verifyWithPerplexity(request: BusinessVerificationRequest): Promise<{
    found: boolean;
    confidence: number;
    data: Partial<GMBData>;
  }> {
    try {
      const searchQuery = `Verify business information for "${request.businessName}" at "${request.address}". Provide current business hours, phone number, website, and confirm if this business actually exists.`;

      const response = await axios.post('https://api.perplexity.ai/chat/completions', {
        model: 'llama-3.1-sonar-small-128k-online',
        messages: [
          {
            role: 'system',
            content: 'You are a business verification assistant. Provide accurate, real-time business information. If a business does not exist or information cannot be verified, clearly state this. Never fabricate information.'
          },
          {
            role: 'user',
            content: searchQuery
          }
        ],
        max_tokens: 1500,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${this.perplexityApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        return { found: false, confidence: 0, data: {} };
      }

      // Parse the verification response
      const verification = this.parsePerplexityVerification(content, request);
      return verification;

    } catch (error: any) {
      logger.error('Perplexity verification failed', { error: error.message });
      return { found: false, confidence: 0, data: {} };
    }
  }

  /**
   * Parse Perplexity verification response
   */
  private parsePerplexityVerification(content: string, request: BusinessVerificationRequest): {
    found: boolean;
    confidence: number;
    data: Partial<GMBData>;
  } {
    const lowerContent = content.toLowerCase();
    
    // Check if business exists
    const existsIndicators = ['exists', 'found', 'located', 'operating', 'open'];
    const notExistsIndicators = ['does not exist', 'not found', 'closed permanently', 'no longer operating'];
    
    const existsScore = existsIndicators.reduce((score, indicator) => 
      lowerContent.includes(indicator) ? score + 1 : score, 0);
    const notExistsScore = notExistsIndicators.reduce((score, indicator) => 
      lowerContent.includes(indicator) ? score + 1 : score, 0);

    if (notExistsScore > existsScore) {
      return { found: false, confidence: 0, data: {} };
    }

    // Extract verified information
    const data: Partial<GMBData> = {
      businessName: request.businessName,
      address: request.address
    };

    // Extract phone number
    const phoneMatch = content.match(/(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/);
    if (phoneMatch) {
      data.phone = phoneMatch[1];
    }

    // Extract website
    const websiteMatch = content.match(/(https?:\/\/[^\s]+)/);
    if (websiteMatch) {
      data.website = websiteMatch[1];
    }

    // Extract hours
    const hoursMatch = content.match(/hours?:?\s*([^.]+)/i);
    if (hoursMatch) {
      data.businessHours = hoursMatch[1].trim();
    }

    // Calculate confidence based on information found
    let confidence = 0.5; // Base confidence for existence
    if (data.phone) confidence += 0.2;
    if (data.website) confidence += 0.2;
    if (data.businessHours) confidence += 0.1;

    return {
      found: true,
      confidence: Math.min(confidence, 1.0),
      data
    };
  }

  /**
   * Verify business with Google Places API
   */
  private async verifyWithGooglePlaces(request: BusinessVerificationRequest): Promise<{
    found: boolean;
    confidence: number;
    data: Partial<GMBData>;
  }> {
    try {
      // First, try to find the business by name and location
      const searchQuery = `${request.businessName} ${request.address}`;
      const url = 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json';
      
      const response = await axios.get(url, {
        params: {
          input: searchQuery,
          inputtype: 'textquery',
          fields: 'place_id,name,formatted_address,rating,user_ratings_total,business_status',
          key: this.googleMapsApiKey
        }
      });

      if (response.data.status !== 'OK' || !response.data.candidates.length) {
        return { found: false, confidence: 0, data: {} };
      }

      const place = response.data.candidates[0];
      
      // Check if business is operational
      if (place.business_status === 'CLOSED_PERMANENTLY') {
        return { found: false, confidence: 0, data: {} };
      }

      const data: Partial<GMBData> = {
        businessName: place.name,
        address: place.formatted_address,
        rating: place.rating || 0,
        reviewCount: place.user_ratings_total || 0,
        placeId: place.place_id,
        isVerified: true,
        dataSource: 'Google Places API'
      };

      // Calculate confidence based on name similarity
      const nameSimilarity = this.calculateStringSimilarity(
        request.businessName.toLowerCase(),
        place.name.toLowerCase()
      );

      return {
        found: true,
        confidence: nameSimilarity,
        data
      };

    } catch (error: any) {
      logger.error('Google Places verification failed', { error: error.message });
      return { found: false, confidence: 0, data: {} };
    }
  }

  /**
   * Verify contact information
   */
  private async verifyContactInformation(request: BusinessVerificationRequest): Promise<{
    verified: boolean;
    confidence: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let confidence = 0.8; // Base confidence

    // Validate phone number format
    if (request.phone) {
      const phoneRegex = /^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/;
      if (!phoneRegex.test(request.phone)) {
        issues.push('Phone number format appears invalid');
        confidence -= 0.2;
      }
      
      // Check for placeholder numbers
      if (request.phone.includes('123456') || request.phone.includes('000000')) {
        issues.push('Phone number appears to be a placeholder');
        confidence -= 0.3;
      }
    }

    // Validate website
    if (request.website) {
      try {
        new URL(request.website);
      } catch {
        issues.push('Website URL format appears invalid');
        confidence -= 0.2;
      }
    }

    return {
      verified: issues.length === 0,
      confidence: Math.max(confidence, 0),
      issues
    };
  }

  /**
   * Verify with online directories
   */
  private async verifyWithDirectories(request: BusinessVerificationRequest): Promise<DirectoryVerification[]> {
    // This would integrate with various directory APIs
    // For now, return placeholder indicating real integration needed
    return [{
      directoryName: 'Real Directory Integration Required',
      found: false,
      consistency: {
        nameMatch: false,
        addressMatch: false,
        phoneMatch: false
      }
    }];
  }

  /**
   * Calculate directory confidence score
   */
  private calculateDirectoryConfidence(results: DirectoryVerification[]): number {
    if (results.length === 0) return 0;
    
    const foundCount = results.filter(r => r.found).length;
    return foundCount / results.length;
  }

  /**
   * Calculate string similarity
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.calculateLevenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance
   */
  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Get verification methodology description
   */
  private getVerificationMethodology(): string {
    return `Multi-source verification using: ${this.perplexityApiKey ? 'Perplexity Sonar live web search, ' : ''}${this.googleMapsApiKey ? 'Google Places API, ' : ''}contact information validation, and directory cross-referencing. Confidence scoring based on data consistency across sources.`;
  }
}
