export interface ChartData {
    type: string;
    data: any;
    options?: any;
}
export interface VisualizationRequest {
    chartType: string;
    data: any;
    options?: any;
}
export interface VisualizationResponse {
    chartId: string;
    chartType: string;
    data: ChartData;
    timestamp: string;
    status: string;
}
export declare class SimpleVisualizationEngine {
    private chartsCache;
    generateChart(request: VisualizationRequest): Promise<VisualizationResponse>;
    generateMultipleCharts(requests: VisualizationRequest[]): Promise<VisualizationResponse[]>;
    private generateScoreGauge;
    private generateBreakdownChart;
    private generateCompetitiveChart;
    private generateInsightsMatrix;
    private getScoreColor;
    getCachedChart(chartId: string): ChartData | undefined;
    clearCache(): void;
    getCacheStats(): {
        size: number;
        charts: string[];
    };
}
export default SimpleVisualizationEngine;
//# sourceMappingURL=SimpleVisualizationEngine.d.ts.map