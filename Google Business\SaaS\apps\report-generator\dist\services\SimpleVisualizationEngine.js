"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleVisualizationEngine = void 0;
const uuid_1 = require("uuid");
class SimpleVisualizationEngine {
    chartsCache = new Map();
    async generateChart(request) {
        const chartId = (0, uuid_1.v4)();
        try {
            let chartData;
            switch (request.chartType) {
                case 'score-gauge':
                    chartData = this.generateScoreGauge(request.data);
                    break;
                case 'breakdown-chart':
                    chartData = this.generateBreakdownChart(request.data);
                    break;
                case 'competitive-comparison':
                    chartData = this.generateCompetitiveChart(request.data);
                    break;
                case 'insights-matrix':
                    chartData = this.generateInsightsMatrix(request.data);
                    break;
                default:
                    throw new Error(`Unsupported chart type: ${request.chartType}`);
            }
            this.chartsCache.set(chartId, chartData);
            return {
                chartId,
                chartType: request.chartType,
                data: chartData,
                timestamp: new Date().toISOString(),
                status: 'generated'
            };
        }
        catch (error) {
            throw new Error(`Failed to generate chart: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async generateMultipleCharts(requests) {
        const results = [];
        for (const request of requests) {
            try {
                const result = await this.generateChart(request);
                results.push(result);
            }
            catch (error) {
                console.error(`Failed to generate chart ${request.chartType}:`, error instanceof Error ? error.message : String(error));
            }
        }
        return results;
    }
    generateScoreGauge(data) {
        const score = data.overallScore || 0;
        const grade = data.grade || 'F';
        return {
            type: 'gauge',
            data: {
                score,
                grade,
                color: this.getScoreColor(score),
                segments: [
                    { min: 0, max: 20, color: '#ef4444', label: 'Poor' },
                    { min: 20, max: 40, color: '#f97316', label: 'Fair' },
                    { min: 40, max: 60, color: '#eab308', label: 'Good' },
                    { min: 60, max: 80, color: '#22c55e', label: 'Very Good' },
                    { min: 80, max: 100, color: '#16a34a', label: 'Excellent' }
                ]
            },
            options: {
                title: 'Overall GMB Score',
                subtitle: `Grade: ${grade}`,
                size: { width: 400, height: 300 }
            }
        };
    }
    generateBreakdownChart(data) {
        const categories = data.breakdown || {};
        return {
            type: 'bar',
            data: {
                labels: Object.keys(categories),
                datasets: [{
                        label: 'Category Scores',
                        data: Object.values(categories),
                        backgroundColor: [
                            '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'
                        ]
                    }]
            },
            options: {
                title: 'Score Breakdown by Category',
                scales: {
                    y: { min: 0, max: 100 }
                }
            }
        };
    }
    generateCompetitiveChart(data) {
        const business = data.business || 0;
        const competitors = data.competitors || [];
        const labels = ['Your Business', ...competitors.map((c) => c.name)];
        const scores = [business, ...competitors.map((c) => c.score)];
        return {
            type: 'radar',
            data: {
                labels,
                datasets: [{
                        label: 'Competitive Analysis',
                        data: scores,
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: '#3b82f6',
                        pointBackgroundColor: '#3b82f6'
                    }]
            },
            options: {
                title: 'Competitive Position Analysis',
                scales: {
                    r: { min: 0, max: 100 }
                }
            }
        };
    }
    generateInsightsMatrix(data) {
        const insights = data.insights || [];
        const categories = ['strength', 'weakness', 'opportunity', 'threat'];
        const categoryCounts = categories.map(category => insights.filter((insight) => insight.type === category).length);
        return {
            type: 'doughnut',
            data: {
                labels: ['Strengths', 'Weaknesses', 'Opportunities', 'Threats'],
                datasets: [{
                        data: categoryCounts,
                        backgroundColor: ['#10b981', '#ef4444', '#3b82f6', '#f59e0b']
                    }]
            },
            options: {
                title: 'Insights Distribution',
                legend: { position: 'bottom' }
            }
        };
    }
    getScoreColor(score) {
        if (score >= 80)
            return '#16a34a';
        if (score >= 60)
            return '#22c55e';
        if (score >= 40)
            return '#eab308';
        if (score >= 20)
            return '#f97316';
        return '#ef4444';
    }
    getCachedChart(chartId) {
        return this.chartsCache.get(chartId);
    }
    clearCache() {
        this.chartsCache.clear();
    }
    getCacheStats() {
        return {
            size: this.chartsCache.size,
            charts: Array.from(this.chartsCache.keys())
        };
    }
}
exports.SimpleVisualizationEngine = SimpleVisualizationEngine;
exports.default = SimpleVisualizationEngine;
//# sourceMappingURL=SimpleVisualizationEngine.js.map