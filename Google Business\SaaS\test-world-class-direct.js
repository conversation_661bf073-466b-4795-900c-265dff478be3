const { WorldClassReportGenerator } = require('./apps/report-generator/dist/services/WorldClassReportGenerator');
const fs = require('fs');
const path = require('path');

console.log('🏆 Testing World-Class GMB Audit Report Generation (Direct)');

// Enhanced sample data for Raga Dental Implants
const worldClassReportData = {
  businessData: {
    businessName: "Raga Dental Implants and Laser",
    address: "123 Main Street, Thanjavur, Tamil Nadu 613001",
    phone: "+91-4362-123456",
    website: "https://ragadental.com",
    category: "Dental Clinic",
    description: "Premier dental care specializing in implants and laser dentistry in Thanjavur",
    
    // Enhanced review data with sentiment analysis
    reviews: [
      {
        author: "<PERSON><PERSON>",
        rating: 5,
        text: "Excellent service! Dr. <PERSON><PERSON> is very professional and the laser treatment was completely painless. Highly recommend!",
        date: "2024-01-15",
        sentiment: "positive"
      },
      {
        author: "<PERSON><PERSON>",
        rating: 4,
        text: "Good dental care. The implant procedure went smoothly. Staff is friendly and helpful.",
        date: "2024-01-10",
        sentiment: "positive"
      },
      {
        author: "<PERSON><PERSON> <PERSON>",
        rating: 5,
        text: "Amazing experience! The clinic is very clean and modern. Dr. <PERSON><PERSON> explained everything clearly.",
        date: "2024-01-08",
        sentiment: "positive"
      },
      {
        author: "Suresh Babu",
        rating: 3,
        text: "Decent service but had to wait longer than expected. Treatment quality was good though.",
        date: "2024-01-05",
        sentiment: "neutral"
      },
      {
        author: "Lakshmi Narayanan",
        rating: 5,
        text: "Best dental clinic in Thanjavur! The laser dentistry is revolutionary. No pain at all!",
        date: "2024-01-03",
        sentiment: "positive"
      },
      {
        author: "Venkat Raman",
        rating: 2,
        text: "Service was okay but felt rushed. Would have liked more explanation about the procedure.",
        date: "2023-12-28",
        sentiment: "negative"
      },
      {
        author: "Divya Krishnan",
        rating: 5,
        text: "Fantastic dental implant work! Dr. Raga is skilled and caring. Clinic has latest technology.",
        date: "2023-12-25",
        sentiment: "positive"
      },
      {
        author: "Arun Prakash",
        rating: 4,
        text: "Professional service and good results. The laser treatment was innovative and effective.",
        date: "2023-12-20",
        sentiment: "positive"
      },
      {
        author: "Kavitha Murugan",
        rating: 5,
        text: "Excellent cosmetic dentistry! My smile looks perfect now. Thank you Dr. Raga and team!",
        date: "2023-12-18",
        sentiment: "positive"
      },
      {
        author: "Ganesh Subramanian",
        rating: 4,
        text: "Good experience overall. Modern equipment and clean facility. Reasonable pricing too.",
        date: "2023-12-15",
        sentiment: "positive"
      }
    ],
    
    // Categorized photo data with quality scores
    photos: [
      { type: "exterior", quality: 8, date: "2024-01-10", description: "Clinic exterior view" },
      { type: "interior", quality: 9, date: "2024-01-08", description: "Reception area" },
      { type: "interior", quality: 7, date: "2024-01-08", description: "Waiting room" },
      { type: "equipment", quality: 9, date: "2024-01-05", description: "Laser dentistry equipment" },
      { type: "equipment", quality: 8, date: "2024-01-05", description: "Digital X-ray machine" },
      { type: "team", quality: 8, date: "2024-01-03", description: "Dr. Raga with team" },
      { type: "services", quality: 9, date: "2024-01-01", description: "Dental implant procedure" },
      { type: "services", quality: 7, date: "2023-12-28", description: "Teeth cleaning session" }
    ],
    
    // Enhanced posts data with engagement metrics
    posts: [
      {
        title: "New Laser Technology",
        content: "Introducing advanced laser dentistry for painless treatments!",
        date: "2024-01-12",
        engagement: { views: 245, likes: 18, shares: 3 }
      },
      {
        title: "Dental Health Tips",
        content: "5 essential tips for maintaining healthy teeth during winter season",
        date: "2024-01-05",
        engagement: { views: 189, likes: 12, shares: 2 }
      },
      {
        title: "Patient Success Story",
        content: "Amazing smile transformation with our dental implant services!",
        date: "2023-12-28",
        engagement: { views: 312, likes: 24, shares: 5 }
      },
      {
        title: "Holiday Hours",
        content: "Special holiday hours for emergency dental care during New Year",
        date: "2023-12-25",
        engagement: { views: 156, likes: 8, shares: 1 }
      },
      {
        title: "Team Training",
        content: "Our team completed advanced laser dentistry certification program",
        date: "2023-12-20",
        engagement: { views: 203, likes: 15, shares: 2 }
      }
    ]
  },
  
  // AI Analysis data structure matching Phase 3 output
  analysisData: {
    scores: {
      overall: 45,
      grade: 'F',
      breakdown: {
        reviews: 65,
        visibility: 0,
        seo: 100,
        photos: 24,
        posts: 0,
        nap: 50
      }
    },
    
    insights: [
      {
        category: 'competitive',
        type: 'weakness',
        title: 'Review Volume Significantly Below Competitors',
        description: 'You have fewer reviews than most competitors, impacting trust and visibility.',
        impact: 'high',
        priority: 'high'
      },
      {
        category: 'technical',
        type: 'weakness', 
        title: 'Poor Overall Performance',
        description: 'Your GMB profile needs significant improvement with a score of 45/100.',
        impact: 'high',
        priority: 'critical'
      },
      {
        category: 'seo',
        type: 'weakness',
        title: 'Business Description Needs Improvement', 
        description: 'Your business description lacks keyword optimization and could be more compelling.',
        impact: 'high',
        priority: 'high'
      },
      {
        category: 'seo',
        type: 'strength',
        title: 'Strong SEO Foundation',
        description: 'Your Google Business Profile has excellent SEO optimization.',
        impact: 'high',
        priority: 'low'
      },
      {
        category: 'competitive',
        type: 'strength', 
        title: 'Strong Market Position',
        description: 'You rank among the top competitors in your local market.',
        impact: 'high',
        priority: 'low'
      }
    ],
    
    recommendations: [
      {
        title: 'Enhance Local Visibility',
        recommendation: 'Improve your local search rankings and online presence.',
        priority: 'CRITICAL',
        impact: { score: 9, description: 'High impact on local search visibility' }
      },
      {
        title: 'Improve Photo Strategy', 
        recommendation: 'Add more high-quality photos to showcase your business.',
        priority: 'CRITICAL',
        impact: { score: 8, description: 'Significant improvement in profile engagement' }
      },
      {
        title: 'Increase Google Posts Activity',
        recommendation: 'Regular posting keeps your profile active and engaging.',
        priority: 'CRITICAL', 
        impact: { score: 7, description: 'Better engagement and visibility' }
      },
      {
        title: 'Implement Review Generation Strategy',
        recommendation: 'Increase your review volume and improve overall rating through systematic review requests.',
        priority: 'HIGH',
        impact: { score: 9, description: 'Builds trust and improves local rankings' }
      }
    ]
  },
  
  options: {
    includeTemplates: true,
    includeAppendix: true,
    reportType: 'comprehensive'
  }
};

async function testWorldClassReport() {
  try {
    console.log('\n🎨 Generating World-Class GMB Audit Report (Direct)...\n');
    
    const startTime = Date.now();
    const generator = new WorldClassReportGenerator();
    
    const result = await generator.generateWorldClassReport(worldClassReportData);
    const generationTime = Date.now() - startTime;
    
    if (result.success) {
      console.log('✅ World-Class Report Generated Successfully!\n');
      
      // Save the report
      const reportsDir = path.join(__dirname, 'reports');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }
      
      const filename = `raga-dental-world-class-direct-${Date.now()}.html`;
      const filepath = path.join(reportsDir, filename);
      fs.writeFileSync(filepath, result.htmlContent);
      
      console.log('📊 ENHANCED ANALYSIS SUMMARY:');
      console.log(`   Overall Score: ${worldClassReportData.analysisData.scores.overall}/100 (Grade: ${worldClassReportData.analysisData.scores.grade})`);
      console.log(`   Generation Time: ${generationTime}ms`);
      console.log(`   Report ID: ${result.reportId}`);
      console.log(`   Report Size: ${result.size} bytes`);
      console.log(`   💾 Local HTML Report: ${filepath}\n`);
      
      // Check if all advanced sections are included
      const htmlContent = result.htmlContent;
      const sectionsCheck = {
        'Geographic Heatmap': htmlContent.includes('📍 Geographic Analysis & Market Coverage') && htmlContent.includes('google-maps-container'),
        'Photo Quality Audit': htmlContent.includes('📸 Photo Quality & Presence Audit') && htmlContent.includes('photo-audit-container'),
        'Review Sentiment Analysis': htmlContent.includes('💬 Review Sentiment Analysis') && htmlContent.includes('Sentiment Distribution'),
        'Keyword Ranking Analysis': htmlContent.includes('🔍 Keyword Ranking Analysis') && htmlContent.includes('keyword-table'),
        '30-Day Engagement Plan': htmlContent.includes('📅 30-Day Engagement Action Plan') && htmlContent.includes('Week-by-Week Action Plan'),
        'Technical Appendix': htmlContent.includes('🧾 Technical Appendix & Raw Data') && htmlContent.includes('Photo Metadata Analysis')
      };
      
      console.log('🏆 ADVANCED SECTIONS VERIFICATION:');
      Object.entries(sectionsCheck).forEach(([section, included]) => {
        console.log(`   ${included ? '✅' : '❌'} ${section}`);
      });
      
      const allSectionsIncluded = Object.values(sectionsCheck).every(Boolean);
      console.log(`\n🎯 IMPLEMENTATION STATUS: ${allSectionsIncluded ? '✅ COMPLETE' : '⚠️ PARTIAL'}`);
      
      if (allSectionsIncluded) {
        console.log('\n🚀 ALL 6 DIAGNOSTIC SECTIONS SUCCESSFULLY IMPLEMENTED!');
        console.log('   📊 Geographic heatmap with Google Maps integration');
        console.log('   📸 Photo quality audit with metadata analysis');
        console.log('   💬 Review sentiment analysis with export data');
        console.log('   🔍 Keyword ranking analysis with competitor tracking');
        console.log('   📅 30-day engagement plan with actionable tasks');
        console.log('   🧾 Technical appendix with comprehensive raw data');
      }
      
    } else {
      console.error('❌ Report generation failed');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testWorldClassReport();
