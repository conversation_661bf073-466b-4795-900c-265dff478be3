import { GMBData } from './GMBScrapingService';
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    cleanedData: Partial<GMBData>;
    confidence: number;
}
export interface DataQualityMetrics {
    completeness: number;
    accuracy: number;
    consistency: number;
    freshness: number;
    overall: number;
}
export declare class DataValidationService {
    private gmbDataSchema;
    constructor();
    private initializeSchemas;
    validateAndClean(data: Partial<GMBData>): Promise<ValidationResult>;
    private cleanData;
    private cleanBusinessName;
    private cleanAddress;
    private cleanPhoneNumber;
    private cleanWebsiteUrl;
    private cleanRating;
    private cleanReviewCount;
    private cleanCoordinates;
    private performCustomValidation;
    private isValidAddressFormat;
    private isValidPhoneFormat;
    private isValidWebsiteFormat;
    private calculateConfidence;
    private calculateCompleteness;
    calculateDataQuality(data: Partial<GMBData>, validationResult: ValidationResult): DataQualityMetrics;
    private calculateConsistency;
    private calculateFreshness;
}
//# sourceMappingURL=DataValidationService.d.ts.map