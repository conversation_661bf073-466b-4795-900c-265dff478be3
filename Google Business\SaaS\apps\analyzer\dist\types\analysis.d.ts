export interface AnalysisResult {
    seoAnalysis: SEOAnalysis;
    sentimentAnalysis: SentimentAnalysis;
    competitiveAnalysis: CompetitiveAnalysis;
    photoAnalysis: PhotoAnalysis;
    overallHealth: number;
    timestamp: string;
}
export interface SEOAnalysis {
    score: number;
    factors: {
        businessName: {
            score: number;
            issues: string[];
            recommendations: string[];
        };
        description: {
            score: number;
            keywordDensity: number;
            readability: number;
            issues: string[];
            recommendations: string[];
        };
        categories: {
            score: number;
            primary: string;
            secondary: string[];
            relevance: number;
            issues: string[];
            recommendations: string[];
        };
        hours: {
            score: number;
            completeness: number;
            accuracy: number;
            issues: string[];
            recommendations: string[];
        };
        contact: {
            score: number;
            napConsistency: number;
            phoneFormat: number;
            websiteQuality: number;
            issues: string[];
            recommendations: string[];
        };
    };
    keywordAnalysis: {
        primary: string[];
        secondary: string[];
        missing: string[];
        density: Record<string, number>;
    };
}
export interface SentimentAnalysis {
    overall: {
        score: number;
        sentiment: 'positive' | 'neutral' | 'negative';
        confidence: number;
    };
    breakdown: {
        positive: number;
        neutral: number;
        negative: number;
    };
    trends: {
        recent: number;
        historical: number;
        trajectory: 'improving' | 'stable' | 'declining';
    };
    themes: {
        positive: Array<{
            theme: string;
            frequency: number;
            impact: number;
        }>;
        negative: Array<{
            theme: string;
            frequency: number;
            impact: number;
        }>;
    };
    responseAnalysis: {
        responseRate: number;
        averageResponseTime: number;
        responseQuality: number;
        recommendations: string[];
    };
}
export interface CompetitiveAnalysis {
    position: {
        rank: number;
        totalCompetitors: number;
        marketShare: number;
    };
    benchmarks: {
        rating: {
            business: number;
            average: number;
            percentile: number;
        };
        reviews: {
            business: number;
            average: number;
            percentile: number;
        };
        photos: {
            business: number;
            average: number;
            percentile: number;
        };
        posts: {
            business: number;
            average: number;
            percentile: number;
        };
    };
    gaps: Array<{
        area: string;
        impact: 'high' | 'medium' | 'low';
        description: string;
        recommendation: string;
    }>;
    opportunities: Array<{
        type: string;
        potential: number;
        effort: 'low' | 'medium' | 'high';
        description: string;
        actionItems: string[];
    }>;
}
export interface PhotoAnalysis {
    score: number;
    quantity: {
        total: number;
        recommended: number;
        gap: number;
    };
    quality: {
        average: number;
        issues: string[];
        recommendations: string[];
    };
    categories: {
        exterior: number;
        interior: number;
        products: number;
        team: number;
        logo: number;
        missing: string[];
    };
    optimization: {
        geotagged: number;
        highResolution: number;
        properNaming: number;
        recommendations: string[];
    };
}
export interface ScoreBreakdown {
    overall: number;
    breakdown: {
        reviews: number;
        visibility: number;
        seo: number;
        photos: number;
        posts: number;
        nap: number;
    };
    weights: {
        reviews: number;
        visibility: number;
        seo: number;
        photos: number;
        posts: number;
        nap: number;
    };
    grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D+' | 'D' | 'F';
}
export interface Insight {
    id: string;
    type: 'strength' | 'weakness' | 'opportunity' | 'threat';
    category: 'seo' | 'reviews' | 'photos' | 'competitive' | 'technical';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    confidence: number;
    data: any;
    recommendations: string[];
}
export interface Recommendation {
    id: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    category: 'seo' | 'reviews' | 'photos' | 'competitive' | 'technical';
    title: string;
    description: string;
    actionItems: Array<{
        task: string;
        effort: 'low' | 'medium' | 'high';
        timeline: string;
        impact: number;
    }>;
    expectedImpact: {
        scoreIncrease: number;
        timeframe: string;
        confidence: number;
    };
    resources: Array<{
        type: 'guide' | 'tool' | 'template';
        title: string;
        url?: string;
        description: string;
    }>;
}
export interface AnalysisData {
    reviews: any[];
    rankings: any[];
    seoFactors: any;
    photos: any[];
    posts: any[];
    citations: any[];
    competitors: any[];
}
//# sourceMappingURL=analysis.d.ts.map