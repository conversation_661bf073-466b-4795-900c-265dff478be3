import { AnalysisResult, ScoreBreakdown, Insight, Recommendation } from '../types/analysis';
export declare class RecommendationEngine {
    generateRecommendations(analysis: AnalysisResult, scores: ScoreBreakdown, insights: Insight[]): Promise<Recommendation[]>;
    private generateScoreBasedRecommendations;
    private generateInsightBasedRecommendations;
    private generateSEORecommendations;
    private generateReviewRecommendations;
    private generatePhotoRecommendations;
    private createAreaRecommendation;
    private getResourcesForCategory;
    private getPriorityWeight;
}
//# sourceMappingURL=RecommendationEngine.d.ts.map