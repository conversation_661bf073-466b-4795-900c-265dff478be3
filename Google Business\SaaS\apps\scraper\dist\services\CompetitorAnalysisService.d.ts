import { GMBData } from './GMBScrapingService';
export interface CompetitorSearchRequest {
    businessName: string;
    address: string;
    category: string;
    radius?: number;
    maxCompetitors?: number;
    coordinates?: {
        latitude: number;
        longitude: number;
    };
}
export interface CompetitorData extends GMBData {
    competitorId: string;
    distance?: number;
    localRanking?: number;
    competitiveStrength: number;
    threatLevel: 'low' | 'medium' | 'high';
    advantages: string[];
    weaknesses: string[];
}
export interface CompetitorAnalysisResult {
    targetBusiness: {
        name: string;
        address: string;
        category: string;
    };
    competitors: CompetitorData[];
    analysis: {
        totalCompetitors: number;
        averageRating: number;
        averageReviewCount: number;
        marketSaturation: 'low' | 'medium' | 'high';
        competitiveGaps: string[];
        recommendations: string[];
    };
    scrapedAt: Date;
}
export declare class CompetitorAnalysisService {
    private scrapingService;
    private validationService;
    private googlePlacesApiKey;
    private perplexityApiKey;
    constructor();
    analyzeCompetitors(request: CompetitorSearchRequest): Promise<CompetitorAnalysisResult>;
    private findCompetitors;
    private generateSearchQueries;
    private extractLocationFromAddress;
    private searchCompetitors;
    private isSameBusiness;
    private calculateStringSimilarity;
    private calculateLevenshteinDistance;
    private enrichCompetitorData;
    private calculateCompetitiveStrength;
    private assessThreatLevel;
    private analyzeCompetitorStrengthsWeaknesses;
    private analyzeCompetitiveLandscape;
    private identifyCompetitiveGaps;
    private generateRecommendations;
    private searchCompetitorsWithGooglePlaces;
    private searchCompetitorsWithPerplexity;
    private parsePerplexityResponse;
    private mapCategoryToPlaceType;
    private generateSecureId;
    private geocodeAddress;
    close(): Promise<void>;
}
//# sourceMappingURL=CompetitorAnalysisService.d.ts.map