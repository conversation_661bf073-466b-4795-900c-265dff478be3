{"timestamp": "2025-07-31T06:35:02.366Z", "warmup": {"requests": 5, "averageResponseTime": 14, "successRate": "100.0"}, "endpointTests": {"report-generator_GET__health": {"service": "report-generator", "endpoint": "GET /health", "totalRequests": 10, "successfulRequests": 10, "successRate": "100.0", "averageResponseTime": 5, "averageSize": 207, "minResponseTime": 3, "maxResponseTime": 8}, "report-generator_GET__api_docs": {"service": "report-generator", "endpoint": "GET /api/docs", "totalRequests": 10, "successfulRequests": 10, "successRate": "100.0", "averageResponseTime": 2, "averageSize": 627, "minResponseTime": 1, "maxResponseTime": 2}, "analyzer_GET__health": {"service": "analyzer", "endpoint": "GET /health", "totalRequests": 10, "successfulRequests": 10, "successRate": "100.0", "averageResponseTime": 2, "averageSize": 154, "minResponseTime": 1, "maxResponseTime": 4}, "analyzer_GET__api_docs": {"service": "analyzer", "endpoint": "GET /api/docs", "totalRequests": 10, "successfulRequests": 10, "successRate": "100.0", "averageResponseTime": 1, "averageSize": 310, "minResponseTime": 1, "maxResponseTime": 3}, "data-collector_GET__health": {"service": "data-collector", "endpoint": "GET /health", "totalRequests": 10, "successfulRequests": 10, "successRate": "100.0", "averageResponseTime": 2, "averageSize": 153, "minResponseTime": 1, "maxResponseTime": 5}}, "loadTests": {"1_users": {"concurrentUsers": 1, "testDuration": 30011, "totalRequests": 263, "successfulRequests": 263, "failedRequests": 0, "successRate": "100.0", "requestsPerSecond": 9, "averageResponseTime": 6, "minResponseTime": 4, "maxResponseTime": 16}, "5_users": {"concurrentUsers": 5, "testDuration": 30036, "totalRequests": 1303, "successfulRequests": 1303, "failedRequests": 0, "successRate": "100.0", "requestsPerSecond": 43, "averageResponseTime": 9, "minResponseTime": 4, "maxResponseTime": 34}, "10_users": {"concurrentUsers": 10, "testDuration": 30088, "totalRequests": 2628, "successfulRequests": 2628, "failedRequests": 0, "successRate": "100.0", "requestsPerSecond": 87, "averageResponseTime": 10, "minResponseTime": 2, "maxResponseTime": 47}, "20_users": {"concurrentUsers": 20, "testDuration": 30108, "totalRequests": 5261, "successfulRequests": 5261, "failedRequests": 0, "successRate": "100.0", "requestsPerSecond": 175, "averageResponseTime": 11, "minResponseTime": 2, "maxResponseTime": 53}}, "integratedReportTests": {"1_concurrent": {"concurrentReports": 1, "totalTime": 34, "successfulReports": 1, "successRate": "100.0", "averageResponseTime": 34, "averageReportSize": 7617, "reportsPerMinute": 1765}, "3_concurrent": {"concurrentReports": 3, "totalTime": 33, "successfulReports": 3, "successRate": "100.0", "averageResponseTime": 28, "averageReportSize": 7617, "reportsPerMinute": 5455}, "5_concurrent": {"concurrentReports": 5, "totalTime": 54, "successfulReports": 5, "successRate": "100.0", "averageResponseTime": 43, "averageReportSize": 7617, "reportsPerMinute": 5556}}, "summary": {"endpointPerformance": {"averageResponseTime": 2, "averageSuccessRate": "100.0"}, "loadTestPerformance": {"maxConcurrentUsers": 20, "maxRequestsPerSecond": 175, "averageSuccessRateUnderLoad": "100.0"}, "reportGenerationPerformance": {"maxConcurrentReports": 5, "averageReportGenerationTime": 35, "maxReportsPerMinute": 5556}}}