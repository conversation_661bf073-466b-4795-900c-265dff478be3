const http = require('http');

// Quick test for improved report rendering
const testBusinessData = {
  businessName: "Test Restaurant",
  address: "123 Test St, Test City, TC 12345",
  phone: "******-123-4567",
  website: "https://testrestaurant.com"
};

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const responseData = res.headers['content-type']?.includes('application/json') 
            ? JSON.parse(body) 
            : body;
          resolve({ status: res.statusCode, data: responseData });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testReportRendering() {
  console.log('🎨 Testing Improved Report Rendering\n');

  try {
    // Generate integrated report
    console.log('1. Generating integrated report with real analysis...');
    const integratedResponse = await makeRequest({
      hostname: 'localhost',
      port: 3003,
      path: '/api/generate/integrated-report',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      businessData: testBusinessData,
      options: { template: 'default' }
    });

    if (integratedResponse.data.success) {
      const reportId = integratedResponse.data.data.report.reportId;
      console.log(`   Report ID: ${reportId}`);
      console.log(`   Overall Score: ${integratedResponse.data.data.analysis.scores.overall}`);
      
      // Access the report portal to check rendering
      console.log('\n2. Checking report rendering quality...');
      const portalResponse = await makeRequest({
        hostname: 'localhost',
        port: 3003,
        path: `/api/portal/${reportId}`,
        method: 'GET'
      });

      if (typeof portalResponse.data === 'string') {
        const htmlContent = portalResponse.data;
        
        // Check for improved rendering
        const hasObjectErrors = htmlContent.includes('[object Object]');
        const hasActionItems = htmlContent.includes('<ul class="action-items">');
        const hasRecommendations = htmlContent.includes('class="recommendation-item"');
        const hasBusinessName = htmlContent.includes(testBusinessData.businessName);
        
        console.log(`   Contains Business Name: ${hasBusinessName ? '✅' : '❌'}`);
        console.log(`   Has Recommendations Section: ${hasRecommendations ? '✅' : '❌'}`);
        console.log(`   Has Action Items: ${hasActionItems ? '✅' : '❌'}`);
        console.log(`   Object Rendering Errors: ${hasObjectErrors ? '❌ Found' : '✅ None'}`);
        
        if (hasObjectErrors) {
          console.log('\n   ⚠️  Still some object rendering issues detected');
        } else {
          console.log('\n   ✅ Report rendering looks good!');
        }

        // Extract a sample recommendation for inspection
        const recMatch = htmlContent.match(/<li class="recommendation-item">(.*?)<\/li>/s);
        if (recMatch) {
          console.log('\n   Sample Recommendation Preview:');
          const sampleRec = recMatch[1].substring(0, 200) + '...';
          console.log(`   ${sampleRec}`);
        }

      } else {
        console.log(`   ❌ Unexpected response format: ${JSON.stringify(portalResponse.data)}`);
      }

    } else {
      console.log(`   ❌ Failed to generate report: ${JSON.stringify(integratedResponse.data)}`);
    }

    console.log('\n✅ Report rendering test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testReportRendering();
