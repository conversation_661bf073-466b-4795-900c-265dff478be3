import { createLogger } from 'winston';
import { v4 as uuidv4 } from 'uuid';

const logger = createLogger({
  level: 'info',
  format: require('winston').format.combine(
    require('winston').format.timestamp(),
    require('winston').format.json()
  ),
  transports: [
    new (require('winston')).transports.Console()
  ]
});

// Types from Phase 3 Analyzer
export interface AnalysisResult {
  seoAnalysis: SEOAnalysis;
  sentimentAnalysis: SentimentAnalysis;
  competitiveAnalysis: CompetitiveAnalysis;
  photoAnalysis: PhotoAnalysis;
  overallHealth: number;
  timestamp: string;
}

export interface SEOAnalysis {
  onPageFactors: {
    businessName: { score: number; issues: string[]; recommendations: string[] };
    description: { score: number; issues: string[]; recommendations: string[] };
    categories: { score: number; issues: string[]; recommendations: string[] };
    attributes: { score: number; issues: string[]; recommendations: string[] };
    hours: { score: number; issues: string[]; recommendations: string[] };
    website: { score: number; issues: string[]; recommendations: string[] };
    phone: { score: number; issues: string[]; recommendations: string[] };
  };
  localSEOFactors: {
    napConsistency: { score: number; issues: string[]; recommendations: string[] };
    citationQuality: { score: number; issues: string[]; recommendations: string[] };
    reviewSignals: { score: number; issues: string[]; recommendations: string[] };
    proximityFactors: { score: number; issues: string[]; recommendations: string[] };
  };
  technicalFactors: {
    websiteOptimization: { score: number; issues: string[]; recommendations: string[] };
    mobileOptimization: { score: number; issues: string[]; recommendations: string[] };
    pageSpeed: { score: number; issues: string[]; recommendations: string[] };
    structuredData: { score: number; issues: string[]; recommendations: string[] };
  };
  overallScore: number;
  criticalIssues: string[];
  quickWins: string[];
}

export interface SentimentAnalysis {
  overall: {
    score: number;
    sentiment: 'positive' | 'neutral' | 'negative';
    confidence: number;
  };
  breakdown: {
    positive: number;
    neutral: number;
    negative: number;
  };
  trends: {
    recent: number;
    historical: number;
    trajectory: 'improving' | 'stable' | 'declining';
  };
  themes: {
    positive: Array<{ theme: string; frequency: number; impact: number; }>;
    negative: Array<{ theme: string; frequency: number; impact: number; }>;
  };
  responseAnalysis: {
    responseRate: number;
    averageResponseTime: number;
    responseQuality: number;
    recommendations: string[];
  };
}

export interface CompetitiveAnalysis {
  position: {
    rank: number;
    totalCompetitors: number;
    percentile: number;
  };
  strengths: Array<{ factor: string; advantage: number; description: string; }>;
  weaknesses: Array<{ factor: string; gap: number; description: string; }>;
  opportunities: Array<{ factor: string; potential: number; description: string; }>;
  threats: Array<{ factor: string; risk: number; description: string; }>;
  benchmarks: {
    averageRating: number;
    averageReviews: number;
    averagePhotos: number;
    averagePosts: number;
  };
}

export interface PhotoAnalysis {
  quantity: { score: number; current: number; recommended: number; };
  quality: { score: number; issues: string[]; recommendations: string[]; };
  diversity: { score: number; categories: string[]; missing: string[]; };
  recency: { score: number; lastUpdate: string; recommendations: string[]; };
  optimization: { score: number; issues: string[]; recommendations: string[]; };
  overallScore: number;
}

export interface ScoreBreakdown {
  overall: number;
  grade: string;
  breakdown: {
    reviews: number;
    visibility: number;
    seo: number;
    photos: number;
    posts: number;
    nap: number;
  };
  weights: {
    reviews: number;
    visibility: number;
    seo: number;
    photos: number;
    posts: number;
    nap: number;
  };
}

export interface Insight {
  id: string;
  type: 'strength' | 'weakness' | 'opportunity' | 'threat';
  category: 'seo' | 'reviews' | 'photos' | 'competitive' | 'technical';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  actionable: boolean;
  relatedMetrics: string[];
}

export interface Recommendation {
  id: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'seo' | 'reviews' | 'photos' | 'competitive' | 'technical';
  title: string;
  description: string;
  actionItems: Array<{
    task: string;
    effort: 'low' | 'medium' | 'high';
    timeline: string;
    impact: number;
  }>;
  expectedImpact: {
    scoreIncrease: number;
    timeframe: string;
    confidence: number;
  };
  resources: Array<{
    type: 'guide' | 'tool' | 'template';
    title: string;
    url?: string;
    description: string;
  }>;
}

export interface AnalyzerResponse {
  success: boolean;
  data: {
    analysis: AnalysisResult;
    scores: ScoreBreakdown;
    insights: Insight[];
    recommendations: Recommendation[];
    timestamp: string;
  };
}

export interface BusinessData {
  businessName: string;
  address?: string;
  phone?: string;
  website?: string;
  reviews?: any[];
  photos?: any[];
  posts?: any[];
  rankings?: any[];
  seoFactors?: any;
  citations?: any[];
}

export default class AnalyzerClient {
  private baseUrl: string;
  private timeout: number;

  constructor() {
    this.baseUrl = process.env.ANALYZER_SERVICE_URL || 'http://localhost:3002';
    this.timeout = parseInt(process.env.ANALYZER_TIMEOUT || '30000');
  }

  async analyzeBusinessProfile(businessData: BusinessData, competitorData?: any[]): Promise<AnalyzerResponse> {
    try {
      logger.info('Requesting business analysis from analyzer service', { 
        businessName: businessData.businessName,
        hasCompetitors: !!competitorData?.length 
      });

      const response = await this.makeRequest('/api/analyze/business', {
        businessData,
        competitorData: competitorData || []
      });

      if (!response.success) {
        throw new Error(`Analyzer service error: ${response.error?.message || 'Unknown error'}`);
      }

      logger.info('Business analysis completed successfully', {
        overallScore: response.data.scores.overall,
        insightsCount: response.data.insights.length,
        recommendationsCount: response.data.recommendations.length
      });

      return response;

    } catch (error) {
      logger.error('Failed to analyze business profile', { 
        error: error instanceof Error ? error.message : String(error),
        businessName: businessData.businessName 
      });

      // Return fallback analysis if service is unavailable
      return this.getFallbackAnalysis(businessData);
    }
  }

  async calculateScore(analysisData: any): Promise<ScoreBreakdown> {
    try {
      logger.info('Requesting score calculation from analyzer service');

      const response = await this.makeRequest('/api/calculate/score', {
        analysisData
      });

      if (!response.success) {
        throw new Error(`Score calculation error: ${response.error?.message || 'Unknown error'}`);
      }

      return response.data;

    } catch (error) {
      logger.error('Failed to calculate score', { 
        error: error instanceof Error ? error.message : String(error) 
      });

      // Return fallback score
      return this.getFallbackScore();
    }
  }

  private async makeRequest(endpoint: string, data: any): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timeout after ${this.timeout}ms`);
      }
      throw error;
    }
  }

  private getFallbackAnalysis(businessData: BusinessData): AnalyzerResponse {
    logger.warn('Using fallback analysis due to analyzer service unavailability');

    const fallbackAnalysis: AnalysisResult = {
      seoAnalysis: {
        onPageFactors: {
          businessName: { score: 85, issues: [], recommendations: ['Optimize business name for local keywords'] },
          description: { score: 70, issues: ['Description could be more detailed'], recommendations: ['Add more descriptive content'] },
          categories: { score: 90, issues: [], recommendations: [] },
          attributes: { score: 75, issues: [], recommendations: ['Add more business attributes'] },
          hours: { score: 95, issues: [], recommendations: [] },
          website: { score: 80, issues: [], recommendations: ['Ensure website is mobile-friendly'] },
          phone: { score: 100, issues: [], recommendations: [] }
        },
        localSEOFactors: {
          napConsistency: { score: 85, issues: [], recommendations: ['Verify NAP consistency across all platforms'] },
          citationQuality: { score: 70, issues: [], recommendations: ['Build more high-quality citations'] },
          reviewSignals: { score: 80, issues: [], recommendations: ['Encourage more customer reviews'] },
          proximityFactors: { score: 75, issues: [], recommendations: ['Optimize for local search terms'] }
        },
        technicalFactors: {
          websiteOptimization: { score: 75, issues: [], recommendations: ['Improve website SEO'] },
          mobileOptimization: { score: 85, issues: [], recommendations: [] },
          pageSpeed: { score: 70, issues: [], recommendations: ['Optimize page loading speed'] },
          structuredData: { score: 60, issues: [], recommendations: ['Implement structured data markup'] }
        },
        overallScore: 78,
        criticalIssues: ['Website speed needs improvement'],
        quickWins: ['Add more business attributes', 'Optimize business description']
      },
      sentimentAnalysis: {
        overall: { score: 85, sentiment: 'positive', confidence: 0.9 },
        breakdown: { positive: 70, neutral: 20, negative: 10 },
        trends: { recent: 88, historical: 82, trajectory: 'improving' },
        themes: {
          positive: [{ theme: 'Great service', frequency: 15, impact: 8 }],
          negative: [{ theme: 'Long wait times', frequency: 3, impact: 6 }]
        },
        responseAnalysis: {
          responseRate: 75,
          averageResponseTime: 24,
          responseQuality: 80,
          recommendations: ['Respond to more reviews', 'Improve response time']
        }
      },
      competitiveAnalysis: {
        position: { rank: 3, totalCompetitors: 10, percentile: 70 },
        strengths: [{ factor: 'Review quality', advantage: 15, description: 'Higher review scores than competitors' }],
        weaknesses: [{ factor: 'Photo count', gap: -5, description: 'Fewer photos than top competitors' }],
        opportunities: [{ factor: 'Local posts', potential: 20, description: 'Opportunity to increase posting frequency' }],
        threats: [{ factor: 'New competitors', risk: 10, description: 'New businesses entering the market' }],
        benchmarks: { averageRating: 4.2, averageReviews: 150, averagePhotos: 25, averagePosts: 12 }
      },
      photoAnalysis: {
        quantity: { score: 70, current: 15, recommended: 25 },
        quality: { score: 85, issues: [], recommendations: ['Add more high-resolution photos'] },
        diversity: { score: 75, categories: ['exterior', 'interior', 'products'], missing: ['team', 'menu'] },
        recency: { score: 60, lastUpdate: '2024-01-15', recommendations: ['Upload more recent photos'] },
        optimization: { score: 80, issues: [], recommendations: ['Add photo descriptions'] },
        overallScore: 74
      },
      overallHealth: 78,
      timestamp: new Date().toISOString()
    };

    const fallbackScores: ScoreBreakdown = {
      overall: 78,
      grade: 'B',
      breakdown: { reviews: 85, visibility: 75, seo: 78, photos: 74, posts: 70, nap: 85 },
      weights: { reviews: 0.25, visibility: 0.20, seo: 0.20, photos: 0.15, posts: 0.10, nap: 0.10 }
    };

    const fallbackInsights: Insight[] = [
      {
        id: uuidv4(),
        type: 'strength',
        category: 'reviews',
        title: 'Strong Review Performance',
        description: 'Your business maintains excellent customer satisfaction with high-quality reviews.',
        impact: 'high',
        confidence: 0.9,
        actionable: true,
        relatedMetrics: ['review_score', 'sentiment_analysis']
      },
      {
        id: uuidv4(),
        type: 'opportunity',
        category: 'photos',
        title: 'Photo Gallery Enhancement',
        description: 'Adding more diverse, high-quality photos can significantly improve visibility.',
        impact: 'medium',
        confidence: 0.8,
        actionable: true,
        relatedMetrics: ['photo_count', 'photo_diversity']
      }
    ];

    const fallbackRecommendations: Recommendation[] = [
      {
        id: uuidv4(),
        priority: 'high',
        category: 'photos',
        title: 'Expand Photo Gallery',
        description: 'Upload additional high-quality photos to showcase your business comprehensively.',
        actionItems: [
          { task: 'Take professional exterior photos', effort: 'low', timeline: '1 week', impact: 8 },
          { task: 'Capture interior ambiance shots', effort: 'low', timeline: '1 week', impact: 7 },
          { task: 'Photograph team members', effort: 'medium', timeline: '2 weeks', impact: 6 }
        ],
        expectedImpact: { scoreIncrease: 5, timeframe: '2-4 weeks', confidence: 0.8 },
        resources: [
          { type: 'guide', title: 'GMB Photo Best Practices', description: 'Complete guide to optimizing business photos' }
        ]
      }
    ];

    return {
      success: true,
      data: {
        analysis: fallbackAnalysis,
        scores: fallbackScores,
        insights: fallbackInsights,
        recommendations: fallbackRecommendations,
        timestamp: new Date().toISOString()
      }
    };
  }

  private getFallbackScore(): ScoreBreakdown {
    return {
      overall: 75,
      grade: 'B',
      breakdown: { reviews: 80, visibility: 70, seo: 75, photos: 65, posts: 85, nap: 90 },
      weights: { reviews: 0.25, visibility: 0.20, seo: 0.20, photos: 0.15, posts: 0.10, nap: 0.10 }
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}
