"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const config = {
    database: {
        url: process.env.DATABASE_URL || 'postgresql://gmb_user:gmb_password@localhost:5432/gmb_audit_db',
    },
    app: {
        env: process.env.NODE_ENV || 'development',
    },
};
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
class ExtendedPrismaClient extends client_1.PrismaClient {
    constructor() {
        super({
            datasources: {
                db: {
                    url: config.database.url,
                },
            },
        });
    }
    async getBusinessInsights(businessId) {
        const result = await this.$queryRaw `
      SELECT get_business_insights(${businessId}) as insights
    `;
        return result[0]?.insights;
    }
    async findNearbyCompetitors(latitude, longitude, radiusMiles = 5, limit = 10) {
        return this.$queryRaw `
      SELECT
        id,
        name,
        address,
        rating,
        review_count,
        latitude,
        longitude,
        calculate_distance(${latitude}, ${longitude}, latitude, longitude) as distance
      FROM businesses
      WHERE
        latitude IS NOT NULL
        AND longitude IS NOT NULL
        AND calculate_distance(${latitude}, ${longitude}, latitude, longitude) <= ${radiusMiles}
      ORDER BY distance
      LIMIT ${limit}
    `;
    }
    async getBusinessAnalytics(businessId, days = 30) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        return this.gMBData.findMany({
            where: {
                businessId,
                scrapedAt: {
                    gte: startDate,
                },
            },
            orderBy: {
                scrapedAt: 'desc',
            },
            select: {
                rating: true,
                reviewCount: true,
                totalPhotos: true,
                totalPosts: true,
                scrapedAt: true,
            },
        });
    }
    async getCompetitorRankings(businessId) {
        return this.competitor.findMany({
            where: {
                businessId,
            },
            orderBy: [
                { localRanking: 'asc' },
                { mapRanking: 'asc' },
            ],
            select: {
                id: true,
                name: true,
                rating: true,
                reviewCount: true,
                localRanking: true,
                mapRanking: true,
                distance: true,
            },
        });
    }
    async healthCheck() {
        try {
            await this.$queryRaw `SELECT 1`;
            return { status: 'healthy', timestamp: new Date() };
        }
        catch (error) {
            logger.error('Database health check failed', { error });
            return { status: 'unhealthy', error: error.message, timestamp: new Date() };
        }
    }
    async logApiUsage(data) {
        return this.apiUsage.create({
            data: {
                ...data,
                timestamp: new Date(),
            },
        });
    }
    async logSystemMetric(metricType, metricName, value, unit, tags) {
        return this.systemMetrics.create({
            data: {
                metricType,
                metricName,
                value,
                unit,
                tags,
                timestamp: new Date(),
            },
        });
    }
}
const prisma = new ExtendedPrismaClient();
exports.prisma = prisma;
process.on('beforeExit', async () => {
    logger.info('Disconnecting from database...');
    await prisma.$disconnect();
});
exports.default = prisma;
//# sourceMappingURL=index.js.map