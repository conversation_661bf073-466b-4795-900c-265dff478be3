import { AnalysisResult, ScoreBreakdown, Insight } from '../types/analysis';
export declare class InsightGenerator {
    generateInsights(analysis: AnalysisResult, scores: ScoreBreakdown): Promise<Insight[]>;
    private generateSEOInsights;
    private generateReviewInsights;
    private generateCompetitiveInsights;
    private generatePhotoInsights;
    private generateOverallInsights;
    private getImpactWeight;
}
//# sourceMappingURL=InsightGenerator.d.ts.map