export interface EmailDeliveryRequest {
    reportId: string;
    recipient: string;
    subject?: string;
    message?: string;
    attachPDF?: boolean;
}
export interface WhatsAppDeliveryRequest {
    reportId: string;
    phoneNumber: string;
    message?: string;
    includeLink?: boolean;
}
export interface DeliveryResponse {
    deliveryId: string;
    type: 'email' | 'whatsapp';
    status: 'pending' | 'sent' | 'failed';
    recipient: string;
    timestamp: string;
    trackingInfo?: any;
}
export interface DeliveryStats {
    totalDeliveries: number;
    emailDeliveries: number;
    whatsappDeliveries: number;
    successfulDeliveries: number;
    failedDeliveries: number;
}
export declare class SimpleDeliveryService {
    private deliveryHistory;
    private emailTemplatesDir;
    constructor();
    sendEmailReport(request: EmailDeliveryRequest): Promise<DeliveryResponse>;
    sendWhatsAppReport(request: WhatsAppDeliveryRequest): Promise<DeliveryResponse>;
    sendMultiChannelReport(emailRequest?: EmailDeliveryRequest, whatsappRequest?: WhatsAppDeliveryRequest): Promise<DeliveryResponse[]>;
    private generateEmailContent;
    private generateWhatsAppMessage;
    private getDefaultEmailTemplate;
    private isValidEmail;
    private isValidPhoneNumber;
    getDeliveryStatus(deliveryId: string): DeliveryResponse | undefined;
    getDeliveryStats(): DeliveryStats;
    getDeliveryHistory(limit?: number): DeliveryResponse[];
    clearHistory(): void;
}
export default SimpleDeliveryService;
//# sourceMappingURL=SimpleDeliveryService.d.ts.map