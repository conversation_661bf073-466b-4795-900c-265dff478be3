# 🚀 GMB Audit Generator

A world-class Google Business Profile audit report generator that outperforms competitors through superior data collection, AI-powered analysis, and automated report generation.

## 📋 Features

- **Comprehensive GMB Data Scraping** - Extract business listings, reviews, photos, posts, and competitor data
- **AI-Powered Analysis** - Multi-AI analysis using Gemini, OpenAI, and Perplexity for superior insights
- **Advanced Scoring System** - Weighted scoring across SEO, visibility, reviews, photos, and NAP consistency
- **Dynamic Visualizations** - Interactive heatmaps, charts, and competitor analysis
- **Automated Report Generation** - Beautiful PDF reports with actionable recommendations
- **Multi-Channel Delivery** - Email, WhatsApp, and direct download options
- **n8n Workflow Integration** - Visual workflow automation and orchestration
- **Real-time Monitoring** - Comprehensive logging, metrics, and health monitoring

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Input Layer   │    │  Processing     │    │   Output Layer  │
│                 │    │     Engine      │    │                 │
│ • Web Forms     │───▶│ • Data Scraper  │───▶│ • PDF Reports   │
│ • API Endpoints │    │ • AI Analyzer   │    │ • Email/WhatsApp│
│ • n8n Workflows│    │ • Score Engine  │    │ • Dashboard     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Data Layer    │
                    │                 │
                    │ • PostgreSQL    │
                    │ • Redis Cache   │
                    │ • File Storage  │
                    └─────────────────┘
```

## 🛠️ Tech Stack

- **Backend**: Node.js + TypeScript + Express.js
- **Database**: PostgreSQL 15+ with Prisma ORM
- **Cache**: Redis 7+ for session and API caching
- **Queue**: Bull Queue for job processing
- **Scraping**: Playwright with anti-detection measures
- **AI**: Google Gemini Pro + OpenAI GPT-4 + Perplexity Sonar
- **Visualization**: Leaflet.js + Chart.js
- **Reports**: Puppeteer for PDF generation
- **Orchestration**: n8n for workflow automation
- **Monitoring**: Winston + Sentry + Prometheus

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- Git

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd gmb-audit-generator

# Copy environment variables
cp .env.example .env

# Edit .env with your API keys and configuration
nano .env
```

### 2. Install Dependencies

```bash
# Install all dependencies
npm install

# Generate Prisma client
npm run db:generate
```

### 3. Start Development Environment

```bash
# Start all services (PostgreSQL, Redis, n8n, Adminer)
npm run docker:dev

# Start the API in development mode
npm run dev
```

### 4. Access Services

- **API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api-docs
- **n8n Workflows**: http://localhost:5678 (admin/admin123)
- **Database Admin**: http://localhost:8080
- **Redis Admin**: http://localhost:8081

## 📁 Project Structure

```
gmb-audit-generator/
├── apps/
│   ├── api/                    # Main API service
│   ├── scraper/               # Dedicated scraping service
│   ├── analyzer/              # AI analysis service
│   ├── reporter/              # PDF generation service
│   └── dashboard/             # Admin dashboard
├── packages/
│   ├── shared/                # Shared utilities
│   ├── database/              # Prisma schema & migrations
│   ├── types/                 # TypeScript definitions
│   └── config/                # Environment configurations
├── infrastructure/
│   ├── postgres/              # PostgreSQL configuration
│   ├── redis/                 # Redis configuration
│   └── docker/                # Container configurations
├── n8n-workflows/             # n8n workflow definitions
└── docs/                      # Technical documentation
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev                    # Start all services in development mode
npm run build                  # Build all packages
npm run test                   # Run test suite
npm run test:watch            # Run tests in watch mode

# Database
npm run db:generate           # Generate Prisma client
npm run db:push              # Push schema to database
npm run db:migrate           # Run database migrations
npm run db:studio            # Open Prisma Studio

# Code Quality
npm run lint                 # Run ESLint
npm run lint:fix            # Fix ESLint issues
npm run type-check          # TypeScript type checking

# Docker
npm run docker:dev          # Start development containers
npm run docker:down         # Stop development containers
```

### Environment Variables

Key environment variables you need to configure:

```bash
# Database
DATABASE_URL="postgresql://gmb_user:gmb_password@localhost:5432/gmb_audit_db"
REDIS_URL="redis://localhost:6379"

# AI Services
GEMINI_API_KEY="your_gemini_api_key"
OPENAI_API_KEY="your_openai_api_key"
GOOGLE_MAPS_API_KEY="your_google_maps_api_key"

# Email
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"

# Security
JWT_SECRET="your_32_character_secret_key"
ENCRYPTION_KEY="your_32_character_encryption_key"
```

## 📊 API Endpoints

### Core Endpoints

- `GET /health` - Health check
- `POST /api/audit/create` - Create new audit
- `GET /api/audit/:id` - Get audit status
- `GET /api/reports/:id` - Download report
- `POST /webhook/n8n` - n8n webhook integration

### Authentication

All API endpoints (except health and webhooks) require JWT authentication:

```bash
Authorization: Bearer <your_jwt_token>
```

## 🔄 Workflow Integration

### n8n Workflow Example

1. **Form Submission** → Webhook trigger
2. **Data Validation** → Validate business information
3. **Scraping** → Extract GMB data
4. **Analysis** → AI-powered analysis
5. **Report Generation** → Create PDF report
6. **Delivery** → Send via email/WhatsApp

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run tests for specific package
npm run test --workspace=@gmb-audit/api

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test -- --coverage
```

## 📈 Monitoring

### Health Checks

- **API Health**: `GET /health`
- **Database**: Connection and query performance
- **Redis**: Cache availability and performance
- **External APIs**: AI services and Google APIs

### Metrics

- Request/response times
- Scraping success rates
- Report generation times
- Error rates and types
- Resource utilization

## 🚀 Deployment

### Development Deployment

```bash
# Using Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

### Production Deployment

See `infrastructure/` directory for:
- Kubernetes manifests
- Terraform configurations
- CI/CD pipeline definitions

## 🔐 Security

- JWT-based authentication
- Rate limiting on all endpoints
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration
- Helmet.js security headers

## 📚 Documentation

- [Technical Implementation Plan](./Technical_Implementation_Plan.md)
- [Quick Start Guide](./Quick_Start_Guide.md)
- [API Documentation](http://localhost:3000/api-docs)
- [Database Schema](./packages/database/prisma/schema.prisma)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the troubleshooting guide

---

Built with ❤️ for superior Google Business Profile auditing.
