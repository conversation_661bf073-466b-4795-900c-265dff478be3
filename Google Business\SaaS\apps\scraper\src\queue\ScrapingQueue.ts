import Queue from 'bull';
import { GMBScrapingService, ScrapingRequest, GMBData } from '../services/GMBScrapingService';

// Simple logger for Phase 2
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

export interface ScrapingJobData extends ScrapingRequest {
  jobId: string;
  createdAt: Date;
  retryCount?: number;
}

export interface JobStatus {
  id: string;
  status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused' | 'stuck';
  progress: number;
  data?: any;
  result?: GMBData;
  error?: string;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
}

export class ScrapingQueue {
  private queue: Queue.Queue<ScrapingJobData>;
  private scrapingService: GMBScrapingService;
  private isInitialized = false;

  constructor() {
    // Initialize Redis connection for queue
    const redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
    };

    this.queue = new Queue('gmb-scraping', {
      redis: redisConfig,
      defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: 3, // Retry failed jobs up to 3 times
        backoff: {
          type: 'exponential',
          delay: 5000, // Start with 5 second delay
        },
      },
    });

    this.scrapingService = new GMBScrapingService();
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    logger.info('Initializing scraping queue');

    // Set up job processing
    this.queue.process('scrape-business', 1, async (job) => {
      return this.processScrapingJob(job);
    });

    // Set up event listeners
    this.queue.on('completed', (job, result) => {
      logger.info('Scraping job completed', { 
        jobId: job.id, 
        businessName: job.data.businessName 
      });
    });

    this.queue.on('failed', (job, error) => {
      logger.error('Scraping job failed', { 
        jobId: job.id, 
        businessName: job.data.businessName,
        error: error.message 
      });
    });

    this.queue.on('stalled', (job) => {
      logger.warn('Scraping job stalled', { 
        jobId: job.id, 
        businessName: job.data.businessName 
      });
    });

    this.queue.on('progress', (job, progress) => {
      logger.debug('Scraping job progress', { 
        jobId: job.id, 
        progress: `${progress}%` 
      });
    });

    // Clean up old jobs periodically
    setInterval(async () => {
      try {
        await this.queue.clean(24 * 60 * 60 * 1000, 'completed'); // Clean completed jobs older than 24 hours
        await this.queue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Clean failed jobs older than 7 days
      } catch (error) {
        logger.error('Error cleaning queue', { error });
      }
    }, 60 * 60 * 1000); // Run every hour

    this.isInitialized = true;
    logger.info('Scraping queue initialized successfully');
  }

  private async processScrapingJob(job: Queue.Job<ScrapingJobData>): Promise<GMBData> {
    const { businessName, address, placeId } = job.data;
    
    logger.info('Processing scraping job', { 
      jobId: job.id, 
      businessName,
      attempt: job.attemptsMade + 1 
    });

    try {
      // Update progress
      await job.progress(10);

      // Perform the scraping
      await job.progress(30);
      const scrapingRequest: ScrapingRequest = {
        businessName,
        address,
        placeId,
      };

      await job.progress(50);
      const result = await this.scrapingService.scrapeBusinessData(scrapingRequest);

      await job.progress(80);

      // Here you would typically save to database
      // await this.saveToDatabase(result);

      await job.progress(100);

      logger.info('Scraping job processed successfully', { 
        jobId: job.id, 
        businessName 
      });

      return result;

    } catch (error: any) {
      logger.error('Error processing scraping job', { 
        jobId: job.id, 
        businessName,
        error: error.message 
      });
      throw error;
    }
  }

  public async addScrapingJob(request: ScrapingRequest): Promise<Queue.Job<ScrapingJobData>> {
    const jobData: ScrapingJobData = {
      ...request,
      jobId: `scrape-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
    };

    const priority = this.getPriority(request.priority || 'normal');
    
    const job = await this.queue.add('scrape-business', jobData, {
      priority,
      delay: this.getDelay(), // Add random delay to avoid rate limiting
    });

    logger.info('Scraping job added to queue', { 
      jobId: job.id, 
      businessName: request.businessName,
      priority: request.priority 
    });

    return job;
  }

  private getPriority(priority: string): number {
    switch (priority) {
      case 'high': return 10;
      case 'normal': return 5;
      case 'low': return 1;
      default: return 5;
    }
  }

  private getDelay(): number {
    // Add random delay between 1-10 seconds to avoid rate limiting
    return Math.floor(Math.random() * 9000) + 1000;
  }

  public async getJobStatus(jobId: string): Promise<JobStatus> {
    const job = await this.queue.getJob(jobId);
    
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    const status: JobStatus = {
      id: job.id?.toString() || '',
      status: await job.getState(),
      progress: job.progress(),
      data: job.data,
      createdAt: new Date(job.timestamp),
    };

    if (job.processedOn) {
      status.processedAt = new Date(job.processedOn);
    }

    if (job.finishedOn) {
      status.completedAt = new Date(job.finishedOn);
    }

    if (job.returnvalue) {
      status.result = job.returnvalue;
    }

    if (job.failedReason) {
      status.error = job.failedReason;
    }

    return status;
  }

  public async getQueueStats(): Promise<any> {
    const waiting = await this.queue.getWaiting();
    const active = await this.queue.getActive();
    const completed = await this.queue.getCompleted();
    const failed = await this.queue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length,
    };
  }

  public async pauseQueue(): Promise<void> {
    await this.queue.pause();
    logger.info('Scraping queue paused');
  }

  public async resumeQueue(): Promise<void> {
    await this.queue.resume();
    logger.info('Scraping queue resumed');
  }

  public async close(): Promise<void> {
    logger.info('Closing scraping queue');
    
    try {
      await this.queue.close();
      await this.scrapingService.close();
      logger.info('Scraping queue closed successfully');
    } catch (error) {
      logger.error('Error closing scraping queue', { error });
    }
  }
}
