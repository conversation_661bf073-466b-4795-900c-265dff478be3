import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Simple config for Phase 1
const config = {
  database: {
    url: process.env.DATABASE_URL || 'postgresql://gmb_user:gmb_password@localhost:5432/gmb_audit_db',
  },
  app: {
    env: process.env.NODE_ENV || 'development',
  },
};

// Simple logger for Phase 1
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

// Simple PrismaClient for Phase 1
class ExtendedPrismaClient extends PrismaClient {
  constructor() {
    super({
      datasources: {
        db: {
          url: config.database.url,
        },
      },
    });
  }

  // Custom method to get business insights
  async getBusinessInsights(businessId: string) {
    const result: any = await this.$queryRaw`
      SELECT get_business_insights(${businessId}) as insights
    `;
    return result[0]?.insights;
  }

  // Custom method to find nearby competitors
  async findNearbyCompetitors(
    latitude: number,
    longitude: number,
    radiusMiles: number = 5,
    limit: number = 10
  ) {
    return this.$queryRaw`
      SELECT
        id,
        name,
        address,
        rating,
        review_count,
        latitude,
        longitude,
        calculate_distance(${latitude}, ${longitude}, latitude, longitude) as distance
      FROM businesses
      WHERE
        latitude IS NOT NULL
        AND longitude IS NOT NULL
        AND calculate_distance(${latitude}, ${longitude}, latitude, longitude) <= ${radiusMiles}
      ORDER BY distance
      LIMIT ${limit}
    ` as any;
  }

  // Custom method to get business analytics
  async getBusinessAnalytics(businessId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this.gMBData.findMany({
      where: {
        businessId,
        scrapedAt: {
          gte: startDate,
        },
      },
      orderBy: {
        scrapedAt: 'desc',
      },
      select: {
        rating: true,
        reviewCount: true,
        totalPhotos: true,
        totalPosts: true,
        scrapedAt: true,
      },
    });
  }

  // Custom method to get competitor rankings
  async getCompetitorRankings(businessId: string) {
    return this.competitor.findMany({
      where: {
        businessId,
      },
      orderBy: [
        { localRanking: 'asc' },
        { mapRanking: 'asc' },
      ],
      select: {
        id: true,
        name: true,
        rating: true,
        reviewCount: true,
        localRanking: true,
        mapRanking: true,
        distance: true,
      },
    });
  }

  // Health check method
  async healthCheck() {
    try {
      await this.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date() };
    } catch (error: any) {
      logger.error('Database health check failed', { error });
      return { status: 'unhealthy', error: error.message, timestamp: new Date() };
    }
  }

  // Method to log API usage
  async logApiUsage(data: {
    userId?: string;
    endpoint: string;
    method: string;
    statusCode: number;
    responseTime: number;
    userAgent?: string;
    ipAddress?: string;
  }) {
    return this.apiUsage.create({
      data: {
        ...data,
        timestamp: new Date(),
      },
    });
  }

  // Method to log system metrics
  async logSystemMetric(
    metricType: string,
    metricName: string,
    value: number,
    unit?: string,
    tags?: any
  ) {
    return this.systemMetrics.create({
      data: {
        metricType,
        metricName,
        value,
        unit,
        tags,
        timestamp: new Date(),
      },
    });
  }
}

// Create singleton instance
const prisma = new ExtendedPrismaClient();

// Graceful shutdown
process.on('beforeExit', async () => {
  logger.info('Disconnecting from database...');
  await prisma.$disconnect();
});

export { prisma };
export default prisma;

// Export types (will be available after Prisma client generation)
// export type {
//   User,
//   Business,
//   GMBData,
//   Competitor,
//   AuditReport,
//   SystemMetrics,
//   ApiUsage
// } from '@prisma/client';
