const { WorldClassReportGenerator } = require('./apps/report-generator/src/services/WorldClassReportGenerator');

// Test data
const businessData = {
  businessName: 'Raga Dental Implants',
  address: '123 Main Street, Thanjavur, Tamil Nadu 613001',
  phone: '+91-4362-123456',
  category: 'Dental Clinic',
  description: 'Leading dental implant clinic in Thanjavur'
};

const analysisData = {
  scores: { overall: 45, grade: 'F' },
  recommendations: []
};

async function testKeywordRanking() {
  console.log('🔍 Testing Keyword Ranking Generation...');
  
  try {
    const generator = new WorldClassReportGenerator();
    
    // Test the keyword ranking method directly
    const keywordSection = await generator.generateKeywordRanking(businessData, analysisData);
    
    console.log('✅ Keyword ranking section generated successfully!');
    console.log('📏 Section length:', keywordSection.length, 'characters');
    console.log('🔍 Contains title:', keywordSection.includes('🔍 Keyword Ranking Analysis'));
    console.log('📊 Contains table:', keywordSection.includes('keyword-table'));
    
    // Save the section to a file for inspection
    const fs = require('fs');
    fs.writeFileSync('./keyword-section-debug.html', `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Keyword Section Debug</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .keyword-table { border-collapse: collapse; width: 100%; }
        .keyword-table th, .keyword-table td { border: 1px solid #ddd; padding: 8px; }
        .keyword-table th { background-color: #f2f2f2; }
      </style>
    </head>
    <body>
      ${keywordSection}
    </body>
    </html>
    `);
    
    console.log('💾 Debug HTML saved to keyword-section-debug.html');
    
  } catch (error) {
    console.error('❌ Error generating keyword ranking:', error.message);
    console.error('Stack:', error.stack);
  }
}

testKeywordRanking();
