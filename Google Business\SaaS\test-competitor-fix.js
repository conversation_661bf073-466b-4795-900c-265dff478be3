require('dotenv').config();
const { CompetitorAnalysisService } = require('./apps/scraper/dist/services/CompetitorAnalysisService.js');

async function testCompetitorAnalysis() {
  const service = new CompetitorAnalysisService();
  
  const request = {
    businessName: 'Naturals Training Academy',
    address: 'Thanjavur, Tamil Nadu, India',
    category: 'Beauty Training',
    radius: 5,
    maxCompetitors: 5
  };
  
  console.log('🔍 Testing competitor analysis with auto-geocoding...');
  console.log('Request:', JSON.stringify(request, null, 2));
  
  try {
    const result = await service.analyzeCompetitors(request);
    console.log('✅ SUCCESS! Competitor analysis result:');
    console.log('Total competitors found:', result.competitors.length);
    console.log('First 3 competitors:');
    result.competitors.slice(0, 3).forEach((comp, i) => {
      console.log(`${i+1}. ${comp.businessName} - ${comp.rating}⭐ (${comp.reviewCount} reviews)`);
      console.log(`   Address: ${comp.address}`);
      console.log(`   Data Source: ${comp.dataSource || 'Unknown'}`);
    });
    
    await service.close();
  } catch (error) {
    console.error('❌ ERROR:', error.message);
  }
}

testCompetitorAnalysis();
