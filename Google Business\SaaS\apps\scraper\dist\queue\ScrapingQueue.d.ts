import Queue from 'bull';
import { ScrapingRequest, GMBData } from '../services/GMBScrapingService';
export interface ScrapingJobData extends ScrapingRequest {
    jobId: string;
    createdAt: Date;
    retryCount?: number;
}
export interface JobStatus {
    id: string;
    status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused' | 'stuck';
    progress: number;
    data?: any;
    result?: GMBData;
    error?: string;
    createdAt: Date;
    processedAt?: Date;
    completedAt?: Date;
}
export declare class ScrapingQueue {
    private queue;
    private scrapingService;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    private processScrapingJob;
    addScrapingJob(request: ScrapingRequest): Promise<Queue.Job<ScrapingJobData>>;
    private getPriority;
    private getDelay;
    getJobStatus(jobId: string): Promise<JobStatus>;
    getQueueStats(): Promise<any>;
    pauseQueue(): Promise<void>;
    resumeQueue(): Promise<void>;
    close(): Promise<void>;
}
//# sourceMappingURL=ScrapingQueue.d.ts.map