# Database Configuration
DATABASE_URL="postgresql://gmb_user:gmb_password@localhost:5432/gmb_audit_db"
REDIS_URL="redis://localhost:6379"

# AI Service API Keys
GEMINI_API_KEY="AIzaSyDD8amL8y5JPiitcRqMN5jLuHVkUOIPxSA"
OPENAI_API_KEY="********************************************************************************************************************************************************************"
PERPLEXITY_API_KEY="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"

# Google Services
GOOGLE_MAPS_API_KEY="AIzaSyCMh3qCD4apGYbDxSPu6pkyG3beNzX0Y7M"
GOOGLE_STATIC_MAPS_API_KEY="AIzaSyBNd3FZ4y14r39lV-SAYCWTsw91Jb8fNHA"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"
FROM_EMAIL="<EMAIL>"

# WhatsApp Configuration (Optional)
WHATSAPP_API_URL="https://api.whatsapp.com/send"
WHATSAPP_TOKEN="your_whatsapp_token"

# Security
JWT_SECRET="your_super_secret_jwt_key_here"
ENCRYPTION_KEY="your_32_character_encryption_key"
API_RATE_LIMIT_WINDOW_MS=900000
API_RATE_LIMIT_MAX_REQUESTS=100

# Application Configuration
NODE_ENV="development"
PORT=3000
API_BASE_URL="http://localhost:3000"
FRONTEND_URL="http://localhost:3001"

# File Storage
UPLOAD_DIR="./uploads"
REPORTS_DIR="./reports"
MAX_FILE_SIZE_MB=10

# Scraping Configuration
SCRAPING_DELAY_MIN=1000
SCRAPING_DELAY_MAX=3000
SCRAPING_TIMEOUT=30000
MAX_CONCURRENT_SCRAPERS=3
USE_PROXY=false
PROXY_LIST_URL=""

# Monitoring & Logging
LOG_LEVEL="info"
SENTRY_DSN=""
ENABLE_METRICS=true

# n8n Configuration
N8N_WEBHOOK_URL="http://localhost:5678/webhook"
N8N_API_KEY="your_n8n_api_key"

# Development
ENABLE_SWAGGER=true
ENABLE_CORS=true
CORS_ORIGIN="http://localhost:3001"
