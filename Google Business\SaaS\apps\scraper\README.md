# GMB Scraper Service

A robust Google My Business (GMB) data scraping service with anti-detection measures, data validation, and competitor analysis capabilities.

## Features

### 🕷️ Advanced Web Scraping
- **Playwright-based scraping** with anti-detection measures
- **Stealth mode** with randomized user agents and browser fingerprints
- **Proxy rotation** support for large-scale operations
- **Human-like behavior** simulation with random delays and typing patterns
- **Rate limiting** and queue management to avoid detection

### 📊 Data Validation & Quality
- **Comprehensive data validation** using Joi schemas
- **Data cleaning and normalization** pipelines
- **Quality metrics** calculation (completeness, accuracy, consistency, freshness)
- **Confidence scoring** for scraped data
- **Error handling** and data recovery mechanisms

### 🏢 Competitor Analysis
- **Automated competitor discovery** based on business category and location
- **Competitive landscape analysis** with market saturation assessment
- **Strength/weakness analysis** for each competitor
- **Threat level assessment** and competitive positioning
- **Gap analysis** and strategic recommendations

### ⚡ Queue Management
- **Redis-based job queue** using Bull for scalable processing
- **Priority-based scheduling** (high, normal, low priority jobs)
- **Retry mechanisms** with exponential backoff
- **Job status tracking** and progress monitoring
- **Automatic cleanup** of old completed/failed jobs

## API Endpoints

### Health Check
```
GET /health
```
Returns service health status and basic information.

### Business Scraping
```
POST /api/scrape/business
Content-Type: application/json

{
  "businessName": "Example Restaurant",
  "address": "123 Main St, New York, NY",
  "placeId": "optional-google-place-id",
  "priority": "normal"
}
```
Adds a scraping job to the queue and returns job ID.

### Job Status
```
GET /api/scrape/status/:jobId
```
Returns the current status and progress of a scraping job.

### Direct Scraping (Testing)
```
POST /api/scrape/direct
Content-Type: application/json

{
  "businessName": "Example Restaurant",
  "address": "123 Main St, New York, NY",
  "placeId": "optional-google-place-id"
}
```
Performs immediate scraping without queue (for testing purposes).

### Data Validation
```
POST /api/validate
Content-Type: application/json

{
  "data": {
    "businessName": "Example Restaurant",
    "address": "123 Main St, New York, NY",
    "rating": 4.5,
    "reviewCount": 150
  }
}
```
Validates and cleans GMB data, returns validation results and quality metrics.

### Competitor Analysis
```
POST /api/competitors/analyze
Content-Type: application/json

{
  "businessName": "Example Restaurant",
  "address": "123 Main St, New York, NY",
  "category": "restaurant",
  "radius": 5,
  "maxCompetitors": 10
}
```
Analyzes competitors in the local area and provides competitive insights.

## Environment Variables

```bash
# Server Configuration
SCRAPER_PORT=3001
CORS_ORIGIN=*
NODE_ENV=development

# Redis Configuration (for queue)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Proxy Configuration (optional)
PROXY_LIST=http://proxy1:port,http://proxy2:port
```

## Installation & Setup

1. **Install dependencies:**
```bash
npm install
```

2. **Build the service:**
```bash
npm run build
```

3. **Start development server:**
```bash
npm run dev
```

4. **Start production server:**
```bash
npm start
```

## Data Structures

### GMB Data Schema
```typescript
interface GMBData {
  businessName: string;
  address: string;
  phone?: string;
  website?: string;
  rating?: number;
  reviewCount?: number;
  totalPhotos?: number;
  totalPosts?: number;
  hours?: any;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  recentReviews?: any[];
  photos?: string[];
  posts?: any[];
  description?: string;
  attributes?: any;
  placeId?: string;
  googleUrl?: string;
  rawData?: any;
  scrapedAt: Date;
}
```

### Competitor Data
```typescript
interface CompetitorData extends GMBData {
  competitorId: string;
  distance?: number;
  localRanking?: number;
  competitiveStrength: number; // 0-100 score
  threatLevel: 'low' | 'medium' | 'high';
  advantages: string[];
  weaknesses: string[];
}
```

## Anti-Detection Features

- **Dynamic User Agents**: Rotates through realistic browser user agents
- **Viewport Randomization**: Random screen resolutions and window sizes
- **Geolocation Spoofing**: Randomized coordinates near target location
- **Browser Fingerprint Masking**: Overrides webdriver detection properties
- **Human-like Timing**: Random delays between actions
- **Proxy Support**: Optional proxy rotation for IP diversity

## Queue Management

The service uses Redis and Bull queue for managing scraping jobs:

- **Job Priorities**: High (10), Normal (5), Low (1)
- **Retry Logic**: Up to 3 attempts with exponential backoff
- **Rate Limiting**: Random delays between jobs to avoid detection
- **Cleanup**: Automatic removal of old completed/failed jobs

## Error Handling

- **Graceful degradation** when scraping fails
- **Detailed error logging** with context information
- **Retry mechanisms** for transient failures
- **Data validation** to ensure quality
- **Fallback strategies** for missing data

## Performance Considerations

- **Concurrent processing** limited to prevent detection
- **Memory management** with automatic browser cleanup
- **Resource monitoring** and cleanup of stale connections
- **Queue optimization** with priority-based scheduling

## Security Features

- **Rate limiting** on API endpoints
- **Input validation** and sanitization
- **CORS protection** with configurable origins
- **Security headers** using Helmet.js
- **Error message sanitization** to prevent information leakage

## Monitoring & Logging

- **Structured logging** with Winston-compatible interface
- **Performance metrics** tracking
- **Job status monitoring** through queue dashboard
- **Health check endpoints** for service monitoring

## Development

### Running Tests
```bash
npm test
npm run test:watch
```

### Linting
```bash
npm run lint
npm run lint:fix
```

### Type Checking
```bash
npm run type-check
```

## Production Deployment

1. **Environment Setup**: Configure all required environment variables
2. **Redis Instance**: Ensure Redis is running and accessible
3. **Proxy Configuration**: Set up proxy rotation if needed
4. **Monitoring**: Implement health checks and alerting
5. **Scaling**: Use multiple instances with shared Redis queue

## Legal Considerations

- **Respect robots.txt** and website terms of service
- **Rate limiting** to avoid overwhelming target servers
- **Data privacy** compliance with local regulations
- **Fair use** principles for web scraping

## Support

For issues and questions, please refer to the main project documentation or create an issue in the project repository.
