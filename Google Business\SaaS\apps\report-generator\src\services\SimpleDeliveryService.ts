import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';

export interface EmailDeliveryRequest {
  reportId: string;
  recipient: string;
  subject?: string;
  message?: string;
  attachPDF?: boolean;
}

export interface WhatsAppDeliveryRequest {
  reportId: string;
  phoneNumber: string;
  message?: string;
  includeLink?: boolean;
}

export interface DeliveryResponse {
  deliveryId: string;
  type: 'email' | 'whatsapp';
  status: 'pending' | 'sent' | 'failed';
  recipient: string;
  timestamp: string;
  trackingInfo?: any;
}

export interface DeliveryStats {
  totalDeliveries: number;
  emailDeliveries: number;
  whatsappDeliveries: number;
  successfulDeliveries: number;
  failedDeliveries: number;
}

export class SimpleDeliveryService {
  private deliveryHistory: Map<string, DeliveryResponse> = new Map();
  private emailTemplatesDir: string;

  constructor() {
    this.emailTemplatesDir = path.join(__dirname, '../templates/email');
  }

  /**
   * Send report via email
   */
  async sendEmailReport(request: EmailDeliveryRequest): Promise<DeliveryResponse> {
    const deliveryId = uuidv4();
    
    try {
      // Validate email format
      if (!this.isValidEmail(request.recipient)) {
        throw new Error('Invalid email address format');
      }

      // For now, simulate email sending (would use nodemailer in production)
      const emailContent = await this.generateEmailContent(request);
      
      const response: DeliveryResponse = {
        deliveryId,
        type: 'email',
        status: 'sent', // Simulated success
        recipient: request.recipient,
        timestamp: new Date().toISOString(),
        trackingInfo: {
          subject: request.subject || 'Your GMB Audit Report',
          contentLength: emailContent.length,
          attachments: request.attachPDF ? ['report.pdf'] : [],
          provider: 'smtp_simulation'
        }
      };

      // Store delivery record
      this.deliveryHistory.set(deliveryId, response);

      return response;

    } catch (error) {
      const failedResponse: DeliveryResponse = {
        deliveryId,
        type: 'email',
        status: 'failed',
        recipient: request.recipient,
        timestamp: new Date().toISOString(),
        trackingInfo: {
          error: error instanceof Error ? error.message : String(error)
        }
      };

      this.deliveryHistory.set(deliveryId, failedResponse);
      throw new Error(`Email delivery failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Send report via WhatsApp
   */
  async sendWhatsAppReport(request: WhatsAppDeliveryRequest): Promise<DeliveryResponse> {
    const deliveryId = uuidv4();
    
    try {
      // Validate phone number format
      if (!this.isValidPhoneNumber(request.phoneNumber)) {
        throw new Error('Invalid phone number format');
      }

      // For now, simulate WhatsApp sending (would use Twilio in production)
      const message = this.generateWhatsAppMessage(request);
      
      const response: DeliveryResponse = {
        deliveryId,
        type: 'whatsapp',
        status: 'sent', // Simulated success
        recipient: request.phoneNumber,
        timestamp: new Date().toISOString(),
        trackingInfo: {
          messageLength: message.length,
          includeLink: request.includeLink || false,
          provider: 'twilio_simulation'
        }
      };

      // Store delivery record
      this.deliveryHistory.set(deliveryId, response);

      return response;

    } catch (error) {
      const failedResponse: DeliveryResponse = {
        deliveryId,
        type: 'whatsapp',
        status: 'failed',
        recipient: request.phoneNumber,
        timestamp: new Date().toISOString(),
        trackingInfo: {
          error: error instanceof Error ? error.message : String(error)
        }
      };

      this.deliveryHistory.set(deliveryId, failedResponse);
      throw new Error(`WhatsApp delivery failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Send report via multiple channels
   */
  async sendMultiChannelReport(
    emailRequest?: EmailDeliveryRequest,
    whatsappRequest?: WhatsAppDeliveryRequest
  ): Promise<DeliveryResponse[]> {
    const results: DeliveryResponse[] = [];

    if (emailRequest) {
      try {
        const emailResult = await this.sendEmailReport(emailRequest);
        results.push(emailResult);
      } catch (error) {
        console.error('Email delivery failed:', error instanceof Error ? error.message : String(error));
      }
    }

    if (whatsappRequest) {
      try {
        const whatsappResult = await this.sendWhatsAppReport(whatsappRequest);
        results.push(whatsappResult);
      } catch (error) {
        console.error('WhatsApp delivery failed:', error instanceof Error ? error.message : String(error));
      }
    }

    return results;
  }

  /**
   * Generate email content
   */
  private async generateEmailContent(request: EmailDeliveryRequest): Promise<string> {
    try {
      // Load email template
      const templatePath = path.join(this.emailTemplatesDir, 'default.html');
      let template: string;

      if (fs.existsSync(templatePath)) {
        template = fs.readFileSync(templatePath, 'utf-8');
      } else {
        template = this.getDefaultEmailTemplate();
      }

      // Replace placeholders
      const content = template
        .replace(/{{reportId}}/g, request.reportId)
        .replace(/{{recipient}}/g, request.recipient)
        .replace(/{{subject}}/g, request.subject || 'Your GMB Audit Report')
        .replace(/{{message}}/g, request.message || 'Please find your Google Business Profile audit report attached.')
        .replace(/{{portalLink}}/g, `${process.env.BASE_URL || 'http://localhost:3003'}/api/portal/${request.reportId}`)
        .replace(/{{downloadLink}}/g, `${process.env.BASE_URL || 'http://localhost:3003'}/api/download/${request.reportId}`)
        .replace(/{{timestamp}}/g, new Date().toLocaleDateString());

      return content;

    } catch (error) {
      throw new Error(`Failed to generate email content: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate WhatsApp message
   */
  private generateWhatsAppMessage(request: WhatsAppDeliveryRequest): string {
    const baseUrl = process.env.BASE_URL || 'http://localhost:3003';
    const portalLink = `${baseUrl}/api/portal/${request.reportId}`;
    
    let message = request.message || '🏢 Your Google Business Profile Audit Report is ready!';
    
    if (request.includeLink !== false) {
      message += `\n\n📊 View your report: ${portalLink}`;
      message += `\n📥 Download PDF: ${baseUrl}/api/download/${request.reportId}`;
    }
    
    message += '\n\n✨ Generated by GMB Audit Report Generator';
    
    return message;
  }

  /**
   * Get default email template
   */
  private getDefaultEmailTemplate(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{subject}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #3b82f6; color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 GMB Audit Report</h1>
            <p>Your Google Business Profile analysis is complete</p>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>{{message}}</p>
            <p>You can access your report in the following ways:</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{portalLink}}" class="button">📊 View Online</a>
                <a href="{{downloadLink}}" class="button">📥 Download PDF</a>
            </div>
            <p><strong>Report ID:</strong> {{reportId}}</p>
            <p><strong>Generated:</strong> {{timestamp}}</p>
        </div>
        <div class="footer">
            <p>Generated by GMB Audit Report Generator</p>
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Basic phone number validation (international format)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  /**
   * Get delivery status
   */
  getDeliveryStatus(deliveryId: string): DeliveryResponse | undefined {
    return this.deliveryHistory.get(deliveryId);
  }

  /**
   * Get delivery statistics
   */
  getDeliveryStats(): DeliveryStats {
    const deliveries = Array.from(this.deliveryHistory.values());
    
    return {
      totalDeliveries: deliveries.length,
      emailDeliveries: deliveries.filter(d => d.type === 'email').length,
      whatsappDeliveries: deliveries.filter(d => d.type === 'whatsapp').length,
      successfulDeliveries: deliveries.filter(d => d.status === 'sent').length,
      failedDeliveries: deliveries.filter(d => d.status === 'failed').length
    };
  }

  /**
   * Get delivery history
   */
  getDeliveryHistory(limit: number = 50): DeliveryResponse[] {
    const deliveries = Array.from(this.deliveryHistory.values());
    return deliveries
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Clear delivery history
   */
  clearHistory(): void {
    this.deliveryHistory.clear();
  }
}

export default SimpleDeliveryService;
