"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performance = exports.validation = exports.utils = exports.logger = void 0;
exports.logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
exports.utils = {
    sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
    generateId: () => Math.random().toString(36).substring(2, 15),
    formatDate: (date) => date.toISOString(),
};
exports.validation = {
    isEmail: (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
    isUrl: (url) => {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    },
    isPhoneNumber: (phone) => /^\+?[\d\s\-\(\)]+$/.test(phone),
};
exports.performance = {
    timer: () => {
        const start = Date.now();
        return {
            end: () => Date.now() - start,
        };
    },
    measure: async (fn, label) => {
        const start = Date.now();
        const result = await fn();
        const duration = Date.now() - start;
        if (label) {
            exports.logger.debug(`Performance: ${label} took ${duration}ms`);
        }
        return { result, duration };
    },
};
//# sourceMappingURL=index.js.map