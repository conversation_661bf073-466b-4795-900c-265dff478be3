"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = require("dotenv");
const winston_1 = __importDefault(require("winston"));
const AIAnalyzer_1 = require("./engines/AIAnalyzer");
const ScoreCalculator_1 = require("./scoring/ScoreCalculator");
const InsightGenerator_1 = require("./insights/InsightGenerator");
const RecommendationEngine_1 = require("./recommendations/RecommendationEngine");
(0, dotenv_1.config)();
const logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
        })
    ]
});
const aiAnalyzer = new AIAnalyzer_1.AIAnalyzer();
const scoreCalculator = new ScoreCalculator_1.ScoreCalculator();
const insightGenerator = new InsightGenerator_1.InsightGenerator();
const recommendationEngine = new RecommendationEngine_1.RecommendationEngine();
const app = (0, express_1.default)();
const PORT = process.env.ANALYZER_PORT || 3002;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use((0, compression_1.default)());
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            service: 'GMB Analyzer',
            environment: process.env.NODE_ENV || 'development'
        }
    });
});
app.post('/api/analyze/business', async (req, res) => {
    try {
        const { businessData, competitorData } = req.body;
        if (!businessData) {
            return res.status(400).json({
                success: false,
                error: { message: 'Business data is required', code: 'MISSING_DATA' }
            });
        }
        logger.info('Starting AI analysis for business', {
            businessId: businessData.id,
            businessName: businessData.businessName
        });
        const analysisResult = await aiAnalyzer.analyzeBusinessProfile(businessData, competitorData);
        const scoreBreakdown = scoreCalculator.calculateOverallScore({
            reviews: businessData.reviews || [],
            rankings: businessData.rankings || [],
            seoFactors: businessData,
            photos: businessData.photos || [],
            posts: businessData.posts || [],
            citations: businessData.citations || [],
            competitors: competitorData || []
        });
        const insights = await insightGenerator.generateInsights(analysisResult, scoreBreakdown);
        const recommendations = await recommendationEngine.generateRecommendations(analysisResult, scoreBreakdown, insights);
        res.json({
            success: true,
            data: {
                analysis: analysisResult,
                scores: scoreBreakdown,
                insights,
                recommendations,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        logger.error('Error in AI analysis', { error: error.message, stack: error.stack });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to analyze business data', code: 'ANALYSIS_ERROR' }
        });
    }
});
app.post('/api/analyze/seo', async (req, res) => {
    try {
        const { businessData } = req.body;
        if (!businessData) {
            return res.status(400).json({
                success: false,
                error: { message: 'Business data is required', code: 'MISSING_DATA' }
            });
        }
        const seoAnalysis = await aiAnalyzer.analyzeSEOFactors(businessData);
        res.json({
            success: true,
            data: seoAnalysis
        });
    }
    catch (error) {
        logger.error('Error in SEO analysis', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to analyze SEO factors', code: 'SEO_ANALYSIS_ERROR' }
        });
    }
});
app.post('/api/analyze/sentiment', async (req, res) => {
    try {
        const { reviews } = req.body;
        if (!reviews || !Array.isArray(reviews)) {
            return res.status(400).json({
                success: false,
                error: { message: 'Reviews array is required', code: 'MISSING_DATA' }
            });
        }
        const sentimentAnalysis = await aiAnalyzer.analyzeReviewSentiment(reviews);
        res.json({
            success: true,
            data: sentimentAnalysis
        });
    }
    catch (error) {
        logger.error('Error in sentiment analysis', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to analyze review sentiment', code: 'SENTIMENT_ANALYSIS_ERROR' }
        });
    }
});
app.post('/api/calculate/score', async (req, res) => {
    try {
        const { analysisData } = req.body;
        if (!analysisData) {
            return res.status(400).json({
                success: false,
                error: { message: 'Analysis data is required', code: 'MISSING_DATA' }
            });
        }
        const scoreBreakdown = scoreCalculator.calculateOverallScore(analysisData);
        res.json({
            success: true,
            data: scoreBreakdown
        });
    }
    catch (error) {
        logger.error('Error in score calculation', { error: error.message });
        res.status(500).json({
            success: false,
            error: { message: 'Failed to calculate scores', code: 'SCORE_CALCULATION_ERROR' }
        });
    }
});
app.get('/api/docs', (req, res) => {
    res.json({
        success: true,
        data: {
            service: 'GMB Analyzer API',
            version: '1.0.0',
            endpoints: {
                health: '/health',
                analyze: '/api/analyze/business',
                seo: '/api/analyze/seo',
                sentiment: '/api/analyze/sentiment',
                score: '/api/calculate/score'
            },
            description: 'AI-powered analysis engine for Google Business Profile audits'
        }
    });
});
app.use((err, req, res, next) => {
    logger.error('Unhandled error', { error: err.message, stack: err.stack });
    res.status(500).json({
        success: false,
        error: { message: 'Internal server error', code: 'INTERNAL_ERROR' }
    });
});
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    process.exit(0);
});
process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    process.exit(0);
});
app.listen(PORT, () => {
    logger.info(`GMB Analyzer service started on port ${PORT}`);
    logger.info('Available endpoints:');
    logger.info('  GET  /health - Health check');
    logger.info('  POST /api/analyze/business - Full business analysis');
    logger.info('  POST /api/analyze/seo - SEO factor analysis');
    logger.info('  POST /api/analyze/sentiment - Review sentiment analysis');
    logger.info('  POST /api/calculate/score - Score calculation');
    logger.info('  GET  /api/docs - API documentation');
});
exports.default = app;
//# sourceMappingURL=index.js.map