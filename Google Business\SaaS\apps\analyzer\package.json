{"name": "@gmb-audit/analyzer", "version": "1.0.0", "description": "AI Analysis Engine for GMB audit generator", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@sentry/node": "^7.81.1", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "openai": "^4.20.1", "sentiment": "^5.0.2", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.20.3", "typescript": "^5.3.0"}}