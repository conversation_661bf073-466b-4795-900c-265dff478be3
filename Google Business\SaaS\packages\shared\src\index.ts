// Simple shared utilities for Phase 1
export const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

export const utils = {
  sleep: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  generateId: () => Math.random().toString(36).substring(2, 15),
  formatDate: (date: Date) => date.toISOString(),
};

// Simple validation utilities
export const validation = {
  isEmail: (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
  isUrl: (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
  isPhoneNumber: (phone: string) => /^\+?[\d\s\-\(\)]+$/.test(phone),
};

// Simple performance utilities
export const performance = {
  timer: () => {
    const start = Date.now();
    return {
      end: () => Date.now() - start,
    };
  },
  measure: async <T>(fn: () => Promise<T>, label?: string): Promise<{ result: T; duration: number }> => {
    const start = Date.now();
    const result = await fn();
    const duration = Date.now() - start;
    if (label) {
      logger.debug(`Performance: ${label} took ${duration}ms`);
    }
    return { result, duration };
  },
};
