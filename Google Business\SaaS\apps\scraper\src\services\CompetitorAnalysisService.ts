import { GMBScrapingService, GMBData } from './GMBScrapingService';
import { DataValidationService } from './DataValidationService';
import axios from 'axios';

// Simple logger for Phase 2
const logger = {
  info: (message: string, meta?: any) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
  warn: (message: string, meta?: any) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
  error: (message: string, meta?: any) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
  debug: (message: string, meta?: any) => console.log(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};

export interface CompetitorSearchRequest {
  businessName: string;
  address: string;
  category: string;
  radius?: number; // in miles, default 5
  maxCompetitors?: number; // default 10
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface CompetitorData extends GMBData {
  competitorId: string;
  distance?: number; // distance from target business in miles
  localRanking?: number; // position in local search results
  competitiveStrength: number; // 0-100 score
  threatLevel: 'low' | 'medium' | 'high';
  advantages: string[];
  weaknesses: string[];
}

export interface CompetitorAnalysisResult {
  targetBusiness: {
    name: string;
    address: string;
    category: string;
  };
  competitors: CompetitorData[];
  analysis: {
    totalCompetitors: number;
    averageRating: number;
    averageReviewCount: number;
    marketSaturation: 'low' | 'medium' | 'high';
    competitiveGaps: string[];
    recommendations: string[];
  };
  scrapedAt: Date;
}

export class CompetitorAnalysisService {
  private scrapingService: GMBScrapingService;
  private validationService: DataValidationService;
  private googlePlacesApiKey: string;
  private perplexityApiKey: string;

  constructor() {
    this.scrapingService = new GMBScrapingService();
    this.validationService = new DataValidationService();
    this.googlePlacesApiKey = process.env.GOOGLE_MAPS_API_KEY || '';
    this.perplexityApiKey = process.env.PERPLEXITY_API_KEY || '';

    if (!this.googlePlacesApiKey) {
      logger.warn('Google Places API key not found. Competitor discovery will be limited.');
    }
    if (!this.perplexityApiKey) {
      logger.warn('Perplexity API key not found. Live verification will be limited.');
    }
  }

  public async analyzeCompetitors(request: CompetitorSearchRequest): Promise<CompetitorAnalysisResult> {
    logger.info('Starting competitor analysis', { 
      businessName: request.businessName,
      category: request.category 
    });

    try {
      // Find competitors
      const competitors = await this.findCompetitors(request);

      // Analyze competitive landscape
      const analysis = this.analyzeCompetitiveLandscape(competitors, request);

      const result: CompetitorAnalysisResult = {
        targetBusiness: {
          name: request.businessName,
          address: request.address,
          category: request.category,
        },
        competitors,
        analysis,
        scrapedAt: new Date(),
      };

      logger.info('Competitor analysis completed', {
        businessName: request.businessName,
        competitorsFound: competitors.length,
        marketSaturation: analysis.marketSaturation,
      });

      return result;

    } catch (error: any) {
      logger.error('Error in competitor analysis', { 
        businessName: request.businessName,
        error: error.message 
      });
      throw error;
    }
  }

  private async findCompetitors(request: CompetitorSearchRequest): Promise<CompetitorData[]> {
    const competitors: CompetitorData[] = [];
    const maxCompetitors = request.maxCompetitors || 10;
    const radius = request.radius || 5;

    logger.debug('Searching for competitors', {
      category: request.category,
      radius: `${radius} miles`,
      maxCompetitors
    });

    try {
      // Auto-geocode business address if coordinates not provided
      if (!request.coordinates && this.googlePlacesApiKey) {
        logger.info('Auto-geocoding business address for competitor search', { address: request.address });
        request.coordinates = await this.geocodeAddress(request.address);
      }

      // Search for competitors using Google search
      const searchQueries = this.generateSearchQueries(request);
      
      for (const query of searchQueries) {
        if (competitors.length >= maxCompetitors) break;

        const searchResults = await this.searchCompetitors(query, request);
        
        for (const result of searchResults) {
          if (competitors.length >= maxCompetitors) break;
          
          // Skip if it's the same business
          if (this.isSameBusiness(result, request)) {
            continue;
          }

          // Check if already found
          if (competitors.some(c => this.isSameBusiness(result, { businessName: c.businessName, address: c.address }))) {
            continue;
          }

          const competitorData = await this.enrichCompetitorData(result, competitors.length + 1);
          competitors.push(competitorData);
        }
      }

      logger.info('Competitors found', { count: competitors.length });
      return competitors;

    } catch (error: any) {
      logger.error('Error finding competitors', { error: error.message });
      throw error;
    }
  }

  private generateSearchQueries(request: CompetitorSearchRequest): string[] {
    const { category, address } = request;
    const location = this.extractLocationFromAddress(address);

    return [
      `${category} near ${location}`,
      `best ${category} in ${location}`,
      `top ${category} ${location}`,
      `${category} services ${location}`,
      `local ${category} ${location}`,
    ];
  }

  private extractLocationFromAddress(address: string): string {
    // Extract city and state from address
    const parts = address.split(',').map(p => p.trim());
    if (parts.length >= 2) {
      return `${parts[parts.length - 2]}, ${parts[parts.length - 1]}`;
    }
    return address;
  }

  private async searchCompetitors(query: string, request: CompetitorSearchRequest): Promise<Partial<GMBData>[]> {
    logger.debug('Searching competitors with query', { query });

    try {
      // Use Google Places API for real competitor discovery
      if (this.googlePlacesApiKey && request.coordinates) {
        return await this.searchCompetitorsWithGooglePlaces(request);
      }

      // Fallback to Perplexity Sonar for live web search
      if (this.perplexityApiKey) {
        return await this.searchCompetitorsWithPerplexity(query, request);
      }

      // If no APIs available, return transparent message
      logger.warn('No API keys available for competitor discovery', { query });
      return [{
        businessName: 'Real competitor data requires API integration',
        address: 'Google Places API or Perplexity Sonar needed',
        rating: 0,
        reviewCount: 0,
        phone: 'N/A',
        website: 'N/A',
        isPlaceholder: true,
        message: 'Upgrade to Pro plan for real competitor insights'
      }];

    } catch (error: any) {
      logger.error('Error searching competitors', { error: error.message, query });
      return [{
        businessName: 'Competitor search failed',
        address: 'Error occurred during competitor discovery',
        rating: 0,
        reviewCount: 0,
        phone: 'N/A',
        website: 'N/A',
        isError: true,
        errorMessage: error.message
      }];
    }
  }

  private isSameBusiness(business1: Partial<GMBData>, business2: { businessName: string; address: string }): boolean {
    const name1 = business1.businessName?.toLowerCase().trim() || '';
    const name2 = business2.businessName.toLowerCase().trim();
    const addr1 = business1.address?.toLowerCase().trim() || '';
    const addr2 = business2.address.toLowerCase().trim();

    // Check if names are very similar (allowing for minor variations)
    const nameSimilarity = this.calculateStringSimilarity(name1, name2);
    const addressSimilarity = this.calculateStringSimilarity(addr1, addr2);

    return nameSimilarity > 0.8 || addressSimilarity > 0.8;
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1;

    const editDistance = this.calculateLevenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private async enrichCompetitorData(basicData: Partial<GMBData>, ranking: number): Promise<CompetitorData> {
    logger.debug('Enriching competitor data', { businessName: basicData.businessName });

    try {
      // Validate and clean the data
      const validationResult = await this.validationService.validateAndClean(basicData);
      const cleanedData = validationResult.cleanedData;

      // Calculate competitive metrics
      const competitiveStrength = this.calculateCompetitiveStrength(cleanedData);
      const threatLevel = this.assessThreatLevel(competitiveStrength, cleanedData);
      const { advantages, weaknesses } = this.analyzeCompetitorStrengthsWeaknesses(cleanedData);

      const competitorData: CompetitorData = {
        ...cleanedData,
        competitorId: `comp-${Date.now()}-${this.generateSecureId()}`,
        localRanking: ranking,
        competitiveStrength,
        threatLevel,
        advantages,
        weaknesses,
        scrapedAt: new Date(),
      } as CompetitorData;

      return competitorData;

    } catch (error: any) {
      logger.error('Error enriching competitor data', { 
        businessName: basicData.businessName,
        error: error.message 
      });
      throw error;
    }
  }

  private calculateCompetitiveStrength(data: Partial<GMBData>): number {
    let score = 0;

    // Rating contribution (40% of score)
    if (data.rating) {
      score += (data.rating / 5) * 40;
    }

    // Review count contribution (30% of score)
    if (data.reviewCount) {
      const reviewScore = Math.min(data.reviewCount / 100, 1) * 30; // Cap at 100 reviews for full score
      score += reviewScore;
    }

    // Online presence contribution (20% of score)
    if (data.website) score += 10;
    if (data.photos && data.photos.length > 0) score += 5;
    if (data.posts && data.posts.length > 0) score += 5;

    // Completeness contribution (10% of score)
    const completenessFields = [data.phone, data.address, data.hours];
    const completeness = completenessFields.filter(field => field).length / completenessFields.length;
    score += completeness * 10;

    return Math.round(Math.min(score, 100));
  }

  private assessThreatLevel(competitiveStrength: number, data: Partial<GMBData>): 'low' | 'medium' | 'high' {
    if (competitiveStrength >= 80) return 'high';
    if (competitiveStrength >= 60) return 'medium';
    return 'low';
  }

  private analyzeCompetitorStrengthsWeaknesses(data: Partial<GMBData>): { advantages: string[]; weaknesses: string[] } {
    const advantages: string[] = [];
    const weaknesses: string[] = [];

    // Analyze rating
    if (data.rating) {
      if (data.rating >= 4.5) advantages.push('Excellent customer rating');
      else if (data.rating >= 4.0) advantages.push('Good customer rating');
      else if (data.rating < 3.5) weaknesses.push('Below average rating');
    } else {
      weaknesses.push('No rating available');
    }

    // Analyze reviews
    if (data.reviewCount) {
      if (data.reviewCount >= 100) advantages.push('Strong review volume');
      else if (data.reviewCount >= 50) advantages.push('Moderate review volume');
      else if (data.reviewCount < 20) weaknesses.push('Limited customer feedback');
    } else {
      weaknesses.push('No customer reviews');
    }

    // Analyze online presence
    if (data.website) advantages.push('Has website');
    else weaknesses.push('No website');

    if (data.photos && data.photos.length > 10) advantages.push('Rich photo gallery');
    else if (!data.photos || data.photos.length === 0) weaknesses.push('No photos');

    // Analyze contact information
    if (!data.phone) weaknesses.push('No phone number listed');
    if (!data.address) weaknesses.push('Incomplete address information');

    return { advantages, weaknesses };
  }

  private analyzeCompetitiveLandscape(competitors: CompetitorData[], request: CompetitorSearchRequest): CompetitorAnalysisResult['analysis'] {
    const totalCompetitors = competitors.length;
    
    const ratings = competitors.map(c => c.rating).filter(r => r !== undefined) as number[];
    const reviewCounts = competitors.map(c => c.reviewCount).filter(r => r !== undefined) as number[];
    
    const averageRating = ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0;
    const averageReviewCount = reviewCounts.length > 0 ? reviewCounts.reduce((a, b) => a + b, 0) / reviewCounts.length : 0;

    // Determine market saturation
    let marketSaturation: 'low' | 'medium' | 'high' = 'low';
    if (totalCompetitors >= 15) marketSaturation = 'high';
    else if (totalCompetitors >= 8) marketSaturation = 'medium';

    // Identify competitive gaps
    const competitiveGaps = this.identifyCompetitiveGaps(competitors);

    // Generate recommendations
    const recommendations = this.generateRecommendations(competitors, averageRating, averageReviewCount);

    return {
      totalCompetitors,
      averageRating: Math.round(averageRating * 10) / 10,
      averageReviewCount: Math.round(averageReviewCount),
      marketSaturation,
      competitiveGaps,
      recommendations,
    };
  }

  private identifyCompetitiveGaps(competitors: CompetitorData[]): string[] {
    const gaps: string[] = [];

    // Check for common weaknesses
    const noWebsiteCount = competitors.filter(c => !c.website).length;
    const lowRatingCount = competitors.filter(c => c.rating && c.rating < 4.0).length;
    const lowReviewCount = competitors.filter(c => c.reviewCount && c.reviewCount < 50).length;

    if (noWebsiteCount > competitors.length * 0.3) {
      gaps.push('Many competitors lack professional websites');
    }

    if (lowRatingCount > competitors.length * 0.4) {
      gaps.push('Opportunity to excel with superior customer service');
    }

    if (lowReviewCount > competitors.length * 0.5) {
      gaps.push('Market has low customer engagement - opportunity for review generation');
    }

    return gaps;
  }

  private generateRecommendations(competitors: CompetitorData[], avgRating: number, avgReviewCount: number): string[] {
    const recommendations: string[] = [];

    // Rating-based recommendations
    if (avgRating < 4.0) {
      recommendations.push('Focus on exceptional customer service to stand out with higher ratings');
    }

    // Review-based recommendations
    if (avgReviewCount < 50) {
      recommendations.push('Implement aggressive review generation strategy');
    }

    // Online presence recommendations
    const websiteGap = competitors.filter(c => !c.website).length / competitors.length;
    if (websiteGap > 0.3) {
      recommendations.push('Invest in professional website to gain competitive advantage');
    }

    // Photo recommendations
    const photoGap = competitors.filter(c => !c.photos || c.photos.length < 5).length / competitors.length;
    if (photoGap > 0.4) {
      recommendations.push('Create comprehensive photo gallery to showcase business');
    }

    // General recommendations
    recommendations.push('Monitor competitor activities and pricing regularly');
    recommendations.push('Focus on unique value propositions to differentiate');

    return recommendations;
  }

  /**
   * Search competitors using Google Places API
   */
  private async searchCompetitorsWithGooglePlaces(request: CompetitorSearchRequest): Promise<Partial<GMBData>[]> {
    logger.info('Searching competitors with Google Places API', {
      category: request.category,
      coordinates: request.coordinates
    });

    try {
      const { latitude, longitude } = request.coordinates!;
      const radius = (request.radius || 5) * 1609.34; // Convert miles to meters

      // Use text search for better category matching
      const searchQuery = `${request.category} near ${request.address}`;

      const url = 'https://maps.googleapis.com/maps/api/place/textsearch/json';
      const params = {
        query: searchQuery,
        location: `${latitude},${longitude}`,
        radius: radius.toString(),
        key: this.googlePlacesApiKey
      };

      const response = await axios.get(url, { params });

      if (response.data.status !== 'OK') {
        logger.error('Google Places API error', { status: response.data.status });
        return [];
      }

      const competitors = response.data.results
        .filter((place: any) => {
          // Filter out the target business itself
          return !this.isSameBusiness(
            { businessName: place.name, address: place.vicinity },
            { businessName: request.businessName, address: request.address }
          );
        })
        .slice(0, request.maxCompetitors || 10)
        .map((place: any) => ({
          businessName: place.name,
          address: place.formatted_address || place.vicinity,
          rating: place.rating || 0,
          reviewCount: place.user_ratings_total || 0,
          phone: 'N/A', // Requires Place Details API call
          website: 'N/A', // Requires Place Details API call
          placeId: place.place_id,
          photoReference: place.photos?.[0]?.photo_reference,
          priceLevel: place.price_level,
          isVerified: true,
          dataSource: 'Google Places API'
        }));

      logger.info(`Found ${competitors.length} real competitors via Google Places API`);
      return competitors;

    } catch (error: any) {
      logger.error('Google Places API search failed', { error: error.message });
      return [];
    }
  }

  /**
   * Search competitors using Perplexity Sonar for live web search
   */
  private async searchCompetitorsWithPerplexity(query: string, request: CompetitorSearchRequest): Promise<Partial<GMBData>[]> {
    logger.info('Searching competitors with Perplexity Sonar', { query });

    try {
      const location = this.extractLocationFromAddress(request.address);
      const searchQuery = `Find real ${request.category} businesses near ${location}. Include business name, address, phone, website, and ratings.`;

      const response = await axios.post('https://api.perplexity.ai/chat/completions', {
        model: 'llama-3.1-sonar-small-128k-online',
        messages: [
          {
            role: 'system',
            content: 'You are a business research assistant. Provide accurate, real business information in JSON format. Never fabricate data.'
          },
          {
            role: 'user',
            content: searchQuery
          }
        ],
        max_tokens: 2000,
        temperature: 0.1
      }, {
        headers: {
          'Authorization': `Bearer ${this.perplexityApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        logger.warn('No content received from Perplexity Sonar');
        return [];
      }

      // Parse the response to extract business data
      const competitors = this.parsePerplexityResponse(content, request);
      logger.info(`Found ${competitors.length} competitors via Perplexity Sonar`);
      return competitors;

    } catch (error: any) {
      logger.error('Perplexity Sonar search failed', { error: error.message });
      return [];
    }
  }

  /**
   * Parse Perplexity response to extract business data
   */
  private parsePerplexityResponse(content: string, request: CompetitorSearchRequest): Partial<GMBData>[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const businesses = JSON.parse(jsonMatch[0]);
        return businesses
          .filter((business: any) => business.name && business.address)
          .filter((business: any) =>
            !this.isSameBusiness(
              { businessName: business.name, address: business.address },
              { businessName: request.businessName, address: request.address }
            )
          )
          .slice(0, request.maxCompetitors || 10)
          .map((business: any) => ({
            businessName: business.name,
            address: business.address,
            rating: business.rating || 0,
            reviewCount: business.reviewCount || 0,
            phone: business.phone || 'N/A',
            website: business.website || 'N/A',
            isVerified: true,
            dataSource: 'Perplexity Sonar Live Search'
          }));
      }

      // Fallback: extract business names from text
      const businessNames = content.match(/\d+\.\s*([^:\n]+)/g);
      if (businessNames) {
        return businessNames
          .slice(0, request.maxCompetitors || 10)
          .map((match, index) => ({
            businessName: match.replace(/\d+\.\s*/, '').trim(),
            address: `${this.extractLocationFromAddress(request.address)} (Location verified via web search)`,
            rating: 0,
            reviewCount: 0,
            phone: 'N/A',
            website: 'N/A',
            isVerified: false,
            dataSource: 'Perplexity Sonar Text Extraction',
            note: 'Partial data - requires additional verification'
          }));
      }

      return [];
    } catch (error: any) {
      logger.error('Failed to parse Perplexity response', { error: error.message });
      return [];
    }
  }

  /**
   * Map business category to Google Places API type
   */
  private mapCategoryToPlaceType(category: string): string {
    const categoryMap: { [key: string]: string } = {
      'dental': 'dentist',
      'restaurant': 'restaurant',
      'salon': 'beauty_salon',
      'beauty': 'beauty_salon',
      'training': 'school',
      'academy': 'school',
      'education': 'school',
      'clinic': 'hospital',
      'pharmacy': 'pharmacy',
      'gym': 'gym',
      'hotel': 'lodging',
      'lawyer': 'lawyer',
      'accountant': 'accounting'
    };

    const lowerCategory = category.toLowerCase();
    for (const [key, value] of Object.entries(categoryMap)) {
      if (lowerCategory.includes(key)) {
        return value;
      }
    }

    return 'establishment'; // Default fallback
  }

  /**
   * Generate secure ID without Math.random()
   */
  private generateSecureId(): string {
    const timestamp = Date.now().toString(36);
    const randomBytes = Array.from(crypto.getRandomValues(new Uint8Array(6)))
      .map(b => b.toString(36))
      .join('');
    return timestamp + randomBytes;
  }

  /**
   * Geocode business address to get coordinates for Google Places API
   */
  private async geocodeAddress(address: string): Promise<{ latitude: number; longitude: number } | undefined> {
    try {
      logger.info('Geocoding address for competitor search', { address });

      const url = 'https://maps.googleapis.com/maps/api/geocode/json';
      const response = await axios.get(url, {
        params: {
          address: address,
          key: this.googlePlacesApiKey
        }
      });

      if (response.data.status !== 'OK' || !response.data.results.length) {
        logger.warn('Geocoding failed', { status: response.data.status, address });
        return undefined;
      }

      const location = response.data.results[0].geometry.location;
      logger.info('Successfully geocoded address', {
        address,
        coordinates: { latitude: location.lat, longitude: location.lng },
        accuracy: response.data.results[0].geometry.location_type
      });

      return {
        latitude: location.lat,
        longitude: location.lng
      };

    } catch (error: any) {
      logger.error('Geocoding API error', { error: error.message, address });
      return undefined;
    }
  }

  public async close(): Promise<void> {
    await this.scrapingService.close();
  }
}
