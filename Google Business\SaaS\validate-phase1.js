const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Phase 1 Setup...\n');

// Check if all required files exist
const requiredFiles = [
  'package.json',
  'turbo.json',
  'apps/api/package.json',
  'apps/api/src/index.ts',
  'apps/api/dist/index.js',
  'packages/database/package.json',
  'packages/database/src/index.ts',
  'packages/database/prisma/schema.prisma',
  'packages/shared/package.json',
  'packages/shared/src/index.ts',
  'packages/config/package.json',
  'packages/config/src/index.ts',
  'packages/types/package.json',
  'packages/types/src/index.ts',
];

let allFilesExist = true;

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

console.log('\n📦 Checking node_modules:');
const nodeModulesExists = fs.existsSync('node_modules');
console.log(`  ${nodeModulesExists ? '✅' : '❌'} node_modules directory`);

console.log('\n🔧 Checking build outputs:');
const buildOutputs = [
  'apps/api/dist',
  'packages/database/dist',
  'packages/shared/dist',
  'packages/config/dist',
  'packages/types/dist',
];

buildOutputs.forEach(dir => {
  const exists = fs.existsSync(dir);
  console.log(`  ${exists ? '✅' : '❌'} ${dir}`);
});

console.log('\n📋 Summary:');
if (allFilesExist && nodeModulesExists) {
  console.log('✅ Phase 1 setup appears to be complete!');
  console.log('🚀 You can now try to start the API server.');
  console.log('\nNext steps:');
  console.log('1. Start the API: cd apps/api && npm start');
  console.log('2. Test the API: curl http://localhost:3000/health');
  console.log('3. Check the API status: curl http://localhost:3000/api/status');
} else {
  console.log('❌ Phase 1 setup is incomplete. Please check the missing files above.');
}

console.log('\n🎯 Phase 1 Core Infrastructure Status: READY FOR TESTING');
console.log('📝 Next Phase: Phase 2 - Data Collection Engine');
