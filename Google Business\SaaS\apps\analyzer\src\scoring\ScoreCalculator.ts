import { ScoreBreakdown, AnalysisData } from '../types/analysis';
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [new winston.transports.Console()]
});

export class ScoreCalculator {
  private readonly weights = {
    reviews: 0.25,      // Review quantity & quality
    visibility: 0.20,   // Local pack presence
    seo: 0.20,         // On-page optimization
    photos: 0.15,      // Photo optimization
    posts: 0.10,       // Google Posts usage
    nap: 0.10          // NAP consistency
  };

  calculateOverallScore(data: AnalysisData): ScoreBreakdown {
    try {
      logger.info('Calculating overall score breakdown');

      const scores = {
        reviews: this.calculateReviewScore(data.reviews || []),
        visibility: this.calculateVisibilityScore(data.rankings || []),
        seo: this.calculateSEOScore(data.seoFactors || {}),
        photos: this.calculatePhotoScore(data.photos || []),
        posts: this.calculatePostsScore(data.posts || []),
        nap: this.calculateNAPScore(data.citations || [])
      };

      const overall = Object.entries(scores)
        .reduce((sum, [key, score]) => 
          sum + (score * this.weights[key as keyof typeof this.weights]), 0
        );

      const grade = this.calculateGrade(overall);

      const result: ScoreBreakdown = {
        overall: Math.round(overall),
        breakdown: scores,
        weights: this.weights,
        grade
      };

      logger.info('Score calculation completed', { overall: result.overall, grade });
      return result;

    } catch (error: any) {
      logger.error('Error calculating scores', { error: error.message });
      return this.getDefaultScoreBreakdown();
    }
  }

  private calculateReviewScore(reviews: any[]): number {
    if (!reviews || reviews.length === 0) return 0;

    const totalReviews = reviews.length;
    const avgRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0) / totalReviews;
    
    // Score based on quantity and quality
    const quantityScore = Math.min(totalReviews * 2, 50); // Max 50 points for quantity
    const qualityScore = (avgRating / 5) * 50; // Max 50 points for quality
    
    // Recent reviews bonus
    const recentReviews = reviews.filter(review => {
      const reviewDate = new Date(review.date || review.createdAt);
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      return reviewDate > threeMonthsAgo;
    }).length;
    
    const recentBonus = Math.min(recentReviews * 2, 10); // Max 10 bonus points
    
    return Math.min(quantityScore + qualityScore + recentBonus, 100);
  }

  private calculateVisibilityScore(rankings: any[]): number {
    if (!rankings || rankings.length === 0) return 0;

    // Calculate average ranking position
    const positions = rankings.map(r => r.position || r.rank || 10).filter(p => p > 0);
    if (positions.length === 0) return 0;

    const avgPosition = positions.reduce((sum, pos) => sum + pos, 0) / positions.length;
    
    // Convert position to score (position 1 = 100, position 10 = 10)
    const positionScore = Math.max(0, 110 - (avgPosition * 10));
    
    // Local pack presence bonus
    const localPackPresence = rankings.filter(r => r.type === 'local_pack' || r.position <= 3).length;
    const localPackBonus = Math.min(localPackPresence * 10, 20);
    
    return Math.min(positionScore + localPackBonus, 100);
  }

  private calculateSEOScore(seoFactors: any): number {
    if (!seoFactors || typeof seoFactors !== 'object') return 0;

    let score = 0;
    let maxScore = 0;

    // Business name optimization (20 points)
    if (seoFactors.businessName) {
      maxScore += 20;
      if (seoFactors.businessName.length > 0 && seoFactors.businessName.length <= 50) {
        score += 20;
      } else if (seoFactors.businessName.length > 0) {
        score += 10;
      }
    }

    // Description optimization (25 points)
    if (seoFactors.description) {
      maxScore += 25;
      const descLength = seoFactors.description.length;
      if (descLength >= 100 && descLength <= 750) {
        score += 25;
      } else if (descLength > 0) {
        score += 15;
      }
    }

    // Categories (20 points)
    if (seoFactors.categories) {
      maxScore += 20;
      const categoryCount = Array.isArray(seoFactors.categories) ? seoFactors.categories.length : 0;
      if (categoryCount >= 1) {
        score += Math.min(categoryCount * 5, 20);
      }
    }

    // Hours completeness (15 points)
    if (seoFactors.hours) {
      maxScore += 15;
      const hoursSet = Object.keys(seoFactors.hours).length;
      score += Math.min(hoursSet * 2, 15);
    }

    // Contact information (20 points)
    maxScore += 20;
    if (seoFactors.phone) score += 10;
    if (seoFactors.website) score += 10;

    return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  }

  private calculatePhotoScore(photos: any[]): number {
    if (!photos || photos.length === 0) return 0;

    const totalPhotos = photos.length;
    const recommendedPhotos = 20;
    
    // Quantity score (60% of total)
    const quantityScore = Math.min((totalPhotos / recommendedPhotos) * 60, 60);
    
    // Diversity score (40% of total)
    const categories = ['exterior', 'interior', 'products', 'team', 'logo'];
    const categoriesPresent = categories.filter(category => 
      photos.some(photo => photo.category === category)
    ).length;
    
    const diversityScore = (categoriesPresent / categories.length) * 40;
    
    return Math.round(quantityScore + diversityScore);
  }

  private calculatePostsScore(posts: any[]): number {
    if (!posts || posts.length === 0) return 0;

    const totalPosts = posts.length;
    
    // Recent posts (last 3 months)
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    
    const recentPosts = posts.filter(post => {
      const postDate = new Date(post.date || post.createdAt);
      return postDate > threeMonthsAgo;
    }).length;
    
    // Score based on recent activity
    const recentScore = Math.min(recentPosts * 10, 70); // Max 70 for recent posts
    
    // Consistency bonus
    const consistencyBonus = recentPosts >= 4 ? 30 : recentPosts * 7.5; // Max 30 for consistency
    
    return Math.round(recentScore + consistencyBonus);
  }

  private calculateNAPScore(citations: any[]): number {
    if (!citations || citations.length === 0) return 50; // Default score if no citation data

    const totalCitations = citations.length;
    
    // Consistency check
    const consistentCitations = citations.filter(citation => 
      citation.consistent === true || citation.accuracy >= 0.9
    ).length;
    
    const consistencyScore = totalCitations > 0 ? (consistentCitations / totalCitations) * 70 : 0;
    
    // Quantity bonus
    const quantityBonus = Math.min(totalCitations * 2, 30);
    
    return Math.round(consistencyScore + quantityBonus);
  }

  private calculateGrade(score: number): ScoreBreakdown['grade'] {
    if (score >= 97) return 'A+';
    if (score >= 93) return 'A';
    if (score >= 90) return 'B+';
    if (score >= 87) return 'B';
    if (score >= 83) return 'C+';
    if (score >= 80) return 'C';
    if (score >= 77) return 'D+';
    if (score >= 70) return 'D';
    return 'F';
  }

  private getDefaultScoreBreakdown(): ScoreBreakdown {
    return {
      overall: 0,
      breakdown: {
        reviews: 0,
        visibility: 0,
        seo: 0,
        photos: 0,
        posts: 0,
        nap: 0
      },
      weights: this.weights,
      grade: 'F'
    };
  }

  // Utility method to get score interpretation
  getScoreInterpretation(score: number): string {
    if (score >= 90) return 'Excellent - Your GMB profile is highly optimized';
    if (score >= 80) return 'Good - Your profile is well-optimized with room for improvement';
    if (score >= 70) return 'Fair - Your profile needs significant optimization';
    if (score >= 60) return 'Poor - Your profile requires immediate attention';
    return 'Critical - Your profile needs comprehensive optimization';
  }

  // Method to identify top improvement areas
  getTopImprovementAreas(breakdown: ScoreBreakdown): Array<{area: string, score: number, weight: number, impact: number}> {
    const areas = Object.entries(breakdown.breakdown).map(([area, score]) => ({
      area,
      score,
      weight: this.weights[area as keyof typeof this.weights],
      impact: (100 - score) * this.weights[area as keyof typeof this.weights]
    }));

    return areas
      .sort((a, b) => b.impact - a.impact)
      .slice(0, 3);
  }
}
