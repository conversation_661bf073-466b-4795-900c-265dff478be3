{"name": "@gmb-audit/types", "version": "1.0.0", "description": "TypeScript type definitions for GMB audit generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}}