{"name": "GBP Audit Complete Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "gbp-audit", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "gbp-audit-webhook"}, {"parameters": {"jsCode": "// Input validation and data preparation\nconst input = $input.first().json;\n\n// Validate required fields\nif (!input.businessName || !input.city || !input.country) {\n  return [{\n    json: {\n      error: 'Missing required fields: businessName, city, country',\n      success: false\n    }\n  }];\n}\n\n// Generate unique audit ID\nconst auditId = `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n// Prepare standardized data structure\nconst auditData = {\n  auditId,\n  timestamp: new Date().toISOString(),\n  businessInfo: {\n    name: input.businessName.trim(),\n    city: input.city.trim(),\n    state: input.state?.trim() || '',\n    country: input.country.trim(),\n    category: input.category?.trim() || 'business',\n    phone: input.phone?.trim() || '',\n    website: input.website?.trim() || ''\n  },\n  leadInfo: {\n    contactName: input.contactName?.trim() || '',\n    email: input.email?.trim() || '',\n    phone: input.contactPhone?.trim() || '',\n    company: input.company?.trim() || ''\n  },\n  searchQueries: [\n    `${input.businessName} ${input.city}`,\n    `${input.category || 'business'} ${input.city}`,\n    `${input.category || 'business'} near ${input.city}`,\n    `best ${input.category || 'business'} ${input.city}`,\n    `top ${input.category || 'business'} ${input.city}`\n  ],\n  status: 'initiated',\n  success: true\n};\n\nreturn [{ json: auditData }];"}, "id": "input-validator", "name": "Input Validator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition1", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "validation-check", "name": "Validation Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Mock Google My Business API lookup\nconst data = $input.first().json;\n\n// Simulate GMB API response with realistic data\nconst gmbData = {\n  ...data,\n  gmbProfile: {\n    placeId: `ChIJ${Math.random().toString(36).substr(2, 20)}`,\n    isVerified: Math.random() > 0.3,\n    isPublished: Math.random() > 0.1,\n    businessStatus: Math.random() > 0.9 ? 'CLOSED_PERMANENTLY' : 'OPERATIONAL',\n    categories: {\n      primaryCategory: data.businessInfo.category,\n      additionalCategories: [\n        `${data.businessInfo.category} service`,\n        `local ${data.businessInfo.category}`\n      ]\n    },\n    location: {\n      address: `123 Main St, ${data.businessInfo.city}, ${data.businessInfo.state} 12345`,\n      coordinates: {\n        lat: 40.7128 + (Math.random() - 0.5) * 0.1,\n        lng: -74.0060 + (Math.random() - 0.5) * 0.1\n      }\n    },\n    contact: {\n      phone: data.businessInfo.phone || '******-0123',\n      website: data.businessInfo.website || 'https://example.com'\n    },\n    hours: {\n      monday: '9:00 AM - 6:00 PM',\n      tuesday: '9:00 AM - 6:00 PM',\n      wednesday: '9:00 AM - 6:00 PM',\n      thursday: '9:00 AM - 6:00 PM',\n      friday: '9:00 AM - 6:00 PM',\n      saturday: '10:00 AM - 4:00 PM',\n      sunday: 'Closed'\n    },\n    photos: {\n      total: Math.floor(Math.random() * 50) + 5,\n      recent: Math.floor(Math.random() * 10) + 1\n    },\n    reviews: {\n      totalCount: Math.floor(Math.random() * 200) + 10,\n      averageRating: (Math.random() * 2 + 3).toFixed(1),\n      recentCount: Math.floor(Math.random() * 20) + 5\n    },\n    posts: {\n      total: Math.floor(Math.random() * 30),\n      lastPostDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()\n    },\n    completeness: Math.floor(Math.random() * 40) + 60\n  },\n  status: 'gmb_lookup_complete'\n};\n\nreturn [{ json: gmbData }];"}, "id": "gmb-lookup", "name": "Google My Business Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"jsCode": "// NAP Consistency Check across major directories\nconst data = $input.first().json;\nconst businessInfo = data.businessInfo;\n\n// Simulate directory data with variations\nconst directories = [\n  {\n    name: 'Google My Business',\n    businessName: businessInfo.name,\n    address: `123 Main St, ${businessInfo.city}, ${businessInfo.state} 12345`,\n    phone: businessInfo.phone,\n    website: businessInfo.website,\n    consistent: true\n  },\n  {\n    name: 'Yelp',\n    businessName: businessInfo.name + (Math.random() > 0.7 ? ' LLC' : ''),\n    address: `123 Main Street, ${businessInfo.city}, ${businessInfo.state} 12345`,\n    phone: businessInfo.phone.replace(/-/g, '.'),\n    website: businessInfo.website,\n    consistent: Math.random() > 0.3\n  },\n  {\n    name: 'Yellow Pages',\n    businessName: businessInfo.name,\n    address: `123 Main St, ${businessInfo.city}, ${businessInfo.state}`,\n    phone: businessInfo.phone.replace(/-/g, ' '),\n    website: businessInfo.website.replace('https://', 'http://'),\n    consistent: Math.random() > 0.4\n  },\n  {\n    name: 'Facebook',\n    businessName: businessInfo.name,\n    address: `123 Main St, ${businessInfo.city}, ${businessInfo.state} 12345`,\n    phone: businessInfo.phone,\n    website: businessInfo.website + '?ref=facebook',\n    consistent: Math.random() > 0.2\n  },\n  {\n    name: 'Foursquare',\n    businessName: businessInfo.name.toLowerCase(),\n    address: `123 Main St, ${businessInfo.city}, ${businessInfo.state} 12345`,\n    phone: businessInfo.phone,\n    website: businessInfo.website,\n    consistent: Math.random() > 0.5\n  }\n];\n\n// Calculate consistency scores\nconst nameConsistency = directories.filter(d => d.businessName === businessInfo.name).length / directories.length;\nconst addressConsistency = directories.filter(d => d.address.includes('123 Main St')).length / directories.length;\nconst phoneConsistency = directories.filter(d => d.phone === businessInfo.phone).length / directories.length;\nconst websiteConsistency = directories.filter(d => d.website === businessInfo.website).length / directories.length;\n\nconst napData = {\n  ...data,\n  napConsistency: {\n    overallScore: Math.round((nameConsistency + addressConsistency + phoneConsistency + websiteConsistency) * 25),\n    nameConsistency: Math.round(nameConsistency * 100),\n    addressConsistency: Math.round(addressConsistency * 100),\n    phoneConsistency: Math.round(phoneConsistency * 100),\n    websiteConsistency: Math.round(websiteConsistency * 100),\n    directories,\n    issues: directories.filter(d => !d.consistent).length,\n    recommendations: [\n      'Standardize business name format across all directories',\n      'Ensure consistent phone number formatting',\n      'Update address format to match Google My Business',\n      'Use consistent website URL format'\n    ]\n  },\n  status: 'nap_check_complete'\n};\n\nreturn [{ json: napData }];"}, "id": "nap-consistency", "name": "NAP Consistency Check", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"jsCode": "// Competitor Analysis - Find and analyze top 3 local competitors\nconst data = $input.first().json;\nconst businessInfo = data.businessInfo;\n\n// Mock competitor data\nconst competitors = [\n  {\n    name: `Premium ${businessInfo.category} Co`,\n    rating: 4.8,\n    reviewCount: 245,\n    gmbScore: 89,\n    strengths: ['High review count', 'Excellent photos', 'Regular posts'],\n    weaknesses: ['Limited hours', 'No website optimization']\n  },\n  {\n    name: `${businessInfo.city} ${businessInfo.category} Pro`,\n    rating: 4.5,\n    reviewCount: 156,\n    gmbScore: 76,\n    strengths: ['Good local SEO', 'Active social media'],\n    weaknesses: ['Inconsistent NAP', 'Few recent reviews']\n  },\n  {\n    name: `Elite ${businessInfo.category} Services`,\n    rating: 4.2,\n    reviewCount: 89,\n    gmbScore: 68,\n    strengths: ['Professional photos', 'Complete profile'],\n    weaknesses: ['Low review velocity', 'No recent posts']\n  }\n];\n\nconst competitorData = {\n  ...data,\n  competitorAnalysis: {\n    marketPosition: Math.floor(Math.random() * 3) + 2,\n    averageCompetitorScore: Math.round(competitors.reduce((sum, c) => sum + c.gmbScore, 0) / competitors.length),\n    gapAnalysis: {\n      reviewGap: competitors[0].reviewCount - (data.gmbProfile?.reviews?.totalCount || 50),\n      ratingGap: competitors[0].rating - (parseFloat(data.gmbProfile?.reviews?.averageRating) || 4.0),\n      scoreGap: competitors[0].gmbScore - (data.gmbProfile?.completeness || 65)\n    },\n    competitors,\n    opportunities: [\n      'Increase review acquisition velocity',\n      'Improve photo quality and quantity',\n      'Implement regular posting strategy',\n      'Optimize business description and attributes'\n    ]\n  },\n  status: 'competitor_analysis_complete'\n};\n\nreturn [{ json: competitorData }];"}, "id": "competitor-analysis", "name": "Competitor Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 600]}, {"parameters": {"jsCode": "// Local Ranking Analysis for key search terms\nconst data = $input.first().json;\nconst searchQueries = data.searchQueries;\n\n// Mock ranking data\nconst rankings = searchQueries.map((query, index) => ({\n  keyword: query,\n  position: Math.floor(Math.random() * 10) + 1,\n  searchVolume: Math.floor(Math.random() * 1000) + 100,\n  difficulty: Math.floor(Math.random() * 100) + 1,\n  localPack: Math.random() > 0.5,\n  mapPack: Math.random() > 0.3,\n  trends: {\n    lastMonth: Math.floor(Math.random() * 5) - 2,\n    last3Months: Math.floor(Math.random() * 8) - 4\n  }\n}));\n\nconst avgPosition = rankings.reduce((sum, r) => sum + r.position, 0) / rankings.length;\nconst localPackCount = rankings.filter(r => r.localPack).length;\nconst mapPackCount = rankings.filter(r => r.mapPack).length;\n\nconst rankingData = {\n  ...data,\n  rankingAnalysis: {\n    averagePosition: Math.round(avgPosition * 10) / 10,\n    localPackAppearances: localPackCount,\n    mapPackAppearances: mapPackCount,\n    visibilityScore: Math.round((10 - avgPosition) * 10 + localPackCount * 5 + mapPackCount * 3),\n    rankings,\n    recommendations: [\n      avgPosition > 5 ? 'Focus on improving local SEO fundamentals' : 'Maintain current ranking positions',\n      localPackCount < 2 ? 'Optimize for local pack inclusion' : 'Expand local pack presence',\n      'Target long-tail local keywords',\n      'Improve Google My Business optimization'\n    ]\n  },\n  status: 'ranking_analysis_complete'\n};\n\nreturn [{ json: rankingData }];"}, "id": "ranking-analysis", "name": "Local Ranking Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"jsCode": "// Aggregate all audit data and generate comprehensive report\nconst data = $input.first().json;\n\n// Calculate overall GBP health score\nconst gmbScore = data.gmbProfile?.completeness || 65;\nconst napScore = data.napConsistency?.overallScore || 60;\nconst competitorScore = Math.max(0, 100 - (data.competitorAnalysis?.gapAnalysis?.scoreGap || 20));\nconst rankingScore = data.rankingAnalysis?.visibilityScore || 45;\n\nconst overallScore = Math.round((gmbScore * 0.3 + napScore * 0.25 + competitorScore * 0.25 + rankingScore * 0.2));\n\n// Generate priority recommendations\nconst recommendations = [\n  ...data.napConsistency?.recommendations || [],\n  ...data.competitorAnalysis?.opportunities || [],\n  ...data.rankingAnalysis?.recommendations || []\n];\n\n// Create comprehensive audit report\nconst auditReport = {\n  ...data,\n  auditResults: {\n    overallScore,\n    scoreBreakdown: {\n      gmbOptimization: gmbScore,\n      napConsistency: napScore,\n      competitivePosition: competitorScore,\n      localVisibility: rankingScore\n    },\n    criticalIssues: [\n      overallScore < 50 ? 'Overall GBP health is below average' : null,\n      napScore < 60 ? 'NAP consistency needs immediate attention' : null,\n      rankingScore < 40 ? 'Local visibility is significantly impacted' : null\n    ].filter(Boolean),\n    recommendations: recommendations.slice(0, 8),\n    estimatedImpact: {\n      monthlyLeads: Math.floor((100 - overallScore) * 2.5),\n      revenueOpportunity: Math.floor((100 - overallScore) * 150),\n      timeToResults: '30-90 days'\n    }\n  },\n  reportGenerated: new Date().toISOString(),\n  status: 'audit_complete'\n};\n\nreturn [{ json: auditReport }];"}, "id": "report-aggregator", "name": "Report Aggregator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 400]}, {"parameters": {"jsCode": "// Generate professional HTML report\nconst data = $input.first().json;\nconst results = data.auditResults;\nconst business = data.businessInfo;\n\nconst htmlReport = `<!DOCTYPE html>\n<html>\n<head>\n    <title>GBP Audit Report - ${business.name}</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 40px; color: #333; }\n        .header { background: #1a73e8; color: white; padding: 20px; border-radius: 8px; }\n        .score { font-size: 48px; font-weight: bold; text-align: center; margin: 20px 0; }\n        .score.good { color: #34a853; }\n        .score.average { color: #fbbc04; }\n        .score.poor { color: #ea4335; }\n        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }\n        .issue { background: #fef7e0; padding: 10px; margin: 10px 0; border-left: 4px solid #fbbc04; }\n        .recommendation { background: #e8f5e8; padding: 10px; margin: 10px 0; border-left: 4px solid #34a853; }\n        .competitor { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #eee; }\n        .metric { display: inline-block; margin: 10px 20px; text-align: center; }\n        .metric-value { font-size: 24px; font-weight: bold; color: #1a73e8; }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>Google Business Profile Audit Report</h1>\n        <h2>${business.name} - ${business.city}, ${business.state}</h2>\n        <p>Generated on ${new Date(data.reportGenerated).toLocaleDateString()}</p>\n    </div>\n    <div class=\"score ${results.overallScore >= 80 ? 'good' : results.overallScore >= 60 ? 'average' : 'poor'}\">\n        Overall GBP Health Score: ${results.overallScore}/100\n    </div>\n    <div class=\"section\">\n        <h3>Executive Summary</h3>\n        <p>Your Google Business Profile has significant opportunities for improvement. Our analysis identified ${results.criticalIssues.length} critical issues and ${results.recommendations.length} actionable recommendations.</p>\n        <div class=\"metric\">\n            <div class=\"metric-value\">${results.estimatedImpact.monthlyLeads}</div>\n            <div>Potential Monthly Leads</div>\n        </div>\n        <div class=\"metric\">\n            <div class=\"metric-value\">$${results.estimatedImpact.revenueOpportunity}</div>\n            <div>Monthly Revenue Opportunity</div>\n        </div>\n    </div>\n    <div class=\"section\">\n        <h3>Score Breakdown</h3>\n        <p><strong>GMB Optimization:</strong> ${results.scoreBreakdown.gmbOptimization}/100</p>\n        <p><strong>NAP Consistency:</strong> ${results.scoreBreakdown.napConsistency}/100</p>\n        <p><strong>Competitive Position:</strong> ${results.scoreBreakdown.competitivePosition}/100</p>\n        <p><strong>Local Visibility:</strong> ${results.scoreBreakdown.localVisibility}/100</p>\n    </div>\n    <div class=\"section\">\n        <h3>Critical Issues</h3>\n        ${results.criticalIssues.map(issue => `<div class=\"issue\">${issue}</div>`).join('')}\n    </div>\n    <div class=\"section\">\n        <h3>Priority Recommendations</h3>\n        ${results.recommendations.map(rec => `<div class=\"recommendation\">${rec}</div>`).join('')}\n    </div>\n    <div class=\"section\">\n        <h3>Next Steps</h3>\n        <p>Ready to improve your Google Business Profile and capture more local customers? Contact us for a free consultation!</p>\n    </div>\n</body>\n</html>`;\n\nconst reportData = {\n  ...data,\n  htmlReport,\n  reportUrl: `https://reports.example.com/gbp-audit/${data.auditId}`,\n  status: 'report_generated'\n};\n\nreturn [{ json: reportData }];"}, "id": "html-report-generator", "name": "HTML Report Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 400]}, {"parameters": {"jsCode": "// Lead Capture and Scoring System\nconst data = $input.first().json;\nconst leadInfo = data.leadInfo;\nconst auditResults = data.auditResults;\n\n// Calculate lead score based on multiple factors\nlet leadScore = 0;\n\n// Business size indicators\nif (data.gmbProfile?.reviews?.totalCount > 100) leadScore += 20;\nelse if (data.gmbProfile?.reviews?.totalCount > 50) leadScore += 15;\nelse leadScore += 10;\n\n// Audit results (inverse scoring - worse results = better lead)\nif (auditResults.overallScore < 50) leadScore += 30;\nelse if (auditResults.overallScore < 70) leadScore += 20;\nelse leadScore += 10;\n\n// Contact information quality\nif (leadInfo.email && leadInfo.email.includes('@')) leadScore += 15;\nif (leadInfo.phone && leadInfo.phone.length > 10) leadScore += 10;\nif (leadInfo.contactName && leadInfo.contactName.length > 2) leadScore += 10;\nif (leadInfo.company && leadInfo.company.length > 2) leadScore += 5;\n\n// Determine lead priority\nlet priority = 'Low';\nif (leadScore >= 70) priority = 'High';\nelse if (leadScore >= 50) priority = 'Medium';\n\nconst leadData = {\n  ...data,\n  leadCapture: {\n    leadId: `lead_${data.auditId}`,\n    score: leadScore,\n    priority,\n    qualification: {\n      hasEmail: !!leadInfo.email,\n      hasPhone: !!leadInfo.phone,\n      hasContactName: !!leadInfo.contactName,\n      businessSize: data.gmbProfile?.reviews?.totalCount > 50 ? 'Medium-Large' : 'Small',\n      urgency: auditResults.overallScore < 60 ? 'High' : 'Medium'\n    },\n    followUpSequence: [\n      { day: 0, action: 'Send audit report', status: 'pending' },\n      { day: 1, action: 'Follow-up email with insights', status: 'scheduled' },\n      { day: 3, action: 'Educational content delivery', status: 'scheduled' },\n      { day: 7, action: 'Consultation booking reminder', status: 'scheduled' }\n    ],\n    tags: [\n      `score-${Math.floor(leadScore/10)*10}`,\n      `priority-${priority.toLowerCase()}`,\n      `industry-${data.businessInfo.category}`,\n      `location-${data.businessInfo.city}`,\n      auditResults.overallScore < 50 ? 'high-opportunity' : 'standard-opportunity'\n    ]\n  },\n  status: 'lead_captured'\n};\n\nreturn [{ json: leadData }];"}, "id": "lead-capture", "name": "Lead Capture System", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"auditId\": $json.auditId,\n  \"businessName\": $json.businessInfo.name,\n  \"overallScore\": $json.auditResults.overallScore,\n  \"reportUrl\": $json.reportUrl,\n  \"leadScore\": $json.leadCapture.score,\n  \"priority\": $json.leadCapture.priority,\n  \"criticalIssues\": $json.auditResults.criticalIssues.length,\n  \"recommendations\": $json.auditResults.recommendations.length,\n  \"estimatedImpact\": $json.auditResults.estimatedImpact,\n  \"message\": \"GBP audit completed successfully. Report generated and lead captured.\",\n  \"nextSteps\": [\n    \"Review the comprehensive audit report\",\n    \"Implement priority recommendations\",\n    \"Schedule a consultation for detailed strategy\",\n    \"Monitor improvements over 30-90 days\"\n  ],\n  \"timestamp\": $json.reportGenerated\n} }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": $json.error,\n  \"message\": \"Audit request failed validation. Please check required fields and try again.\",\n  \"requiredFields\": [\n    \"businessName\",\n    \"city\",\n    \"country\"\n  ],\n  \"optionalFields\": [\n    \"state\",\n    \"category\",\n    \"phone\",\n    \"website\",\n    \"contactName\",\n    \"email\",\n    \"contactPhone\",\n    \"company\"\n  ]\n} }}", "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 500]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Input Validator", "type": "main", "index": 0}]]}, "Input Validator": {"main": [[{"node": "Validation Check", "type": "main", "index": 0}]]}, "Validation Check": {"main": [[{"node": "Google My Business Lookup", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Google My Business Lookup": {"main": [[{"node": "NAP Consistency Check", "type": "main", "index": 0}, {"node": "Competitor Analysis", "type": "main", "index": 0}, {"node": "Local Ranking Analysis", "type": "main", "index": 0}]]}, "NAP Consistency Check": {"main": [[{"node": "Report Aggregator", "type": "main", "index": 0}]]}, "Competitor Analysis": {"main": [[{"node": "Report Aggregator", "type": "main", "index": 0}]]}, "Local Ranking Analysis": {"main": [[{"node": "Report Aggregator", "type": "main", "index": 0}]]}, "Report Aggregator": {"main": [[{"node": "HTML Report Generator", "type": "main", "index": 0}]]}, "HTML Report Generator": {"main": [[{"node": "Lead Capture System", "type": "main", "index": 0}]]}, "Lead Capture System": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-01-30T20:00:00.000Z", "versionId": "1"}