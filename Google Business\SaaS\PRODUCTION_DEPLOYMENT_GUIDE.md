# Production Deployment Guide
## GMB Audit Report Generator

This guide provides step-by-step instructions for deploying the GMB Audit Report Generator to production.

## Prerequisites

### System Requirements
- **Node.js**: 18+ LTS
- **PostgreSQL**: 15+
- **Redis**: 7+
- **Docker**: 20+ (optional but recommended)
- **SSL Certificate**: For HTTPS
- **Domain Name**: For public access

### API Keys Required
- Google Gemini Pro API Key
- OpenAI API Key (fallback)
- Perplexity API Key (optional)
- Google Maps API Key
- Email SMTP credentials
- Twilio credentials (for WhatsApp)

## Deployment Options

### Option 1: Docker Deployment (Recommended)

#### 1. Create Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: gmb_audit
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  data-collector:
    build: ./apps/scraper
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  analyzer:
    build: ./apps/analyzer
    environment:
      - NODE_ENV=production
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
    restart: unless-stopped

  report-generator:
    build: ./apps/report-generator
    environment:
      - NODE_ENV=production
      - ANALYZER_SERVICE_URL=http://analyzer:3002
      - DATA_COLLECTOR_URL=http://data-collector:3001
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
    depends_on:
      - analyzer
      - data-collector
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - report-generator
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 2. Create Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream report_generator {
        server report-generator:3003;
    }

    upstream analyzer {
        server analyzer:3002;
    }

    upstream data_collector {
        server data-collector:3001;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/ssl/certs/cert.pem;
        ssl_certificate_key /etc/ssl/certs/key.pem;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API routes with rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://report_generator;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health checks (no rate limiting)
        location /health {
            proxy_pass http://report_generator;
        }

        # Static files
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

#### 3. Deploy with Docker

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Option 2: Cloud Provider Deployment

#### AWS Deployment

1. **Set up RDS PostgreSQL**
```bash
aws rds create-db-instance \
  --db-instance-identifier gmb-audit-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username admin \
  --master-user-password your-password \
  --allocated-storage 20
```

2. **Set up ElastiCache Redis**
```bash
aws elasticache create-cache-cluster \
  --cache-cluster-id gmb-audit-redis \
  --cache-node-type cache.t3.micro \
  --engine redis \
  --num-cache-nodes 1
```

3. **Deploy to ECS/EKS**
- Create ECS task definitions for each service
- Set up Application Load Balancer
- Configure auto-scaling policies
- Set up CloudWatch monitoring

#### Google Cloud Deployment

1. **Set up Cloud SQL PostgreSQL**
```bash
gcloud sql instances create gmb-audit-db \
  --database-version=POSTGRES_15 \
  --tier=db-f1-micro \
  --region=us-central1
```

2. **Set up Memorystore Redis**
```bash
gcloud redis instances create gmb-audit-redis \
  --size=1 \
  --region=us-central1
```

3. **Deploy to Cloud Run**
```bash
# Build and push images
gcloud builds submit --tag gcr.io/PROJECT_ID/report-generator
gcloud builds submit --tag gcr.io/PROJECT_ID/analyzer
gcloud builds submit --tag gcr.io/PROJECT_ID/data-collector

# Deploy services
gcloud run deploy report-generator \
  --image gcr.io/PROJECT_ID/report-generator \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## Security Configuration

### 1. Environment Variables

Create `.env.production`:
```bash
# Database
DATABASE_URL=************************************/gmb_audit
REDIS_URL=redis://host:6379

# API Keys
GOOGLE_AI_API_KEY=your_google_ai_key
OPENAI_API_KEY=your_openai_key
PERPLEXITY_API_KEY=your_perplexity_key
GOOGLE_MAPS_API_KEY=your_maps_key

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# WhatsApp
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token

# Security
JWT_SECRET=your_jwt_secret_key
API_KEY=your_api_key
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Application
NODE_ENV=production
PORT=3003
ANALYZER_SERVICE_URL=http://analyzer:3002
DATA_COLLECTOR_URL=http://data-collector:3001
```

### 2. API Authentication

Add to each service:
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

module.exports = { authenticateToken };
```

### 3. Rate Limiting

```javascript
// middleware/rateLimiter.js
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Too many requests, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = { apiLimiter };
```

## Monitoring and Logging

### 1. Application Monitoring

```javascript
// monitoring/metrics.js
const prometheus = require('prom-client');

const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code']
});

const httpRequestTotal = new prometheus.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

module.exports = {
  httpRequestDuration,
  httpRequestTotal,
  register: prometheus.register
};
```

### 2. Structured Logging

```javascript
// utils/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'gmb-audit' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

module.exports = logger;
```

## Health Checks and Monitoring

### 1. Enhanced Health Checks

```javascript
// routes/health.js
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    dependencies: {}
  };

  try {
    // Check database
    await db.query('SELECT 1');
    health.dependencies.database = 'healthy';
  } catch (error) {
    health.dependencies.database = 'unhealthy';
    health.status = 'degraded';
  }

  try {
    // Check Redis
    await redis.ping();
    health.dependencies.redis = 'healthy';
  } catch (error) {
    health.dependencies.redis = 'unhealthy';
    health.status = 'degraded';
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});
```

### 2. Monitoring Dashboard

Set up monitoring with:
- **Prometheus + Grafana** for metrics
- **ELK Stack** for log aggregation
- **Uptime monitoring** for availability
- **Performance monitoring** for response times

## Backup and Recovery

### 1. Database Backup

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backups/gmb_audit_$DATE.sql
aws s3 cp backups/gmb_audit_$DATE.sql s3://your-backup-bucket/
```

### 2. Disaster Recovery

- Set up database replication
- Configure automated backups
- Document recovery procedures
- Test recovery processes regularly

## Performance Optimization

### 1. Caching Strategy

```javascript
// utils/cache.js
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);

const cache = {
  async get(key) {
    return await client.get(key);
  },
  
  async set(key, value, ttl = 3600) {
    return await client.setEx(key, ttl, JSON.stringify(value));
  },
  
  async del(key) {
    return await client.del(key);
  }
};

module.exports = cache;
```

### 2. Database Optimization

```sql
-- Add indexes for performance
CREATE INDEX idx_business_name ON businesses(business_name);
CREATE INDEX idx_created_at ON reports(created_at);
CREATE INDEX idx_status ON analysis_jobs(status);

-- Connection pooling configuration
-- Set max_connections = 100
-- Set shared_buffers = 256MB
-- Set effective_cache_size = 1GB
```

## Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Database migrations run
- [ ] API keys validated
- [ ] Security measures implemented
- [ ] Monitoring configured

### Deployment
- [ ] Services deployed successfully
- [ ] Health checks passing
- [ ] Load balancer configured
- [ ] DNS records updated
- [ ] SSL working correctly
- [ ] Rate limiting active

### Post-Deployment
- [ ] End-to-end testing in production
- [ ] Performance monitoring active
- [ ] Error tracking configured
- [ ] Backup procedures tested
- [ ] Documentation updated
- [ ] Team notified

## Troubleshooting

### Common Issues

1. **Service Connection Errors**
   - Check network connectivity
   - Verify environment variables
   - Check service logs

2. **Database Connection Issues**
   - Verify connection string
   - Check firewall rules
   - Validate credentials

3. **High Response Times**
   - Check database performance
   - Monitor memory usage
   - Review cache hit rates

4. **API Rate Limiting**
   - Monitor API usage
   - Adjust rate limits
   - Implement caching

### Support Contacts

- **Infrastructure**: DevOps team
- **Application**: Development team
- **Database**: DBA team
- **Security**: Security team

---

**Production Deployment Guide Complete**  
**System Ready for Production Deployment 🚀**
