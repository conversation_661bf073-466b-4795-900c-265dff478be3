import { AnalysisResult, SEOAnalysis, SentimentAnalysis, CompetitiveAnalysis, PhotoAnalysis } from '../types/analysis';
export declare class AIAnalyzer {
    private geminiClient;
    private openaiClient;
    private model;
    private perplexityApiKey;
    private keywordRankingService;
    constructor();
    analyzeBusinessProfile(businessData: any, competitorData?: any[]): Promise<AnalysisResult>;
    analyzeSEOFactors(businessData: any): Promise<SEOAnalysis>;
    analyzeReviewSentiment(reviews: any[]): Promise<SentimentAnalysis>;
    analyzeCompetitivePosition(businessData: any, competitors: any[]): Promise<CompetitiveAnalysis>;
    analyzePhotoOptimization(photos: any[]): Promise<PhotoAnalysis>;
    private getOpenAIAnalysis;
    private buildSEOPrompt;
    private buildSentimentPrompt;
    private buildCompetitivePrompt;
    private buildPhotoPrompt;
    private parseSEOResponse;
    private parseSentimentResponse;
    private parseCompetitiveResponse;
    private parsePhotoResponse;
    private calculateOverallHealth;
    private createFallbackSEOAnalysis;
    private createFallbackSentimentAnalysis;
    private createFallbackCompetitiveAnalysis;
    private createFallbackPhotoAnalysis;
    private getDefaultSEOAnalysis;
    private getDefaultSentimentAnalysis;
    private getDefaultCompetitiveAnalysis;
    private getDefaultPhotoAnalysis;
    verifyBusinessWithPerplexity(businessData: any): Promise<{
        isVerified: boolean;
        confidence: number;
        verifiedData: any;
        issues: string[];
    }>;
    private parsePerplexityVerification;
    analyzeKeywordRankingsWithRealData(businessData: any): Promise<any>;
    private extractLocationFromAddress;
}
//# sourceMappingURL=AIAnalyzer.d.ts.map