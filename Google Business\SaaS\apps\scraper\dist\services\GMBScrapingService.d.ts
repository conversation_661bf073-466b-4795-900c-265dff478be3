export interface ScrapingRequest {
    businessName: string;
    address: string;
    placeId?: string;
    priority?: 'low' | 'normal' | 'high';
}
export interface GMBData {
    businessName: string;
    address: string;
    phone?: string;
    website?: string;
    rating?: number;
    reviewCount?: number;
    totalPhotos?: number;
    totalPosts?: number;
    hours?: any;
    businessHours?: any;
    coordinates?: {
        latitude: number;
        longitude: number;
    };
    recentReviews?: any[];
    photos?: string[];
    posts?: any[];
    description?: string;
    attributes?: any;
    placeId?: string;
    googleUrl?: string;
    rawData?: any;
    scrapedAt: Date;
    isVerified?: boolean;
    isPlaceholder?: boolean;
    isError?: boolean;
    dataSource?: string;
    message?: string;
    errorMessage?: string;
}
export declare class GMBScrapingService {
    private browser;
    private contexts;
    private proxyList;
    private userAgents;
    constructor();
    private loadProxyList;
    private initializeBrowser;
    private createStealthContext;
    private randomDelay;
    private humanLikeTyping;
    private extractBusinessData;
    scrapeBusinessData(request: ScrapingRequest): Promise<GMBData>;
    close(): Promise<void>;
}
//# sourceMappingURL=GMBScrapingService.d.ts.map