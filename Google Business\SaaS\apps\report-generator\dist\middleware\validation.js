"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFileSize = exports.validateRateLimit = exports.validateReportId = exports.validate = exports.validateChartGeneration = exports.validateWhatsAppDelivery = exports.validateEmailDelivery = exports.validateReportRequest = void 0;
const joi_1 = __importDefault(require("joi"));
const reportRequestSchema = joi_1.default.object({
    analysisData: joi_1.default.object({
        scores: joi_1.default.object({
            overall: joi_1.default.number().min(0).max(100).required(),
            grade: joi_1.default.string().valid('A', 'B', 'C', 'D', 'F').required(),
            breakdown: joi_1.default.object({
                reviews: joi_1.default.number().min(0).max(100).required(),
                visibility: joi_1.default.number().min(0).max(100).required(),
                seo: joi_1.default.number().min(0).max(100).required(),
                photos: joi_1.default.number().min(0).max(100).required(),
                posts: joi_1.default.number().min(0).max(100).required(),
                nap: joi_1.default.number().min(0).max(100).required()
            }).required()
        }).required(),
        insights: joi_1.default.array().items(joi_1.default.object({
            type: joi_1.default.string().valid('strength', 'weakness', 'opportunity', 'threat').required(),
            title: joi_1.default.string().required(),
            description: joi_1.default.string().required(),
            impact: joi_1.default.string().valid('high', 'medium', 'low').required(),
            confidence: joi_1.default.number().min(0).max(1).required()
        })).required(),
        recommendations: joi_1.default.array().items(joi_1.default.object({
            title: joi_1.default.string().required(),
            description: joi_1.default.string().required(),
            priority: joi_1.default.string().valid('critical', 'high', 'medium', 'low').required(),
            actionItems: joi_1.default.array().items(joi_1.default.object({
                task: joi_1.default.string().required(),
                effort: joi_1.default.string().valid('low', 'medium', 'high').required(),
                timeline: joi_1.default.string().required()
            })).required(),
            expectedImpact: joi_1.default.object({
                scoreIncrease: joi_1.default.number().min(0).required(),
                timeframe: joi_1.default.string().required()
            }).required()
        })).required()
    }).required(),
    businessData: joi_1.default.object({
        businessName: joi_1.default.string().required(),
        address: joi_1.default.string().optional(),
        phone: joi_1.default.string().optional(),
        website: joi_1.default.string().uri().optional(),
        category: joi_1.default.string().optional(),
        rating: joi_1.default.number().min(0).max(5).optional(),
        reviewCount: joi_1.default.number().min(0).optional()
    }).required(),
    options: joi_1.default.object({
        template: joi_1.default.string().valid('default', 'restaurant', 'service', 'healthcare', 'executive').optional(),
        branding: joi_1.default.object({
            logo: joi_1.default.string().optional(),
            primaryColor: joi_1.default.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional(),
            secondaryColor: joi_1.default.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional(),
            companyName: joi_1.default.string().optional()
        }).optional(),
        includeCharts: joi_1.default.boolean().optional(),
        includeRecommendations: joi_1.default.boolean().optional(),
        includeInsights: joi_1.default.boolean().optional(),
        format: joi_1.default.string().valid('pdf', 'html').optional(),
        quality: joi_1.default.string().valid('standard', 'high').optional()
    }).optional()
});
const emailDeliverySchema = joi_1.default.object({
    reportId: joi_1.default.string().uuid().required(),
    recipient: joi_1.default.string().email().required(),
    subject: joi_1.default.string().max(200).optional(),
    message: joi_1.default.string().max(2000).optional()
});
const whatsappDeliverySchema = joi_1.default.object({
    reportId: joi_1.default.string().uuid().required(),
    phoneNumber: joi_1.default.string().pattern(/^\+[1-9]\d{1,14}$/).required(),
    message: joi_1.default.string().max(1000).optional()
});
const chartGenerationSchema = joi_1.default.object({
    analysisData: joi_1.default.object().required(),
    chartTypes: joi_1.default.array().items(joi_1.default.string().valid('score-gauge', 'breakdown-chart', 'competitive-comparison', 'insights-matrix', 'recommendations-priority')).min(1).required()
});
const validateReportRequest = (req, res, next) => {
    const { error } = reportRequestSchema.validate(req.body, { abortEarly: false });
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid request data',
                code: 'VALIDATION_ERROR',
                details: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message
                }))
            }
        });
    }
    next();
};
exports.validateReportRequest = validateReportRequest;
const validateEmailDelivery = (req, res, next) => {
    const { error } = emailDeliverySchema.validate(req.body, { abortEarly: false });
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid email delivery data',
                code: 'VALIDATION_ERROR',
                details: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message
                }))
            }
        });
    }
    next();
};
exports.validateEmailDelivery = validateEmailDelivery;
const validateWhatsAppDelivery = (req, res, next) => {
    const { error } = whatsappDeliverySchema.validate(req.body, { abortEarly: false });
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid WhatsApp delivery data',
                code: 'VALIDATION_ERROR',
                details: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message
                }))
            }
        });
    }
    next();
};
exports.validateWhatsAppDelivery = validateWhatsAppDelivery;
const validateChartGeneration = (req, res, next) => {
    const { error } = chartGenerationSchema.validate(req.body, { abortEarly: false });
    if (error) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid chart generation data',
                code: 'VALIDATION_ERROR',
                details: error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message
                }))
            }
        });
    }
    next();
};
exports.validateChartGeneration = validateChartGeneration;
const validate = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body, { abortEarly: false });
        if (error) {
            return res.status(400).json({
                success: false,
                error: {
                    message: 'Validation failed',
                    code: 'VALIDATION_ERROR',
                    details: error.details.map(detail => ({
                        field: detail.path.join('.'),
                        message: detail.message
                    }))
                }
            });
        }
        next();
    };
};
exports.validate = validate;
const validateReportId = (req, res, next) => {
    const { reportId } = req.params;
    if (!reportId || !joi_1.default.string().uuid().validate(reportId).error === undefined) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Invalid report ID format',
                code: 'INVALID_REPORT_ID'
            }
        });
    }
    next();
};
exports.validateReportId = validateReportId;
const validateRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
    const requests = new Map();
    return (req, res, next) => {
        const clientId = req.ip || 'unknown';
        const now = Date.now();
        const clientData = requests.get(clientId);
        if (!clientData || now > clientData.resetTime) {
            requests.set(clientId, {
                count: 1,
                resetTime: now + windowMs
            });
            return next();
        }
        if (clientData.count >= maxRequests) {
            return res.status(429).json({
                success: false,
                error: {
                    message: 'Too many requests',
                    code: 'RATE_LIMIT_EXCEEDED',
                    retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
                }
            });
        }
        clientData.count++;
        next();
    };
};
exports.validateRateLimit = validateRateLimit;
const validateFileSize = (maxSize = 10 * 1024 * 1024) => {
    return (req, res, next) => {
        if (req.headers['content-length']) {
            const contentLength = parseInt(req.headers['content-length']);
            if (contentLength > maxSize) {
                return res.status(413).json({
                    success: false,
                    error: {
                        message: 'File too large',
                        code: 'FILE_TOO_LARGE',
                        maxSize: maxSize
                    }
                });
            }
        }
        next();
    };
};
exports.validateFileSize = validateFileSize;
//# sourceMappingURL=validation.js.map