{"version": 3, "file": "InsightGenerator.js", "sourceRoot": "", "sources": ["../../src/insights/InsightGenerator.ts"], "names": [], "mappings": ";;;;;;AACA,sDAA8B;AAC9B,+BAAoC;AAEpC,MAAM,MAAM,GAAG,iBAAO,CAAC,YAAY,CAAC;IAClC,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,iBAAO,CAAC,MAAM,CAAC,IAAI,EAAE;IAC7B,UAAU,EAAE,CAAC,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;CAC/C,CAAC,CAAC;AAEH,MAAa,gBAAgB;IAC3B,KAAK,CAAC,gBAAgB,CAAC,QAAwB,EAAE,MAAsB;QACrE,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YAEtD,MAAM,QAAQ,GAAc,EAAE,CAAC;YAG/B,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAGvF,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAGpG,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,mBAAmB,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YAG9G,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YAG9F,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC;YAGvD,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;gBAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC;gBAC9D,OAAO,OAAO,GAAG,OAAO,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,aAAa,cAAc,CAAC,MAAM,WAAW,CAAC,CAAC;YAC3D,OAAO,cAAc,CAAC;QAExB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,WAAgB,EAAE,QAAgB;QAC5D,MAAM,QAAQ,GAAc,EAAE,CAAC;QAG/B,IAAI,WAAW,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,mCAAmC;gBAC1C,WAAW,EAAE,2EAA2E;gBACxF,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,YAAY;gBACtC,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,IAAI,EAAE;aACxE,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;YACjD,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,wCAAwC;gBAC/C,WAAW,EAAE,oFAAoF;gBACjG,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW;gBACrC,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,IAAI,EAAE;aACvE,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,uCAAuC;gBAC9C,WAAW,EAAE,6EAA6E;gBAC1F,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU;gBACpC,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,IAAI,EAAE;aACtE,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,uCAAuC;gBAC9C,WAAW,EAAE,oFAAoF;gBACjG,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK;gBAC/B,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,IAAI,EAAE;aACjE,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,QAAQ,IAAI,EAAE,EAAE,CAAC;YACnB,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,uBAAuB;gBAC9B,WAAW,EAAE,8DAA8D;gBAC3E,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;gBACzB,eAAe,EAAE,CAAC,gCAAgC,EAAE,+BAA+B,CAAC;aACrF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,sBAAsB,CAAC,iBAAsB,EAAE,WAAmB;QACxE,MAAM,QAAQ,GAAc,EAAE,CAAC;QAG/B,IAAI,iBAAiB,CAAC,OAAO,EAAE,SAAS,KAAK,UAAU,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;YACjG,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,8BAA8B;gBACrC,WAAW,EAAE,mFAAmF;gBAChG,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,iBAAiB,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;gBACtD,IAAI,EAAE,iBAAiB,CAAC,OAAO;gBAC/B,eAAe,EAAE,CAAC,sCAAsC,EAAE,wCAAwC,CAAC;aACpG,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,iBAAiB,CAAC,OAAO,EAAE,SAAS,KAAK,UAAU,IAAI,iBAAiB,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;YACxG,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,oCAAoC;gBAC3C,WAAW,EAAE,iFAAiF;gBAC9F,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,iBAAiB,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE;gBACvD,IAAI,EAAE,iBAAiB,CAAC,OAAO;gBAC/B,eAAe,EAAE,CAAC,oCAAoC,EAAE,oCAAoC,EAAE,qCAAqC,CAAC;aACrI,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,YAAY,GAAG,EAAE,EAAE,CAAC;YAC1D,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,0FAA0F;gBACvG,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,eAAe,EAAE,iBAAiB,CAAC,gBAAgB,EAAE,eAAe,IAAI,EAAE;aAC3E,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,iBAAiB,CAAC,MAAM,EAAE,UAAU,KAAK,WAAW,EAAE,CAAC;YACzD,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,kCAAkC;gBACzC,WAAW,EAAE,kFAAkF;gBAC/F,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,iBAAiB,CAAC,MAAM;gBAC9B,eAAe,EAAE,CAAC,oCAAoC,EAAE,oCAAoC,EAAE,iCAAiC,CAAC;aACjI,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,iBAAiB,CAAC,MAAM,EAAE,UAAU,KAAK,WAAW,EAAE,CAAC;YAChE,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE,mFAAmF;gBAChG,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,iBAAiB,CAAC,MAAM;gBAC9B,eAAe,EAAE,CAAC,sCAAsC,EAAE,+BAA+B,CAAC;aAC3F,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,2BAA2B,CAAC,mBAAwB,EAAE,eAAuB;QACnF,MAAM,QAAQ,GAAc,EAAE,CAAC;QAG/B,IAAI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,aAAa;gBACvB,KAAK,EAAE,wBAAwB;gBAC/B,WAAW,EAAE,0DAA0D;gBACvE,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,mBAAmB,CAAC,QAAQ;gBAClC,eAAe,EAAE,CAAC,iCAAiC,EAAE,+BAA+B,CAAC;aACtF,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,aAAa;gBACvB,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,yFAAyF;gBACtG,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,mBAAmB,CAAC,QAAQ;gBAClC,eAAe,EAAE,CAAC,mCAAmC,EAAE,oCAAoC,CAAC;aAC7F,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;QAClD,IAAI,UAAU,EAAE,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,CAAC;YACxC,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,aAAa;gBACvB,KAAK,EAAE,6BAA6B;gBACpC,WAAW,EAAE,+EAA+E;gBAC5F,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,UAAU,CAAC,MAAM;gBACvB,eAAe,EAAE,CAAC,sCAAsC,EAAE,6CAA6C,CAAC;aACzG,CAAC,CAAC;QACL,CAAC;QAED,IAAI,UAAU,EAAE,OAAO,EAAE,UAAU,GAAG,EAAE,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,aAAa;gBACvB,KAAK,EAAE,+CAA+C;gBACtD,WAAW,EAAE,+EAA+E;gBAC5F,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,UAAU,CAAC,OAAO;gBACxB,eAAe,EAAE,CAAC,sCAAsC,EAAE,oCAAoC,CAAC;aAChG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,qBAAqB,CAAC,aAAkB,EAAE,UAAkB;QAClE,MAAM,QAAQ,GAAc,EAAE,CAAC;QAG/B,IAAI,aAAa,CAAC,QAAQ,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,6BAA6B;gBACpC,WAAW,EAAE,YAAY,aAAa,CAAC,QAAQ,CAAC,GAAG,+CAA+C;gBAClG,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,aAAa,CAAC,QAAQ;gBAC5B,eAAe,EAAE,CAAC,kCAAkC,EAAE,qCAAqC,EAAE,4BAA4B,CAAC;aAC3H,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,4CAA4C,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBACvG,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,aAAa,CAAC,UAAU;gBAC9B,eAAe,EAAE,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,OAAO,GAAG,SAAS,CAAC;aAC5F,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,aAAa,CAAC,YAAY,EAAE,SAAS,GAAG,EAAE,EAAE,CAAC;YAC/C,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,sBAAsB;gBAC7B,WAAW,EAAE,4DAA4D;gBACzE,MAAM,EAAE,KAAK;gBACb,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,aAAa,CAAC,YAAY;gBAChC,eAAe,EAAE,CAAC,6CAA6C,EAAE,sCAAsC,CAAC;aACzG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uBAAuB,CAAC,MAAsB;QACpD,MAAM,QAAQ,GAAc,EAAE,CAAC;QAG/B,IAAI,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,WAAW;gBACrB,KAAK,EAAE,+BAA+B;gBACtC,WAAW,EAAE,2BAA2B,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,KAAK,GAAG;gBACxF,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,MAAM;gBACZ,eAAe,EAAE,CAAC,qCAAqC,EAAE,mCAAmC,CAAC;aAC9F,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC;YAC/B,QAAQ,CAAC,IAAI,CAAC;gBACZ,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,WAAW;gBACrB,KAAK,EAAE,0BAA0B;gBACjC,WAAW,EAAE,kEAAkE,MAAM,CAAC,OAAO,OAAO;gBACpG,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,MAAM;gBACZ,eAAe,EAAE,CAAC,qCAAqC,EAAE,wCAAwC,CAAC;aACnG,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,eAAe,CAAC,MAAc;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;YACtB,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;CACF;AA7VD,4CA6VC"}