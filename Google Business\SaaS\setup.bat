@echo off
setlocal enabledelayedexpansion

echo.
echo 🚀 GMB Audit Generator - Windows Setup Script
echo =============================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ and try again.
    pause
    exit /b 1
) else (
    echo [SUCCESS] Node.js found: 
    node --version
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm and try again.
    pause
    exit /b 1
) else (
    echo [SUCCESS] npm found: 
    npm --version
)

echo.
echo [INFO] Setting up environment configuration...

REM Copy .env.example to .env if it doesn't exist
if not exist .env (
    if exist .env.example (
        copy .env.example .env >nul
        echo [SUCCESS] Created .env file from .env.example
        echo [WARNING] Please edit .env file with your API keys and configuration
    ) else (
        echo [ERROR] .env.example file not found
        pause
        exit /b 1
    )
) else (
    echo [WARNING] .env file already exists, skipping...
)

echo.
echo [INFO] Installing dependencies...

REM Install root dependencies
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Root dependencies installed

echo.
echo [INFO] Creating necessary directories...

REM Create directories
if not exist uploads mkdir uploads
if not exist reports mkdir reports
if not exist logs mkdir logs
if not exist temp-files mkdir temp-files
if not exist screenshots mkdir screenshots

echo [SUCCESS] Directories created

echo.
echo [INFO] Setting up database...

REM Generate Prisma client
call npm run db:generate
if errorlevel 1 (
    echo [ERROR] Failed to generate Prisma client
    pause
    exit /b 1
)
echo [SUCCESS] Prisma client generated

echo.
echo [INFO] Checking Docker availability...

REM Check if Docker is available
docker --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker is not installed. You'll need to set up PostgreSQL and Redis manually.
    echo [INFO] Please install PostgreSQL 15+ and Redis 7+ manually, then run: npm run db:push
) else (
    echo [SUCCESS] Docker found: 
    docker --version
    
    REM Check if Docker Compose is available
    docker-compose --version >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Docker Compose is not installed. You'll need to set up services manually.
    ) else (
        echo [SUCCESS] Docker Compose found: 
        docker-compose --version
        
        echo [INFO] Starting database services with Docker...
        call npm run docker:dev
        
        echo [INFO] Waiting for services to be ready...
        timeout /t 15 /nobreak >nul
        
        echo [INFO] Pushing database schema...
        call npm run db:push
        if errorlevel 1 (
            echo [WARNING] Failed to push database schema. Services might not be ready yet.
            echo [INFO] Try running 'npm run db:push' manually after services are up.
        ) else (
            echo [SUCCESS] Database schema pushed
        )
    )
)

echo.
echo [INFO] Verifying setup...

REM Check if build works
call npm run build
if errorlevel 1 (
    echo [ERROR] Build verification failed
    pause
    exit /b 1
)
echo [SUCCESS] Build verification passed

echo.
echo [SUCCESS] 🎉 Setup completed successfully!
echo.
echo Next steps:
echo 1. Edit .env file with your API keys:
echo    - GEMINI_API_KEY
echo    - OPENAI_API_KEY
echo    - GOOGLE_MAPS_API_KEY
echo    - SMTP credentials
echo.
echo 2. Start the development server:
echo    npm run dev
echo.
echo 3. Access the services:
echo    - API: http://localhost:3000
echo    - API Docs: http://localhost:3000/api-docs
echo    - n8n: http://localhost:5678 (admin/admin123)
echo    - Database Admin: http://localhost:8080
echo    - Redis Admin: http://localhost:8081
echo.
echo 4. Read the documentation:
echo    - README.md
echo    - Technical_Implementation_Plan.md
echo    - Quick_Start_Guide.md
echo.
echo [SUCCESS] Happy coding! 🚀
echo.
pause
