#!/bin/bash

# GMB Audit Generator - Setup Script
# This script automates the initial setup process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
        
        # Check if version is 18 or higher
        NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
            print_error "Node.js version 18 or higher is required. Current version: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    # Check Docker
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version)
        print_success "Docker found: $DOCKER_VERSION"
    else
        print_warning "Docker is not installed. You'll need to set up PostgreSQL and Redis manually."
    fi
    
    # Check Docker Compose
    if command_exists docker-compose; then
        DOCKER_COMPOSE_VERSION=$(docker-compose --version)
        print_success "Docker Compose found: $DOCKER_COMPOSE_VERSION"
    else
        print_warning "Docker Compose is not installed. You'll need to set up services manually."
    fi
    
    # Check Git
    if command_exists git; then
        GIT_VERSION=$(git --version)
        print_success "Git found: $GIT_VERSION"
    else
        print_error "Git is not installed. Please install Git and try again."
        exit 1
    fi
}

# Function to setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file with your API keys and configuration"
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_warning ".env file already exists, skipping..."
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    npm install
    print_success "Root dependencies installed"
    
    # Install workspace dependencies
    print_status "Installing workspace dependencies..."
    npm run build --if-present
    print_success "Workspace dependencies installed"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Generate Prisma client
    npm run db:generate
    print_success "Prisma client generated"
    
    # Check if Docker is available
    if command_exists docker && command_exists docker-compose; then
        print_status "Starting database services with Docker..."
        npm run docker:dev
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 10
        
        # Push database schema
        print_status "Pushing database schema..."
        npm run db:push
        print_success "Database schema pushed"
    else
        print_warning "Docker not available. Please set up PostgreSQL and Redis manually."
        print_warning "Then run: npm run db:push"
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p uploads
    mkdir -p reports
    mkdir -p logs
    mkdir -p temp-files
    mkdir -p screenshots
    
    print_success "Directories created"
}

# Function to setup git hooks (optional)
setup_git_hooks() {
    print_status "Setting up git hooks..."
    
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/sh
# Run linting and type checking before commit
npm run lint
npm run type-check
EOF
    
    chmod +x .git/hooks/pre-commit
    print_success "Git hooks setup complete"
}

# Function to verify setup
verify_setup() {
    print_status "Verifying setup..."
    
    # Check if all packages can be built
    if npm run build; then
        print_success "Build verification passed"
    else
        print_error "Build verification failed"
        exit 1
    fi
    
    # Check if linting passes
    if npm run lint; then
        print_success "Linting verification passed"
    else
        print_warning "Linting issues found. Run 'npm run lint:fix' to fix them."
    fi
    
    # Check if type checking passes
    if npm run type-check; then
        print_success "Type checking verification passed"
    else
        print_error "Type checking failed"
        exit 1
    fi
}

# Function to display next steps
display_next_steps() {
    echo ""
    print_success "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your API keys:"
    echo "   - GEMINI_API_KEY"
    echo "   - OPENAI_API_KEY"
    echo "   - GOOGLE_MAPS_API_KEY"
    echo "   - SMTP credentials"
    echo ""
    echo "2. Start the development server:"
    echo "   npm run dev"
    echo ""
    echo "3. Access the services:"
    echo "   - API: http://localhost:3000"
    echo "   - API Docs: http://localhost:3000/api-docs"
    echo "   - n8n: http://localhost:5678 (admin/admin123)"
    echo "   - Database Admin: http://localhost:8080"
    echo "   - Redis Admin: http://localhost:8081"
    echo ""
    echo "4. Read the documentation:"
    echo "   - README.md"
    echo "   - Technical_Implementation_Plan.md"
    echo "   - Quick_Start_Guide.md"
    echo ""
    print_success "Happy coding! 🚀"
}

# Main setup function
main() {
    echo ""
    echo "🚀 GMB Audit Generator - Setup Script"
    echo "======================================"
    echo ""
    
    check_prerequisites
    setup_environment
    install_dependencies
    create_directories
    setup_database
    setup_git_hooks
    verify_setup
    display_next_steps
}

# Run main function
main "$@"
