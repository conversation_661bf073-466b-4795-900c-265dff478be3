"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessVerificationService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger = {
    info: (message, meta) => console.log(`[INFO] ${new Date().toISOString()} ${message}`, meta || ''),
    warn: (message, meta) => console.warn(`[WARN] ${new Date().toISOString()} ${message}`, meta || ''),
    error: (message, meta) => console.error(`[ERROR] ${new Date().toISOString()} ${message}`, meta || ''),
    debug: (message, meta) => console.debug(`[DEBUG] ${new Date().toISOString()} ${message}`, meta || ''),
};
class BusinessVerificationService {
    perplexityApiKey;
    googleMapsApiKey;
    constructor() {
        this.perplexityApiKey = process.env.PERPLEXITY_API_KEY || '';
        this.googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || '';
        if (!this.perplexityApiKey) {
            logger.warn('Perplexity API key not found. Live verification will be limited.');
        }
        if (!this.googleMapsApiKey) {
            logger.warn('Google Maps API key not found. Geocoding will be limited.');
        }
    }
    async verifyBusiness(request) {
        logger.info('Starting business verification', { businessName: request.businessName });
        const sources = [];
        const issues = [];
        let verifiedData = {};
        let totalConfidence = 0;
        let verificationCount = 0;
        try {
            if (this.perplexityApiKey) {
                const perplexityResult = await this.verifyWithPerplexity(request);
                if (perplexityResult.found) {
                    sources.push('Perplexity Sonar Live Search');
                    verifiedData = { ...verifiedData, ...perplexityResult.data };
                    totalConfidence += perplexityResult.confidence;
                    verificationCount++;
                }
                else {
                    issues.push('Business not found in live web search');
                }
            }
            if (this.googleMapsApiKey) {
                const placesResult = await this.verifyWithGooglePlaces(request);
                if (placesResult.found) {
                    sources.push('Google Places API');
                    verifiedData = { ...verifiedData, ...placesResult.data };
                    totalConfidence += placesResult.confidence;
                    verificationCount++;
                }
                else {
                    issues.push('Business not found in Google Places');
                }
            }
            const contactResult = await this.verifyContactInformation(request);
            if (contactResult.verified) {
                sources.push('Contact Information Verification');
                totalConfidence += contactResult.confidence;
                verificationCount++;
            }
            else {
                issues.push(...contactResult.issues);
            }
            const directoryResults = await this.verifyWithDirectories(request);
            if (directoryResults.length > 0) {
                sources.push(`${directoryResults.length} Online Directories`);
                totalConfidence += this.calculateDirectoryConfidence(directoryResults);
                verificationCount++;
            }
            const overallConfidence = verificationCount > 0 ? totalConfidence / verificationCount : 0;
            const isVerified = overallConfidence >= 0.7 && issues.length < 3;
            const result = {
                isVerified,
                confidence: Math.round(overallConfidence * 100) / 100,
                sources,
                issues,
                verifiedData,
                methodology: this.getVerificationMethodology(),
                lastVerified: new Date()
            };
            logger.info('Business verification completed', {
                businessName: request.businessName,
                isVerified,
                confidence: result.confidence,
                sources: sources.length
            });
            return result;
        }
        catch (error) {
            logger.error('Business verification failed', { error: error.message });
            return {
                isVerified: false,
                confidence: 0,
                sources: [],
                issues: [`Verification failed: ${error.message}`],
                verifiedData: {},
                methodology: this.getVerificationMethodology(),
                lastVerified: new Date()
            };
        }
    }
    async verifyWithPerplexity(request) {
        try {
            const searchQuery = `Verify business information for "${request.businessName}" at "${request.address}". Provide current business hours, phone number, website, and confirm if this business actually exists.`;
            const response = await axios_1.default.post('https://api.perplexity.ai/chat/completions', {
                model: 'llama-3.1-sonar-small-128k-online',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a business verification assistant. Provide accurate, real-time business information. If a business does not exist or information cannot be verified, clearly state this. Never fabricate information.'
                    },
                    {
                        role: 'user',
                        content: searchQuery
                    }
                ],
                max_tokens: 1500,
                temperature: 0.1
            }, {
                headers: {
                    'Authorization': `Bearer ${this.perplexityApiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            const content = response.data.choices[0]?.message?.content;
            if (!content) {
                return { found: false, confidence: 0, data: {} };
            }
            const verification = this.parsePerplexityVerification(content, request);
            return verification;
        }
        catch (error) {
            logger.error('Perplexity verification failed', { error: error.message });
            return { found: false, confidence: 0, data: {} };
        }
    }
    parsePerplexityVerification(content, request) {
        const lowerContent = content.toLowerCase();
        const existsIndicators = ['exists', 'found', 'located', 'operating', 'open'];
        const notExistsIndicators = ['does not exist', 'not found', 'closed permanently', 'no longer operating'];
        const existsScore = existsIndicators.reduce((score, indicator) => lowerContent.includes(indicator) ? score + 1 : score, 0);
        const notExistsScore = notExistsIndicators.reduce((score, indicator) => lowerContent.includes(indicator) ? score + 1 : score, 0);
        if (notExistsScore > existsScore) {
            return { found: false, confidence: 0, data: {} };
        }
        const data = {
            businessName: request.businessName,
            address: request.address
        };
        const phoneMatch = content.match(/(\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/);
        if (phoneMatch) {
            data.phone = phoneMatch[1];
        }
        const websiteMatch = content.match(/(https?:\/\/[^\s]+)/);
        if (websiteMatch) {
            data.website = websiteMatch[1];
        }
        const hoursMatch = content.match(/hours?:?\s*([^.]+)/i);
        if (hoursMatch) {
            data.businessHours = hoursMatch[1].trim();
        }
        let confidence = 0.5;
        if (data.phone)
            confidence += 0.2;
        if (data.website)
            confidence += 0.2;
        if (data.businessHours)
            confidence += 0.1;
        return {
            found: true,
            confidence: Math.min(confidence, 1.0),
            data
        };
    }
    async verifyWithGooglePlaces(request) {
        try {
            const searchQuery = `${request.businessName} ${request.address}`;
            const url = 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json';
            const response = await axios_1.default.get(url, {
                params: {
                    input: searchQuery,
                    inputtype: 'textquery',
                    fields: 'place_id,name,formatted_address,rating,user_ratings_total,business_status',
                    key: this.googleMapsApiKey
                }
            });
            if (response.data.status !== 'OK' || !response.data.candidates.length) {
                return { found: false, confidence: 0, data: {} };
            }
            const place = response.data.candidates[0];
            if (place.business_status === 'CLOSED_PERMANENTLY') {
                return { found: false, confidence: 0, data: {} };
            }
            const data = {
                businessName: place.name,
                address: place.formatted_address,
                rating: place.rating || 0,
                reviewCount: place.user_ratings_total || 0,
                placeId: place.place_id,
                isVerified: true,
                dataSource: 'Google Places API'
            };
            const nameSimilarity = this.calculateStringSimilarity(request.businessName.toLowerCase(), place.name.toLowerCase());
            return {
                found: true,
                confidence: nameSimilarity,
                data
            };
        }
        catch (error) {
            logger.error('Google Places verification failed', { error: error.message });
            return { found: false, confidence: 0, data: {} };
        }
    }
    async verifyContactInformation(request) {
        const issues = [];
        let confidence = 0.8;
        if (request.phone) {
            const phoneRegex = /^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/;
            if (!phoneRegex.test(request.phone)) {
                issues.push('Phone number format appears invalid');
                confidence -= 0.2;
            }
            if (request.phone.includes('123456') || request.phone.includes('000000')) {
                issues.push('Phone number appears to be a placeholder');
                confidence -= 0.3;
            }
        }
        if (request.website) {
            try {
                new URL(request.website);
            }
            catch {
                issues.push('Website URL format appears invalid');
                confidence -= 0.2;
            }
        }
        return {
            verified: issues.length === 0,
            confidence: Math.max(confidence, 0),
            issues
        };
    }
    async verifyWithDirectories(request) {
        return [{
                directoryName: 'Real Directory Integration Required',
                found: false,
                consistency: {
                    nameMatch: false,
                    addressMatch: false,
                    phoneMatch: false
                }
            }];
    }
    calculateDirectoryConfidence(results) {
        if (results.length === 0)
            return 0;
        const foundCount = results.filter(r => r.found).length;
        return foundCount / results.length;
    }
    calculateStringSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        if (longer.length === 0)
            return 1.0;
        const distance = this.calculateLevenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }
    calculateLevenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        for (let i = 0; i <= str1.length; i++)
            matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++)
            matrix[j][0] = j;
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(matrix[j][i - 1] + 1, matrix[j - 1][i] + 1, matrix[j - 1][i - 1] + indicator);
            }
        }
        return matrix[str2.length][str1.length];
    }
    getVerificationMethodology() {
        return `Multi-source verification using: ${this.perplexityApiKey ? 'Perplexity Sonar live web search, ' : ''}${this.googleMapsApiKey ? 'Google Places API, ' : ''}contact information validation, and directory cross-referencing. Confidence scoring based on data consistency across sources.`;
    }
}
exports.BusinessVerificationService = BusinessVerificationService;
//# sourceMappingURL=BusinessVerificationService.js.map