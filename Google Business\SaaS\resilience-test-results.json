{"timestamp": "2025-07-31T06:41:08.753Z", "inputValidation": {"tests": [{"name": "Missing Business Data", "expectedStatus": 400, "actualStatus": 400, "passed": true, "responseTime": 49}, {"name": "Invalid Business Name", "expectedStatus": 400, "actualStatus": 400, "passed": true, "responseTime": 5}, {"name": "Malformed JSON", "expectedStatus": 400, "actualStatus": 400, "passed": true, "responseTime": 4}, {"name": "Empty Request Body", "expectedStatus": 400, "actualStatus": 400, "passed": true, "responseTime": 3}, {"name": "Invalid Email Format", "expectedStatus": 400, "actualStatus": 400, "passed": true, "responseTime": 3}]}, "errorHandling": {"tests": [{"name": "Non-existent Report ID", "expectedStatus": 404, "actualStatus": 404, "passed": true, "hasErrorMessage": {"message": "Report not found", "code": "REPORT_NOT_FOUND"}, "responseTime": 4}, {"name": "Invalid Report ID Format", "expectedStatus": 404, "actualStatus": 404, "passed": true, "hasErrorMessage": {"message": "Report not found", "code": "REPORT_NOT_FOUND"}, "responseTime": 2}, {"name": "Non-existent Endpoint", "expectedStatus": 404, "actualStatus": 404, "passed": true, "responseTime": 5}, {"name": "Method Not Allowed", "expectedStatus": [404, 405], "actualStatus": 404, "passed": true, "responseTime": 4}]}, "fallbackMechanisms": {"analyzerServiceAvailable": false, "integratedReportGeneration": {"status": 200, "success": true, "responseTime": 14, "usedFallback": true, "hasAnalysisData": {"scores": {"overall": 78, "grade": "B", "breakdown": {"reviews": 85, "visibility": 75, "seo": 78, "photos": 74, "posts": 70, "nap": 85}, "weights": {"reviews": 0.25, "visibility": 0.2, "seo": 0.2, "photos": 0.15, "posts": 0.1, "nap": 0.1}}, "insights": [{"id": "f9b77142-526f-40b8-aba8-67d5aa7d70b0", "type": "strength", "category": "reviews", "title": "Strong Review Performance", "description": "Your business maintains excellent customer satisfaction with high-quality reviews.", "impact": "high", "confidence": 0.9, "actionable": true, "relatedMetrics": ["review_score", "sentiment_analysis"]}, {"id": "51a90a3e-536a-4025-83ab-606ee57a9456", "type": "opportunity", "category": "photos", "title": "Photo Gallery Enhancement", "description": "Adding more diverse, high-quality photos can significantly improve visibility.", "impact": "medium", "confidence": 0.8, "actionable": true, "relatedMetrics": ["photo_count", "photo_diversity"]}], "recommendations": [{"id": "390b2014-f951-47a8-a719-99e4955295c7", "priority": "high", "category": "photos", "title": "Expand Photo Gallery", "description": "Upload additional high-quality photos to showcase your business comprehensively.", "actionItems": [{"task": "Take professional exterior photos", "effort": "low", "timeline": "1 week", "impact": 8}, {"task": "Capture interior ambiance shots", "effort": "low", "timeline": "1 week", "impact": 7}, {"task": "Photograph team members", "effort": "medium", "timeline": "2 weeks", "impact": 6}], "expectedImpact": {"scoreIncrease": 5, "timeframe": "2-4 weeks", "confidence": 0.8}, "resources": [{"type": "guide", "title": "GMB Photo Best Practices", "description": "Complete guide to optimizing business photos"}]}], "overallHealth": 78}}}, "serviceUnavailable": {"tests": [{"name": "Non-existent Service Port", "status": 0, "expectedError": false, "error": "", "responseTime": 5}, {"name": "Wrong Port for Service", "status": 0, "expectedError": false, "error": "", "responseTime": 3}]}, "malformedData": {"tests": [{"name": "Extremely Large Business Name", "status": 200, "handledGracefully": false, "responseTime": 28}, {"name": "Special Characters in Business Name", "status": 200, "handledGracefully": false, "responseTime": 6}, {"name": "Null Values", "status": 400, "handledGracefully": {"message": "Invalid integrated report request", "details": ["\"businessData.businessName\" must be a string"]}, "hasErrorMessage": "Invalid integrated report request", "responseTime": 4}, {"name": "Array Instead of Object", "status": 400, "handledGracefully": {"message": "Invalid integrated report request", "details": ["\"businessData\" must be of type object"]}, "hasErrorMessage": "Invalid integrated report request", "responseTime": 2}]}, "summary": {"inputValidation": {"passed": 5, "total": 5, "percentage": "100.0"}, "errorHandling": {"passed": 4, "total": 4, "percentage": "100.0"}, "malformedDataHandling": {"handled": 2, "total": 4, "percentage": "50.0"}, "serviceUnavailableHandling": {"handled": 0, "total": 2, "percentage": "0.0"}, "fallbackMechanisms": {"working": true}}}