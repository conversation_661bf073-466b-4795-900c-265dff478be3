export declare const logger: {
    info: (message: string, meta?: any) => void;
    warn: (message: string, meta?: any) => void;
    error: (message: string, meta?: any) => void;
    debug: (message: string, meta?: any) => void;
};
export declare const utils: {
    sleep: (ms: number) => Promise<unknown>;
    generateId: () => string;
    formatDate: (date: Date) => string;
};
export declare const validation: {
    isEmail: (email: string) => boolean;
    isUrl: (url: string) => boolean;
    isPhoneNumber: (phone: string) => boolean;
};
export declare const performance: {
    timer: () => {
        end: () => number;
    };
    measure: <T>(fn: () => Promise<T>, label?: string) => Promise<{
        result: T;
        duration: number;
    }>;
};
//# sourceMappingURL=index.d.ts.map