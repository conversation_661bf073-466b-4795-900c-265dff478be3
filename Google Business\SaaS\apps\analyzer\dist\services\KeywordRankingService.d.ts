export interface KeywordRankingRequest {
    businessName: string;
    website?: string;
    category: string;
    location: string;
    targetKeywords?: string[];
}
export interface KeywordRanking {
    keyword: string;
    position: number;
    searchVolume: number;
    difficulty: number;
    url?: string;
    lastChecked: Date;
    source: string;
    isVerified: boolean;
}
export interface RankingAnalysis {
    totalKeywords: number;
    averagePosition: number;
    topRankingKeywords: KeywordRanking[];
    improvementOpportunities: KeywordRanking[];
    competitorComparison: {
        betterThan: number;
        worseThan: number;
    };
    methodology: string;
}
export declare class KeywordRankingService {
    private perplexityApiKey;
    private searchConsoleConnected;
    constructor();
    analyzeKeywordRankings(request: KeywordRankingRequest): Promise<RankingAnalysis>;
    private getSearchConsoleRankings;
    private getPerplexityRankings;
    private checkKeywordRanking;
    private parseRankingPosition;
    private generateRelevantKeywords;
    private estimateSearchVolume;
    private estimateKeywordDifficulty;
    private analyzeRankings;
    private getNoDataAvailableAnalysis;
    private getErrorAnalysis;
    private delay;
}
//# sourceMappingURL=KeywordRankingService.d.ts.map