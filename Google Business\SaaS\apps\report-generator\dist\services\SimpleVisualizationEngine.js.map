{"version": 3, "file": "SimpleVisualizationEngine.js", "sourceRoot": "", "sources": ["../../src/services/SimpleVisualizationEngine.ts"], "names": [], "mappings": ";;;AAAA,+BAAoC;AAsBpC,MAAa,yBAAyB;IAC5B,WAAW,GAA2B,IAAI,GAAG,EAAE,CAAC;IAKxD,KAAK,CAAC,aAAa,CAAC,OAA6B;QAC/C,MAAM,OAAO,GAAG,IAAA,SAAM,GAAE,CAAC;QAEzB,IAAI,CAAC;YACH,IAAI,SAAoB,CAAC;YAEzB,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC1B,KAAK,aAAa;oBAChB,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,iBAAiB;oBACpB,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,wBAAwB;oBAC3B,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACxD,MAAM;gBACR,KAAK,iBAAiB;oBACpB,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACtD,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YACpE,CAAC;YAGD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAEzC,OAAO;gBACL,OAAO;gBACP,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,WAAW;aACpB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,QAAgC;QAC3D,MAAM,OAAO,GAA4B,EAAE,CAAC;QAE5C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACjD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,SAAS,GAAG,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAE1H,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC;QAEhC,OAAO;YACL,IAAI,EAAE,OAAO;YACb,IAAI,EAAE;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;gBAChC,QAAQ,EAAE;oBACR,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;oBACpD,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;oBACrD,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;oBACrD,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE;oBAC1D,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE;iBAC5D;aACF;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,mBAAmB;gBAC1B,QAAQ,EAAE,UAAU,KAAK,EAAE;gBAC3B,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;aAClC;SACF,CAAC;IACJ,CAAC;IAKO,sBAAsB,CAAC,IAAS;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;QAExC,OAAO;YACL,IAAI,EAAE,KAAK;YACX,IAAI,EAAE;gBACJ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/B,QAAQ,EAAE,CAAC;wBACT,KAAK,EAAE,iBAAiB;wBACxB,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;wBAC/B,eAAe,EAAE;4BACf,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;yBACjE;qBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,6BAA6B;gBACpC,MAAM,EAAE;oBACN,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;IAKO,wBAAwB,CAAC,IAAS;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QAE3C,MAAM,MAAM,GAAG,CAAC,eAAe,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAEnE,OAAO;YACL,IAAI,EAAE,OAAO;YACb,IAAI,EAAE;gBACJ,MAAM;gBACN,QAAQ,EAAE,CAAC;wBACT,KAAK,EAAE,sBAAsB;wBAC7B,IAAI,EAAE,MAAM;wBACZ,eAAe,EAAE,yBAAyB;wBAC1C,WAAW,EAAE,SAAS;wBACtB,oBAAoB,EAAE,SAAS;qBAChC,CAAC;aACH;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,+BAA+B;gBACtC,MAAM,EAAE;oBACN,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;iBACxB;aACF;SACF,CAAC;IACJ,CAAC;IAKO,sBAAsB,CAAC,IAAS;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;QAErC,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;QACrE,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAC/C,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAY,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM,CACpE,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE;gBACJ,MAAM,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,CAAC;gBAC/D,QAAQ,EAAE,CAAC;wBACT,IAAI,EAAE,cAAc;wBACpB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;qBAC9D,CAAC;aACH;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,uBAAuB;gBAC9B,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;aAC/B;SACF,CAAC;IACJ,CAAC;IAKO,aAAa,CAAC,KAAa;QACjC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;QAClC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;QAClC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;QAClC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;QAClC,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,cAAc,CAAC,OAAe;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAKD,UAAU;QACR,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAKD,aAAa;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAC3B,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;SAC5C,CAAC;IACJ,CAAC;CACF;AApND,8DAoNC;AAED,kBAAe,yBAAyB,CAAC"}