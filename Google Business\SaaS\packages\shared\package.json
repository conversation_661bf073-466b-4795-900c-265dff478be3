{"name": "@gmb-audit/shared", "version": "1.0.0", "description": "Shared utilities for GMB audit generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"dotenv": "^16.3.1", "joi": "^17.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ioredis": "^5.3.2", "crypto": "^1.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "validator": "^13.11.0", "lodash": "^4.17.21", "date-fns": "^2.30.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/validator": "^13.11.7", "@types/lodash": "^4.14.202", "typescript": "^5.3.0"}}