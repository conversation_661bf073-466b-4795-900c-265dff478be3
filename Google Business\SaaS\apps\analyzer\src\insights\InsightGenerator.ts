import { AnalysisResult, ScoreBreakdown, Insight } from '../types/analysis';
import winston from 'winston';
import { v4 as uuidv4 } from 'uuid';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [new winston.transports.Console()]
});

export class InsightGenerator {
  async generateInsights(analysis: AnalysisResult, scores: ScoreBreakdown): Promise<Insight[]> {
    try {
      logger.info('Generating insights from analysis data');

      const insights: Insight[] = [];

      // Generate SEO insights
      insights.push(...this.generateSEOInsights(analysis.seoAnalysis, scores.breakdown.seo));

      // Generate review insights
      insights.push(...this.generateReviewInsights(analysis.sentimentAnalysis, scores.breakdown.reviews));

      // Generate competitive insights
      insights.push(...this.generateCompetitiveInsights(analysis.competitiveAnalysis, scores.breakdown.visibility));

      // Generate photo insights
      insights.push(...this.generatePhotoInsights(analysis.photoAnalysis, scores.breakdown.photos));

      // Generate overall performance insights
      insights.push(...this.generateOverallInsights(scores));

      // Sort by impact and confidence
      const sortedInsights = insights.sort((a, b) => {
        const aWeight = this.getImpactWeight(a.impact) * a.confidence;
        const bWeight = this.getImpactWeight(b.impact) * b.confidence;
        return bWeight - aWeight;
      });

      logger.info(`Generated ${sortedInsights.length} insights`);
      return sortedInsights;

    } catch (error: any) {
      logger.error('Error generating insights', { error: error.message });
      return [];
    }
  }

  private generateSEOInsights(seoAnalysis: any, seoScore: number): Insight[] {
    const insights: Insight[] = [];

    // Business name insights
    if (seoAnalysis.factors?.businessName?.score < 70) {
      insights.push({
        id: uuidv4(),
        type: 'weakness',
        category: 'seo',
        title: 'Business Name Optimization Needed',
        description: 'Your business name could be optimized for better local search visibility.',
        impact: 'medium',
        confidence: 85,
        data: seoAnalysis.factors.businessName,
        recommendations: seoAnalysis.factors.businessName.recommendations || []
      });
    }

    // Description insights
    if (seoAnalysis.factors?.description?.score < 70) {
      insights.push({
        id: uuidv4(),
        type: 'weakness',
        category: 'seo',
        title: 'Business Description Needs Improvement',
        description: 'Your business description lacks keyword optimization and could be more compelling.',
        impact: 'high',
        confidence: 90,
        data: seoAnalysis.factors.description,
        recommendations: seoAnalysis.factors.description.recommendations || []
      });
    }

    // Category insights
    if (seoAnalysis.factors?.categories?.score < 80) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'seo',
        title: 'Business Categories Need Optimization',
        description: 'Adding relevant business categories can improve your local search rankings.',
        impact: 'medium',
        confidence: 80,
        data: seoAnalysis.factors.categories,
        recommendations: seoAnalysis.factors.categories.recommendations || []
      });
    }

    // Hours insights
    if (seoAnalysis.factors?.hours?.score < 90) {
      insights.push({
        id: uuidv4(),
        type: 'weakness',
        category: 'seo',
        title: 'Business Hours Information Incomplete',
        description: 'Complete and accurate business hours improve customer trust and search visibility.',
        impact: 'medium',
        confidence: 95,
        data: seoAnalysis.factors.hours,
        recommendations: seoAnalysis.factors.hours.recommendations || []
      });
    }

    // Overall SEO strength
    if (seoScore >= 85) {
      insights.push({
        id: uuidv4(),
        type: 'strength',
        category: 'seo',
        title: 'Strong SEO Foundation',
        description: 'Your Google Business Profile has excellent SEO optimization.',
        impact: 'high',
        confidence: 90,
        data: { score: seoScore },
        recommendations: ['Maintain current SEO practices', 'Monitor for algorithm changes']
      });
    }

    return insights;
  }

  private generateReviewInsights(sentimentAnalysis: any, reviewScore: number): Insight[] {
    const insights: Insight[] = [];

    // Overall sentiment insights
    if (sentimentAnalysis.overall?.sentiment === 'positive' && sentimentAnalysis.overall.score >= 80) {
      insights.push({
        id: uuidv4(),
        type: 'strength',
        category: 'reviews',
        title: 'Excellent Customer Sentiment',
        description: 'Your customers consistently leave positive reviews, indicating high satisfaction.',
        impact: 'high',
        confidence: sentimentAnalysis.overall.confidence || 85,
        data: sentimentAnalysis.overall,
        recommendations: ['Continue providing excellent service', 'Leverage positive reviews in marketing']
      });
    } else if (sentimentAnalysis.overall?.sentiment === 'negative' || sentimentAnalysis.overall?.score < 60) {
      insights.push({
        id: uuidv4(),
        type: 'threat',
        category: 'reviews',
        title: 'Negative Review Sentiment Detected',
        description: 'Your business has concerning negative sentiment that needs immediate attention.',
        impact: 'high',
        confidence: sentimentAnalysis.overall?.confidence || 80,
        data: sentimentAnalysis.overall,
        recommendations: ['Address negative feedback promptly', 'Implement service improvement plan', 'Increase positive review generation']
      });
    }

    // Response rate insights
    if (sentimentAnalysis.responseAnalysis?.responseRate < 50) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'reviews',
        title: 'Low Review Response Rate',
        description: 'Responding to reviews shows you value customer feedback and can improve your reputation.',
        impact: 'medium',
        confidence: 90,
        data: sentimentAnalysis.responseAnalysis,
        recommendations: sentimentAnalysis.responseAnalysis?.recommendations || []
      });
    }

    // Trend insights
    if (sentimentAnalysis.trends?.trajectory === 'declining') {
      insights.push({
        id: uuidv4(),
        type: 'threat',
        category: 'reviews',
        title: 'Declining Review Sentiment Trend',
        description: 'Your review sentiment is trending downward, indicating potential service issues.',
        impact: 'high',
        confidence: 85,
        data: sentimentAnalysis.trends,
        recommendations: ['Investigate recent service changes', 'Implement customer feedback system', 'Train staff on customer service']
      });
    } else if (sentimentAnalysis.trends?.trajectory === 'improving') {
      insights.push({
        id: uuidv4(),
        type: 'strength',
        category: 'reviews',
        title: 'Improving Review Sentiment',
        description: 'Your review sentiment is trending upward, showing positive business improvements.',
        impact: 'medium',
        confidence: 80,
        data: sentimentAnalysis.trends,
        recommendations: ['Continue current improvement efforts', 'Document successful practices']
      });
    }

    return insights;
  }

  private generateCompetitiveInsights(competitiveAnalysis: any, visibilityScore: number): Insight[] {
    const insights: Insight[] = [];

    // Market position insights
    if (competitiveAnalysis.position?.rank <= 3) {
      insights.push({
        id: uuidv4(),
        type: 'strength',
        category: 'competitive',
        title: 'Strong Market Position',
        description: 'You rank among the top competitors in your local market.',
        impact: 'high',
        confidence: 90,
        data: competitiveAnalysis.position,
        recommendations: ['Maintain competitive advantages', 'Monitor competitor activities']
      });
    } else if (competitiveAnalysis.position?.rank > 7) {
      insights.push({
        id: uuidv4(),
        type: 'weakness',
        category: 'competitive',
        title: 'Low Market Ranking',
        description: 'Your business ranks low compared to competitors, indicating optimization opportunities.',
        impact: 'high',
        confidence: 85,
        data: competitiveAnalysis.position,
        recommendations: ['Analyze top competitor strategies', 'Implement competitive improvements']
      });
    }

    // Benchmark insights
    const benchmarks = competitiveAnalysis.benchmarks;
    if (benchmarks?.rating?.percentile < 50) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'competitive',
        title: 'Rating Below Market Average',
        description: 'Your rating is below the market average, affecting your competitive position.',
        impact: 'high',
        confidence: 90,
        data: benchmarks.rating,
        recommendations: ['Focus on service quality improvement', 'Implement customer satisfaction initiatives']
      });
    }

    if (benchmarks?.reviews?.percentile < 30) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'competitive',
        title: 'Review Volume Significantly Below Competitors',
        description: 'You have fewer reviews than most competitors, impacting trust and visibility.',
        impact: 'high',
        confidence: 95,
        data: benchmarks.reviews,
        recommendations: ['Implement review generation strategy', 'Follow up with satisfied customers']
      });
    }

    return insights;
  }

  private generatePhotoInsights(photoAnalysis: any, photoScore: number): Insight[] {
    const insights: Insight[] = [];

    // Photo quantity insights
    if (photoAnalysis.quantity?.gap > 10) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'photos',
        title: 'Insufficient Photo Quantity',
        description: `You need ${photoAnalysis.quantity.gap} more photos to reach the recommended amount.`,
        impact: 'medium',
        confidence: 95,
        data: photoAnalysis.quantity,
        recommendations: ['Add high-quality business photos', 'Include exterior and interior shots', 'Show products and services']
      });
    }

    // Photo category insights
    if (photoAnalysis.categories?.missing?.length > 0) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'photos',
        title: 'Missing Photo Categories',
        description: `You're missing photos in key categories: ${photoAnalysis.categories.missing.join(', ')}.`,
        impact: 'medium',
        confidence: 90,
        data: photoAnalysis.categories,
        recommendations: photoAnalysis.categories.missing.map((cat: string) => `Add ${cat} photos`)
      });
    }

    // Photo optimization insights
    if (photoAnalysis.optimization?.geotagged < 50) {
      insights.push({
        id: uuidv4(),
        type: 'opportunity',
        category: 'photos',
        title: 'Photos Not Geotagged',
        description: 'Geotagged photos can improve your local search visibility.',
        impact: 'low',
        confidence: 80,
        data: photoAnalysis.optimization,
        recommendations: ['Enable location services when taking photos', 'Add location data to existing photos']
      });
    }

    return insights;
  }

  private generateOverallInsights(scores: ScoreBreakdown): Insight[] {
    const insights: Insight[] = [];

    // Overall performance insight
    if (scores.overall >= 90) {
      insights.push({
        id: uuidv4(),
        type: 'strength',
        category: 'technical',
        title: 'Excellent Overall Performance',
        description: `Your GMB profile scores ${scores.overall}/100 with grade ${scores.grade}.`,
        impact: 'high',
        confidence: 95,
        data: scores,
        recommendations: ['Maintain current optimization level', 'Monitor for any performance drops']
      });
    } else if (scores.overall < 60) {
      insights.push({
        id: uuidv4(),
        type: 'threat',
        category: 'technical',
        title: 'Poor Overall Performance',
        description: `Your GMB profile needs significant improvement with a score of ${scores.overall}/100.`,
        impact: 'high',
        confidence: 95,
        data: scores,
        recommendations: ['Prioritize high-impact improvements', 'Consider professional GMB optimization']
      });
    }

    return insights;
  }

  private getImpactWeight(impact: string): number {
    switch (impact) {
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 1;
    }
  }
}
