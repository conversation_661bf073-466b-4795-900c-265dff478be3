// Core business types
export interface BusinessQuery {
  name: string;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  category?: string;
}

export interface BusinessLocation {
  latitude: number;
  longitude: number;
  address: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

// Google My Business data types
export interface GMBBasicInfo {
  name: string;
  rating?: number;
  reviewCount?: number;
  address?: string;
  phone?: string;
  website?: string;
  category?: string;
  description?: string;
  hours?: BusinessHours;
  photos?: GMBPhoto[];
}

export interface BusinessHours {
  monday?: DayHours;
  tuesday?: DayHours;
  wednesday?: DayHours;
  thursday?: DayHours;
  friday?: DayHours;
  saturday?: DayHours;
  sunday?: DayHours;
  specialHours?: SpecialHours[];
}

export interface DayHours {
  open: string; // "09:00"
  close: string; // "17:00"
  isClosed?: boolean;
}

export interface SpecialHours {
  date: string; // "2024-12-25"
  hours?: DayHours;
  isClosed?: boolean;
  description?: string;
}

export interface GMBPhoto {
  url: string;
  thumbnail?: string;
  alt?: string;
  category?: 'logo' | 'cover' | 'interior' | 'exterior' | 'product' | 'team' | 'other';
  uploadDate?: string;
}

export interface GMBReview {
  id: string;
  author: string;
  rating: number;
  text: string;
  date: string;
  response?: {
    text: string;
    date: string;
  };
}

export interface GMBPost {
  id: string;
  type: 'update' | 'event' | 'offer' | 'product';
  title?: string;
  content: string;
  media?: string[];
  publishDate: string;
  eventDate?: string;
  callToAction?: {
    type: string;
    url?: string;
  };
}

// Competitor analysis types
export interface Competitor {
  id: string;
  name: string;
  address?: string;
  rating?: number;
  reviewCount?: number;
  distance?: number; // in miles/km
  localRanking?: number;
  mapRanking?: number;
  location?: BusinessLocation;
  website?: string;
  phone?: string;
  category?: string;
  strengths?: string[];
  weaknesses?: string[];
}

// Analysis and scoring types
export interface ScoreBreakdown {
  overall: number;
  breakdown: {
    reviews: number;
    visibility: number;
    seo: number;
    photos: number;
    posts: number;
    nap: number; // Name, Address, Phone consistency
  };
}

export interface SEOAnalysis {
  title?: {
    score: number;
    issues: string[];
    recommendations: string[];
  };
  description?: {
    score: number;
    issues: string[];
    recommendations: string[];
  };
  keywords?: {
    primary: string[];
    secondary: string[];
    missing: string[];
  };
  schema?: {
    present: boolean;
    type?: string;
    issues: string[];
  };
}

export interface VisibilityAnalysis {
  localPackPresence: boolean;
  mapRanking?: number;
  searchRanking?: number;
  citationCount?: number;
  citationConsistency?: number;
  onlineDirectoryPresence?: {
    total: number;
    present: number;
    missing: string[];
  };
}

export interface ReviewAnalysis {
  averageRating: number;
  totalReviews: number;
  recentReviews: number; // last 30 days
  responseRate: number; // percentage of reviews responded to
  averageResponseTime?: number; // in hours
  sentiment: {
    positive: number;
    neutral: number;
    negative: number;
  };
  commonTopics: {
    topic: string;
    frequency: number;
    sentiment: 'positive' | 'neutral' | 'negative';
  }[];
}

// Report generation types
export interface AuditReportData {
  business: GMBBasicInfo;
  scores: ScoreBreakdown;
  analysis: {
    seo: SEOAnalysis;
    visibility: VisibilityAnalysis;
    reviews: ReviewAnalysis;
  };
  competitors: Competitor[];
  recommendations: Recommendation[];
  visualizations: {
    heatmapUrl?: string;
    chartsData?: ChartData[];
    screenshots?: string[];
  };
  metadata: {
    generatedAt: string;
    reportId: string;
    version: string;
  };
}

export interface Recommendation {
  id: string;
  category: 'seo' | 'reviews' | 'photos' | 'posts' | 'citations' | 'general';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  timeframe: string;
  actionItems: string[];
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'radar';
  title: string;
  data: any;
  options?: any;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Webhook and integration types
export interface WebhookPayload {
  event: string;
  data: any;
  timestamp: string;
  source: string;
}

export interface N8NWorkflowData {
  businessName: string;
  businessAddress: string;
  email?: string;
  phone?: string;
  reportType?: 'basic' | 'comprehensive' | 'competitor';
  deliveryMethod?: 'email' | 'whatsapp' | 'download';
}

// Error types
export interface AppError extends Error {
  code: string;
  statusCode: number;
  isOperational: boolean;
  context?: Record<string, any>;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Configuration types
export interface ScrapingConfig {
  delayMin: number;
  delayMax: number;
  timeout: number;
  maxRetries: number;
  useProxy: boolean;
  userAgents: string[];
}

export interface AIConfig {
  provider: 'gemini' | 'openai' | 'perplexity';
  model: string;
  maxTokens: number;
  temperature?: number;
}

// Job queue types
export interface JobData {
  type: 'scrape' | 'analyze' | 'generate_report' | 'send_email';
  businessId: string;
  userId: string;
  reportId?: string;
  config?: any;
}

export interface JobResult {
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
  timestamp: string;
}
