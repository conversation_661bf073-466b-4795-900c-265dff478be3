import { GMBData } from './GMBScrapingService';
export interface BusinessVerificationRequest {
    businessName: string;
    address: string;
    phone?: string;
    website?: string;
    category?: string;
}
export interface VerificationResult {
    isVerified: boolean;
    confidence: number;
    sources: string[];
    issues: string[];
    verifiedData: Partial<GMBData>;
    methodology: string;
    lastVerified: Date;
}
export interface DirectoryVerification {
    directoryName: string;
    found: boolean;
    businessName?: string;
    address?: string;
    phone?: string;
    website?: string;
    rating?: number;
    reviewCount?: number;
    consistency: {
        nameMatch: boolean;
        addressMatch: boolean;
        phoneMatch: boolean;
    };
}
export declare class BusinessVerificationService {
    private perplexityApiKey;
    private googleMapsApiKey;
    constructor();
    verifyBusiness(request: BusinessVerificationRequest): Promise<VerificationResult>;
    private verifyWithPerplexity;
    private parsePerplexityVerification;
    private verifyWithGooglePlaces;
    private verifyContactInformation;
    private verifyWithDirectories;
    private calculateDirectoryConfidence;
    private calculateStringSimilarity;
    private calculateLevenshteinDistance;
    private getVerificationMethodology;
}
//# sourceMappingURL=BusinessVerificationService.d.ts.map